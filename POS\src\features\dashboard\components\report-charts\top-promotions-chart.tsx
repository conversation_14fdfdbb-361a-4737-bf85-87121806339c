import { useMemo } from 'react'

import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui'

import { PromotionsOverview } from '../promotions-overview'

export function TopPromotionsChart() {
  const formatDateRange = useMemo(() => {
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    const sevenDaysAgo = new Date(yesterday.getTime() - 6 * 24 * 60 * 60 * 1000)

    const formatDate = (date: Date) => {
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`
    }

    const fromFormatted = `${formatDate(sevenDaysAgo)} 00:00`
    const toFormatted = `${formatDate(yesterday)} 23:59`

    return `Báo cáo tính từ ${fromFormatted} - ${toFormatted}`
  }, [])

  return (
    <Card className='col-span-1 flex flex-col'>
      <CardHeader className='flex-shrink-0'>
        <CardTitle>Top 5 chương trình khuyến mại</CardTitle>
        <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
        <CardDescription className='text-xs font-medium text-black'>Doanh thu (₫)</CardDescription>
        <CardAction>
          {/* <button className='cursor-pointer text-xs text-blue-600 hover:text-blue-800'>Chi tiết</button> */}
        </CardAction>
      </CardHeader>
      <CardContent className='flex flex-1 flex-col pl-2'>
        <PromotionsOverview />
      </CardContent>
    </Card>
  )
}
