---
type: "always_apply"
---

# API Integration Standards

## API Client Structure
- **Separate API logic** from UI components in `lib/[domain]-api.ts`
- **Define clear interfaces** for API parameters and responses in `types/api/`
- **Use React Query hooks** in `hooks/api/use-[domain].ts`
- **Implement proper caching** and query invalidation
- **Follow consistent naming** patterns for API functions and hooks

## Error Handling
- **Use consistent error patterns** across API calls
- **Implement proper loading states** in hooks
- **Show user-friendly error messages** with toast notifications
- **Handle edge cases** gracefully

## Caching Strategies
- **Use React Query** for server state management
- **Implement proper cache invalidation** after mutations
- **Set appropriate stale times** based on data freshness requirements
- **Use optimistic updates** for better user experience
- **Set up key** in queryKeys