import { useEffect, useMemo, useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { useCustomizationsData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import MenuItemsProvider, { useItemsInCity } from '../context'
import { ItemsInCity } from '../data'
import {
  useItemsInCityForTable,
  useUpdateItemInCityStatus,
  useItemsInCityListState,
  useUpdateItemInCity,
  UpdateItemInCityRequest
} from '../hooks'
import {
  createColumns,
  ItemsInCityButtons,
  ItemsInCityTableSkeleton,
  ItemsInCityDialogs,
  ItemsInCityDataTable,
  CustomizationDialog,
  BuffetConfigModal
} from './components'

function ItemsInCityContent() {
  const navigate = useNavigate()
  const [currentPage, setCurrentPage] = useState<number>(1)
  const { setOpen, setCurrentRow } = useItemsInCity()
  const { updateStatusAsync } = useUpdateItemInCityStatus()
  const { updateItemAsync } = useUpdateItemInCity()
  const {
    isCustomizationDialogOpen,
    isBuffetItem,
    isBuffetConfigModalOpen,
    setIsCustomizationDialogOpen,
    setIsBuffetItem,
    selectedMenuItem,
    setSelectedMenuItem,
    setIsBuffetConfigModalOpen,
    selectedBuffetMenuItem,
    setSelectedBuffetMenuItem,
    selectedItemTypeUid,
    setSelectedItemTypeUid,
    selectedCityUid,
    setSelectedCityUid,
    selectedDaysOfWeek,
    setSelectedDaysOfWeek,
    selectedStatus,
    setSelectedStatus
  } = useItemsInCityListState()

  const filterParams = useMemo(
    () => ({
      ...(selectedItemTypeUid !== 'all' && { item_type_uid: selectedItemTypeUid }),
      ...(selectedCityUid !== 'all' && { city_uid: selectedCityUid }),
      ...(selectedDaysOfWeek.length > 0 && { time_sale_date_week: selectedDaysOfWeek.join(',') }),
      ...(selectedStatus !== 'all' && { active: parseInt(selectedStatus, 10) }),
      page: currentPage
    }),
    [selectedItemTypeUid, selectedCityUid, selectedDaysOfWeek, selectedStatus, currentPage]
  )

  useEffect(() => {
    setCurrentPage(1)
  }, [selectedItemTypeUid, selectedCityUid, selectedDaysOfWeek, selectedStatus])

  const {
    data: items = [],
    isLoading: itemsLoading,
    error: itemsError,
    hasNextPage
  } = useItemsInCityForTable({
    params: filterParams
  })

  const { data: customizations = [] } = useCustomizationsData({
    skip_limit: true,
    list_city_uid: selectedCityUid !== 'all' ? [selectedCityUid] : undefined
  })

  const handleCustomizationClick = (item: ItemsInCity) => {
    setSelectedMenuItem(item)
    setIsCustomizationDialogOpen(true)
  }

  const handleBuffetConfigClick = (item: ItemsInCity) => {
    setSelectedMenuItem(item)
    setSelectedBuffetMenuItem(item?.extra_data?.exclude_items_buffet || [])
    setIsBuffetItem(item?.extra_data?.is_buffet_item === 1)
    setIsBuffetConfigModalOpen(true)
  }

  const handleCopyClick = (item: ItemsInCity) => {
    navigate({ to: '/menu/items/items-in-city/detail', search: { id: item.id || '' } })
  }

  const handleDeleteClick = (item: ItemsInCity) => {
    setCurrentRow(item)
    setOpen('delete')
  }

  const handleRowClick = (item: ItemsInCity) => {
    navigate({ to: '/menu/items/items-in-city/detail/$id', params: { id: item.id || '' } })
  }

  const handleToggleStatus = async (item: ItemsInCity) => {
    const newStatus = item.active ? 0 : 1
    await updateStatusAsync({
      id: item.id || '',
      active: newStatus
    })
  }

  const columns = createColumns({ onBuffetConfigClick: handleBuffetConfigClick })

  if (itemsError) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>Có lỗi xảy ra khi tải dữ liệu</p>
          <p className='text-muted-foreground text-xs'>
            {itemsError && `Món ăn: ${itemsError?.message || 'Lỗi không xác định'}`}
          </p>
        </div>
      </div>
    )
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Món ăn tại thành phố</h2>
          </div>
          <ItemsInCityButtons />
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          {itemsLoading && <ItemsInCityTableSkeleton />}
          {!itemsLoading && (
            <ItemsInCityDataTable
              columns={columns}
              data={items}
              onCustomizationClick={handleCustomizationClick}
              onCopyClick={handleCopyClick}
              onToggleStatus={handleToggleStatus}
              onRowClick={handleRowClick}
              onDeleteClick={handleDeleteClick}
              customizations={customizations}
              selectedItemTypeUid={selectedItemTypeUid}
              onItemTypeChange={setSelectedItemTypeUid}
              selectedCityUid={selectedCityUid}
              onCityChange={setSelectedCityUid}
              selectedDaysOfWeek={selectedDaysOfWeek}
              onDaysOfWeekChange={setSelectedDaysOfWeek}
              selectedStatus={selectedStatus}
              onStatusChange={setSelectedStatus}
              hasNextPageOverride={hasNextPage}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
            />
          )}
        </div>
      </Main>

      <ItemsInCityDialogs />

      {isCustomizationDialogOpen && selectedMenuItem && (
        <CustomizationDialog
          open={isCustomizationDialogOpen}
          onOpenChange={setIsCustomizationDialogOpen}
          item={selectedMenuItem}
          customizations={customizations}
        />
      )}

      {isBuffetConfigModalOpen && selectedBuffetMenuItem && (
        <BuffetConfigModal
          itemsBuffet={selectedBuffetMenuItem}
          open={isBuffetConfigModalOpen}
          onOpenChange={setIsBuffetConfigModalOpen}
          onItemsChange={async (itemsBuffetIds: string[]) => {
            await updateItemAsync({
              ...selectedMenuItem,
              extra_data: {
                is_buffet_item: isBuffetItem ? 1 : 0,
                exclude_items_buffet: itemsBuffetIds
              }
            } as unknown as UpdateItemInCityRequest)
          }}
          items={items}
          hide={false}
          enable={isBuffetItem}
          onEnableChange={setIsBuffetItem}
        />
      )}
    </>
  )
}

export default function ItemsInCityPage() {
  return (
    <MenuItemsProvider>
      <ItemsInCityContent />
    </MenuItemsProvider>
  )
}
