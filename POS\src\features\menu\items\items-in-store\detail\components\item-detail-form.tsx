import { useEffect, useState } from 'react'

import { type SubmitHandler, useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { useNavigate, useRouterState } from '@tanstack/react-router'

import { Form } from '@/components/ui/form'

import { useUploadImage } from '@/features/menu/items/items-in-city/hooks/use-upload-image'

import { itemsInStoreFormSchema, type ItemsInStoreForm } from '../../data/schema'
import { type ItemInStore, useCreateItemInStore, useUpdateItemInStore, useUpdateItemInStoreStatus } from '../../hooks'
import type { CreateItemInStoreRequest, UpdateItemInStoreRequest } from '../../hooks'
import { useItemConfigurationData } from '../../hooks'
import { generateItemId } from '../../utils/utils'
import { ItemDetailHeader } from './item-detail-header'
import { ItemFormSections } from './item-form-sections'
import { PriceSourceDialog } from './price-source-dialog'

const formSchema = itemsInStoreFormSchema
export type FormValues = ItemsInStoreForm

interface Props {
  currentRow?: ItemInStore
  isCopyMode?: boolean
}

export function ItemDetailForm({ currentRow, isCopyMode = false }: Props) {
  const isUpdate = !!currentRow && !isCopyMode
  const isCopy = !!currentRow && isCopyMode

  const navigate = useNavigate()
  const [openDialog, setOpenDialog] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null)

  const { createItemAsync, isPending: isCreateLoading } = useCreateItemInStore()
  const { updateItemAsync, isPending: isUpdateLoading } = useUpdateItemInStore()
  const { updateStatusAsync, isPending: isUpdateStatusLoading } = useUpdateItemInStoreStatus()
  const { uploadImage, isUploading: isImageUploading } = useUploadImage()

  const itemData = currentRow as ItemInStore | undefined
  const { location } = useRouterState()
  const storeUidFromState = (location.state as any)?.store_uid as string | undefined

  useEffect(() => {
    if (itemData?.image_path) {
      setImagePreview(itemData.image_path as string)
    }
  }, [itemData?.image_path, isCopy])

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      item_name: (itemData?.item_name as string) || '',
      ots_price: Number(itemData?.ots_price) || 0,
      description: (itemData?.description as string) || '',
      item_id_barcode: isCopy ? '' : (itemData?.item_id_barcode as string) || '',
      is_eat_with: Number(itemData?.is_eat_with) || 0,
      item_class_uid: (itemData?.item_class_uid as string) || '',
      item_type_uid: (itemData?.item_type_uid as string) || '',
      store_uid: (itemData?.Stores?.[0]?.id as string) || '',
      city_uid: (itemData?.Stores?.[0]?.city_uid as string) || '',
      item_id: isCopy ? '' : (itemData?.item_id as string) || '',
      enable_custom_item_id: isUpdate ? (itemData?.item_id && (itemData?.item_id as string).trim() !== '' ? 1 : 0) : 0,
      unit_uid: (itemData?.unit_uid as string) || '',
      unit_secondary_uid: (itemData?.unit_secondary_uid as string) || '',
      ots_tax: Number(itemData?.ots_tax) || 0,
      time_cooking: Number(itemData?.time_cooking) || 0,
      ta_price: Number(itemData?.ta_price) || 0,
      ta_tax: Number(itemData?.ta_tax) || 0,
      item_id_mapping: (itemData?.item_id_mapping as string) || '',
      enable_edit_price: Number(itemData?.extra_data?.enable_edit_price) || 0,
      is_print_label: Number(itemData?.is_print_label) || 0,
      is_allow_discount: Number(itemData?.is_allow_discount) || 0,
      is_virtual_item: Number(itemData?.extra_data?.is_virtual_item) || 0,
      is_item_service: Number(itemData?.extra_data?.is_item_service) || 0,
      is_buffet_item: Number(itemData?.extra_data?.is_buffet_item) || 0,
      no_update_quantity_toping: Number(itemData?.extra_data?.no_update_quantity_toping) || 0,
      formula_qrcode: (itemData?.extra_data?.formula_qrcode as string) || '',
      is_service: Number(itemData?.is_service) || 0,
      price_by_source: (itemData?.extra_data?.price_by_source as any) || [],
      exclude_items_buffet: (itemData?.extra_data?.exclude_items_buffet as any) || [],
      up_size_buffet: (itemData?.extra_data?.up_size_buffet as any) || [],
      cross_price: (itemData?.extra_data?.cross_price as any) || [],
      quantity: (itemData?.extra_data?.cross_price as any)?.[0]?.quantity || 0,
      price: (itemData?.extra_data?.cross_price as any)?.[0]?.price || 0,
      time_sale_date_week: Number(itemData?.time_sale_date_week) || 0,
      time_sale_hour_day: Number(itemData?.time_sale_hour_day) || 0,
      sort: Number(itemData?.sort) || 0,
      customization_uid: (itemData?.customization_uid as string) || null,
      apply_with_store: Number((itemData as any)?.apply_with_store) || 2,
      item_color: (itemData?.item_color as string) || ''
    }
  })

  const { company, selectedBrand, sourcesData, itemTypes, units, itemClasses, stores } = useItemConfigurationData({
    form
  })

  useEffect(() => {
    if (isUpdate || !storeUidFromState) return
    const current = form.getValues('store_uid')
    if (!current || String(current).trim() === '') {
      form.setValue('store_uid', storeUidFromState, { shouldDirty: true, shouldValidate: true })
    }
  }, [isUpdate, storeUidFromState])

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedImageFile(file)
      const reader = new FileReader()
      reader.onload = e => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const onSubmit: SubmitHandler<any> = async data => {
    if (!company?.id || !selectedBrand?.id) return

    try {
      let imagePath = ''
      let imagePathThumb = ''
      let itemColor = data.item_color || ''

      if (selectedImageFile) {
        const uploadResult = await uploadImage(selectedImageFile)
        if (uploadResult) {
          imagePath = uploadResult.image_path
          imagePathThumb = uploadResult.image_path_thumb
          itemColor = ''
        }
      } else if (imagePreview && !isCopy) {
        imagePath = itemData?.image_path || ''
        imagePathThumb = itemData?.image_path_thumb || ''
      }

      const extraDataPayload = {
        price_by_source: (data.price_by_source || []).map((s: any) => ({
          price: Number(s.price ?? s.amount ?? 0),
          source_id: String(s.source_id ?? s.source ?? s.sourceId ?? ''),
          price_times: Array.isArray(s.price_times) ? s.price_times : [],
          is_source_exist_in_city: s.is_source_exist_in_city ?? true
        })),
        is_virtual_item: data.is_virtual_item,
        is_item_service: data.is_item_service,
        no_update_quantity_toping: data.no_update_quantity_toping,
        enable_edit_price: data.enable_edit_price,
        is_buffet_item: data.is_buffet_item,
        exclude_items_buffet: data.exclude_items_buffet || [],
        up_size_buffet: data.up_size_buffet || [],
        cross_price: data.cross_price || [],
        formula_qrcode: data.formula_qrcode || ''
      }

      const itemId = isUpdate
        ? data.enable_custom_item_id && data.item_id
          ? data.item_id
          : (itemData?.item_id as string) || (currentRow as any)?.item_id || ''
        : isCopy
          ? data.enable_custom_item_id && data.item_id && data.item_id.trim() !== ''
            ? data.item_id
            : generateItemId()
          : data.enable_custom_item_id && data.item_id
            ? data.item_id
            : generateItemId()

      const selectedStore = (stores || []).find(
        (s: any) => String(s.id) === String(data.store_uid) || String(s.store_uid) === String(data.store_uid)
      )

      const commonData = {
        item_id: itemId,
        item_name: data.item_name,
        description: data.description || '',
        ots_price: data.ots_price,
        ots_tax: data.ots_tax,
        ta_price: data.ta_price,
        ta_tax: data.ta_tax,
        time_sale_hour_day: data.time_sale_hour_day,
        time_sale_date_week: data.time_sale_date_week,
        allow_take_away: data.allow_take_away,
        is_eat_with: data.is_eat_with ? 1 : 0,
        image_path: imagePath,
        image_path_thumb: imagePathThumb,
        item_color: itemColor,
        list_order: data.list_order,
        is_service: data.is_service ? 1 : 0,
        is_material: data.is_material,
        active: isUpdate ? 1 : 1,
        user_id: (itemData?.user_id as string) || '',
        is_foreign: 0,
        quantity_default: data.quantity_default,
        price_change: data.price_change,
        currency_type_id: (itemData?.currency_type_id as string) || '',
        point: data.point,
        is_gift: data.is_gift,
        is_fc: data.is_fc,
        show_on_web: data.show_on_web,
        show_price_on_web: data.show_price_on_web,
        cost_price: data.cost_price,
        is_print_label: data.is_print_label ? 1 : 0,
        quantity_limit: data.quantity_limit,
        is_kit: data.is_kit,
        process_index: data.process_index,
        quantity_per_day: data.quantity_per_day,
        is_parent: data.is_parent,
        is_sub: data.is_sub,
        effective_date: data.effective_date,
        expire_date: data.expire_date,
        time_cooking: data.time_cooking,
        item_id_barcode: data.item_id_barcode || '',
        is_allow_discount: data.is_allow_discount ? 1 : 0,
        item_id_eat_with: (itemData?.item_id_eat_with as string) || '',
        item_id_mapping: data.item_id_mapping || '',
        unit_uid: data.unit_uid,
        unit_secondary_uid: data.unit_secondary_uid || null,
        item_class_uid: data.item_class_uid || null,
        item_type_uid: data.item_type_uid,
        city_uid: selectedStore?.city_uid || '',
        store_uid: data.store_uid || '',
        sort: data.sort,
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        sort_online: data.sort_online,
        customization_uid: data.customization_uid || null,
        is_fabi: 1,
        apply_with_store: data.apply_with_store,
        revision: 0,
        extra_data: extraDataPayload
      }

      const baseData = {
        ...(isUpdate ? { id: currentRow?.id || '' } : {}),
        ...commonData
      }

      if (isUpdate && currentRow?.id) {
        await updateItemAsync(baseData as unknown as UpdateItemInStoreRequest)
        navigate({ to: '/menu/items/items-in-store' })
      } else {
        await createItemAsync(baseData as unknown as CreateItemInStoreRequest)
        navigate({ to: '/menu/items/items-in-store' })
      }
    } catch (error) {
      console.error('Error submitting form:', error)
    }
  }

  const handleDeactive = async () => {
    if (!currentRow?.id || !company?.id || !selectedBrand?.id) return

    try {
      await updateStatusAsync({
        id: currentRow.id,
        active: 0
      })
      navigate({ to: '/menu/items/items-in-store' })
    } catch (error) {}
  }

  const handleActive = async () => {
    if (!currentRow?.id || !company?.id || !selectedBrand?.id) return

    try {
      await updateStatusAsync({
        id: currentRow.id,
        active: 1
      })
      navigate({ to: '/menu/items/items-in-store' })
    } catch (error) {}
  }

  const handleSave = async () => {
    if (!isUpdate) {
      const enableCustom = form.getValues('enable_custom_item_id')
      const currentItemId = form.getValues('item_id')
      if (!enableCustom && (!currentItemId || String(currentItemId).trim() === '')) {
        form.setValue('item_id', generateItemId(), { shouldDirty: true })
      }
    }

    const isValid = await form.trigger()

    if (isValid) {
      form.handleSubmit(onSubmit)()
    }
  }

  const handlePriceSourceConfirm = (data: {
    price: number
    source_id?: string | undefined
    source_name?: string | undefined
    price_times?: number[] | undefined
    is_source_exist_in_city?: boolean | undefined
  }) => {
    const current = form.getValues('price_by_source') || []
    const next = [
      ...current,
      {
        source_id: data.source_id,
        price: data.price,
        source_name: data.source_name,
        price_times: Array.isArray(data.price_times) ? data.price_times : [],
        is_source_exist_in_city: data.is_source_exist_in_city ?? true
      }
    ]
    form.setValue('price_by_source', next, { shouldDirty: true, shouldValidate: true })
    setOpenDialog(false)
  }

  const isLoading = isCreateLoading || isUpdateLoading || isImageUploading

  return (
    <>
      <div className='container mx-auto px-4 py-8'>
        <ItemDetailHeader
          isUpdate={isUpdate}
          isCopy={isCopy}
          currentRow={currentRow}
          isLoading={isLoading}
          onSave={handleSave}
          onDeactive={isUpdate ? handleDeactive : undefined}
          onActive={isUpdate ? handleActive : undefined}
          isDeactivating={isUpdateStatusLoading}
          isActivating={isUpdateStatusLoading}
        />

        <div className='mx-auto max-w-4xl'>
          <div className='bg-white'>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
                <ItemFormSections
                  form={form}
                  mode={isUpdate ? 'update' : 'create'}
                  itemTypes={itemTypes}
                  itemClasses={itemClasses}
                  units={units}
                  stores={stores}
                  imageFile={selectedImageFile}
                  onImageChange={handleImageChange}
                  imagePreview={imagePreview}
                  onImageRemove={() => {
                    setImagePreview(null)
                    setSelectedImageFile(null)
                  }}
                />
              </form>
            </Form>
          </div>
        </div>
      </div>

      <PriceSourceDialog
        open={openDialog}
        onOpenChange={setOpenDialog}
        onConfirm={handlePriceSourceConfirm}
        sources={sourcesData}
      />
    </>
  )
}
