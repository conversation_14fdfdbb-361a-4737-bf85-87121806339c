import { Header } from '@/components/layout/header'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { SystemLogFilters } from './components/system-log-filters'
import { SystemLogTable } from './components/system-log-table'
import { useSystemLog } from './hooks'

export default function SystemLogPage() {
  const {
    logs,
    isLoading,
    error,
    filters,
    updateFilter,
    updateDateRange,
    searchLogs,
    availableRequestPaths,
    availableUsers,
    formatTimestamp,
    totalCount
  } = useSystemLog()

  if (error) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='py-8 text-center'>
          <p className='text-red-600'>{error}</p>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Header */}
      <Header>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>
      <div className='container mx-auto px-4 py-8'>
        <div className='mb-6'>
          <SystemLogFilters
            filters={filters}
            onFilterChange={updateFilter}
            onDateRangeChange={updateDateRange}
            onSearch={searchLogs}
            availableRequestPaths={availableRequestPaths}
            availableUsers={availableUsers}
            isLoading={isLoading}
          />
        </div>

        <div className='mb-4'>
          <p className='text-sm text-gray-600'>Tổng {totalCount} kết quả</p>
        </div>

        <SystemLogTable logs={logs} isLoading={isLoading} formatTimestamp={formatTimestamp} />
      </div>
    </>
  )
}
