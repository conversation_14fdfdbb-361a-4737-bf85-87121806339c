import { UseFormReturn } from 'react-hook-form'

import { useRouterState } from '@tanstack/react-router'

import { useAuthStore } from '@/stores'

import { useCurrentBrand, usePosStores } from '@/stores/posStore'

import {
  useCitiesData,
  useCustomizationsData,
  useCustomizationById,
  useItemsData,
  useSourcesData,
  useItemTypesData,
  useUnitsData,
  useItemClassesData
} from '@/hooks/api'

import type { FormValues } from '../detail/components/item-detail-form'

interface UseItemConfigurationDataProps {
  form?: UseFormReturn<FormValues>
}

export function useItemConfigurationData({ form }: UseItemConfigurationDataProps = {}) {
  const selectedCustomizationUid = form?.watch('customization_uid')

  const { location } = useRouterState()
  const selectedStoreUid = (location.state as any)?.store_uid || form?.watch('store_uid')

  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()
  const { stores } = usePosStores()

  const { data: sourcesData = [] } = useSourcesData({
    company_uid: company?.id,
    brand_uid: selectedBrand?.id,
    skip_limit: true,
    enabled: !!company?.id && !!selectedBrand?.id
  })
  const { data: customizations = [] } = useCustomizationsData({
    skip_limit: true,
    store_uid: selectedStoreUid,
    enabled: !!selectedStoreUid
  })
  const { data: customizationDetails } = useCustomizationById(
    selectedCustomizationUid || '',
    !!selectedCustomizationUid && selectedCustomizationUid !== 'none'
  )
  const { data: items = [] } = useItemsData({
    params: {
      store_uid: selectedStoreUid,
      skip_limit: true
    },
    enabled: !!selectedStoreUid
  })

  const { data: cities = [] } = useCitiesData()
  const { data: itemTypes = [] } = useItemTypesData({ store_uid: selectedStoreUid, skip_limit: true })
  const { data: units = [] } = useUnitsData()
  const { data: itemClasses = [] } = useItemClassesData({ skip_limit: true })

  return {
    selectedStoreUid,
    selectedCustomizationUid,

    company,
    selectedBrand,

    customizations,
    customizationDetails,
    items,
    sourcesData,
    cities,
    stores,
    itemTypes,
    units,
    itemClasses
  }
}
