import { z } from 'zod'

export const itemsInCitySchema = z.object({
  id: z.string().optional(),
  item_id: z.string().optional(),
  item_name: z.string().optional(),
  description: z.string().optional(),
  ots_price: z.coerce.number().optional(),
  ots_tax: z.coerce.number().optional(),
  ta_price: z.coerce.number().optional(),
  ta_tax: z.coerce.number().optional(),
  time_sale_hour_day: z.coerce.number().optional(),
  time_sale_date_week: z.coerce.number().optional(),
  allow_take_away: z.coerce.number().optional(),
  is_eat_with: z.coerce.number().optional(),
  image_path: z.string().optional(),
  image_path_thumb: z.string().optional(),
  item_color: z.string().optional(),
  list_order: z.coerce.number().optional(),
  is_service: z.coerce.number().optional(),
  is_material: z.coerce.number().optional(),
  active: z.coerce.number().optional(),
  user_id: z.string().optional(),
  is_foreign: z.coerce.number().optional(),
  quantity_default: z.coerce.number().optional(),
  price_change: z.coerce.number().optional(),
  currency_type_id: z.string().optional(),
  point: z.coerce.number().optional(),
  is_gift: z.coerce.number().optional(),
  is_fc: z.coerce.number().optional(),
  show_on_web: z.coerce.number().optional(),
  show_price_on_web: z.coerce.number().optional(),
  cost_price: z.coerce.number().optional(),
  is_print_label: z.coerce.number().optional(),
  quantity_limit: z.coerce.number().optional(),
  is_kit: z.coerce.number().optional(),
  time_cooking: z.coerce.number().optional(),
  item_id_barcode: z.string().optional(),
  process_index: z.coerce.number().optional(),
  is_allow_discount: z.coerce.number().optional(),
  quantity_per_day: z.coerce.number().optional(),
  item_id_eat_with: z.string().optional(),
  is_parent: z.coerce.number().optional(),
  is_sub: z.coerce.number().optional(),
  item_id_mapping: z.string().optional(),
  effective_date: z.coerce.number().optional(),
  expire_date: z.coerce.number().optional(),
  sort: z.coerce.number().optional(),
  sort_online: z.coerce.number().optional(),
  revision: z.coerce.number().optional(),
  unit_uid: z.string().optional(),
  unit_secondary_uid: z.string().nullable().optional(),
  item_type_uid: z.string().optional(),
  item_class_uid: z.string().optional(),
  source_uid: z.string().nullable().optional(),
  brand_uid: z.string().optional(),
  city_uid: z.string().optional(),
  company_uid: z.string().optional(),
  customization_uid: z.string().nullable().optional(),
  is_fabi: z.coerce.number().optional(),
  deleted: z.boolean().optional(),
  created_by: z.string().optional(),
  updated_by: z.string().optional(),
  deleted_by: z.string().nullable().optional(),
  created_at: z.coerce.number().optional(),
  updated_at: z.coerce.number().optional(),
  deleted_at: z.coerce.number().nullable().optional(),
  status_trigger_disabled: z.boolean().optional(),
  is_featured: z.coerce.number().optional(),
  extra_data: z
    .object({
      cross_price: z
        .array(
          z.object({
            price: z.coerce.number().optional(),
            quantity: z.coerce.number().optional()
          })
        )
        .optional(),
      formula_qrcode: z.string().optional(),
      is_buffet_item: z.coerce.number().optional(),
      up_size_buffet: z.array(z.any()).optional(),
      is_item_service: z.coerce.number().optional(),
      is_virtual_item: z.coerce.number().optional(),
      price_by_source: z
        .array(
          z.object({
            price: z.coerce.number().optional(),
            source_id: z.string().optional(),
            price_times: z.array(z.any()).optional(),
            is_source_exist_in_city: z.boolean().optional()
          })
        )
        .optional(),
      enable_edit_price: z.coerce.number().optional(),
      exclude_items_buffet: z.array(z.string()).optional(),
      no_update_quantity_toping: z.coerce.number().optional()
    })
    .optional(),
  enable_custom_item_id: z.coerce.number().optional(),
  cities: z
    .array(
      z.object({
        id: z.string().optional(),
        city_id: z.string().optional(),
        fb_city_id: z.string().optional(),
        city_name: z.string().optional(),
        image_path: z.string().nullable().optional(),
        description: z.string().optional(),
        active: z.coerce.number().optional(),
        extra_data: z.any().nullable().optional(),
        revision: z.coerce.number().optional(),
        sort: z.coerce.number().optional(),
        created_by: z.string().nullable().optional(),
        updated_by: z.string().nullable().optional(),
        deleted_by: z.string().nullable().optional(),
        created_at: z.coerce.number().optional(),
        updated_at: z.coerce.number().optional(),
        deleted_at: z.coerce.number().nullable().optional(),
        items_cities: z
          .object({
            item_uid: z.string().optional(),
            city_uid: z.string().optional()
          })
          .optional()
      })
    )
    .optional()
})

export type ItemsInCity = z.infer<typeof itemsInCitySchema>
export type ItemInCityFormValues = ItemsInCity

export const itemsInCityFormSchema = z.object({
  id: z.string().optional(),
  item_id: z.string().optional(),
  item_name: z.string().min(1, 'Tên món là bắt buộc'),
  description: z.string().optional(),
  ots_price: z.coerce.number().min(0).optional(),
  ots_tax: z.coerce.number().min(0).max(1).optional(),
  ta_price: z.coerce.number().min(0).optional(),
  ta_tax: z.coerce.number().min(0).max(1).optional(),
  time_cooking: z.coerce.number().optional(),
  time_sale_hour_day: z.coerce.number().optional(),
  time_sale_date_week: z.coerce.number().optional(),
  allow_take_away: z.coerce.number().optional(),
  is_eat_with: z.coerce.number().optional(),
  image_path: z.string().url().optional(),
  image_path_thumb: z.string().url().optional(),
  item_color: z.string().optional(),
  list_order: z.coerce.number().optional(),
  is_service: z.coerce.number().optional(),
  is_material: z.coerce.number().optional(),
  is_print_label: z.coerce.number().optional(),
  is_allow_discount: z.coerce.number().optional(),
  item_id_barcode: z.string().optional(),
  process_index: z.coerce.number().optional(),
  quantity_per_day: z.coerce.number().optional(),
  item_id_eat_with: z.string().optional(),
  is_parent: z.coerce.number().optional(),
  is_sub: z.coerce.number().optional(),
  item_id_mapping: z.string().optional(),
  effective_date: z.coerce.number().optional(),
  expire_date: z.coerce.number().optional(),
  sort: z.coerce.number().optional(),
  sort_online: z.coerce.number().optional(),
  unit_uid: z.string().min(1, 'Đơn vị là bắt buộc').optional(),
  unit_secondary_uid: z.string().nullable().optional(),
  item_type_uid: z.string().min(1, 'Loại món là bắt buộc'),
  item_class_uid: z.string().optional(),
  city_uid: z.string().min(1, 'Thành phố là bắt buộc'),
  customization_uid: z.string().nullable().optional(),
  is_featured: z.coerce.number().optional(),
  cross_price: z
    .array(
      z.object({
        price: z.coerce.number().min(0).optional(),
        quantity: z.coerce.number().min(0).optional()
      })
    )
    .optional(),
  formula_qrcode: z.string().optional(),
  is_buffet_item: z.coerce.number().optional(),
  up_size_buffet: z.array(z.any()).optional(),
  is_item_service: z.coerce.number().optional(),
  is_virtual_item: z.coerce.number().optional(),
  price_by_source: z
    .array(
      z.object({
        price: z.coerce.number().min(0).optional(),
        source_id: z.string().optional(),
        price_times: z.array(z.any()).optional(),
        is_source_exist_in_city: z.boolean().optional()
      })
    )
    .optional(),
  price_times: z.array(z.any()).optional(),
  enable_edit_price: z.coerce.number().optional(),
  exclude_items_buffet: z.array(z.string()).optional(),
  no_update_quantity_toping: z.coerce.number().optional(),
  quantity: z.coerce.number().optional(),
  price: z.coerce.number().optional(),
  enable_custom_item_id: z.coerce.number().optional()
})

export type ItemsInCityForm = z.infer<typeof itemsInCityFormSchema>
