import { useState } from 'react'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { combosApi } from '@/lib/combos-api'

import { useStoresData } from '@/hooks/api/use-stores'
import { usePromotionsData } from '@/hooks/api/use-promotions'

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui'
import { ApiPromotion } from '@/lib/promotion-api'

interface ComboImportData {
  id: string
  name: string
  store_name: string
  price: number
  promotion_name: string
  combo_code: string
  vat_percent: number
  start_date: string
  end_date: string
  apply_date: string
  apply_time: string
  image: string
  group_name: string
  require_selection: string
  selection_limit: number
  group_item_codes: string
  item_price: number
}

const convertTimeToHours = (timeValue: string | number): string => {
  if (!timeValue) return ''

  const num = typeof timeValue === 'string' ? parseInt(timeValue) : timeValue
  if (isNaN(num)) return ''

  const hours: string[] = []

  for (let i = 0; i < 24; i++) {
    if (num & (1 << i)) {
      hours.push(`${i}h`)
    }
  }

  return hours.join(', ')
}

const convertDateToDays = (dateValue: string | number): string => {
  if (!dateValue) return ''

  const num = typeof dateValue === 'string' ? parseInt(dateValue) : dateValue
  if (isNaN(num)) return ''

  const dayMapping = [
    { value: 2, name: 'Chủ nhật' },
    { value: 4, name: 'Thứ 2' },
    { value: 8, name: 'Thứ 3' },
    { value: 16, name: 'Thứ 4' },
    { value: 32, name: 'Thứ 5' },
    { value: 64, name: 'Thứ 6' },
    { value: 128, name: 'Thứ 7' }
  ]

  const days: string[] = []

  dayMapping.forEach(day => {
    if (num & day.value) {
      days.push(day.name)
    }
  })

  return days.join(', ')
}

interface ComboExportPreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: ComboImportData[]
  onConfirm: () => void
  isLoading?: boolean
  title?: string
}

export function ComboExportPreviewDialog({
  open,
  onOpenChange,
  data,
  onConfirm,
  isLoading = false,
  title = 'Xuất, sửa combo'
}: ComboExportPreviewDialogProps) {
  const { company, brands } = useAuthStore(state => state.auth)
  const { data: stores = [] } = useStoresData()
  const { apiPromotions: promotions = [] } = usePromotionsData({ enabled: true })
  const [isProcessing, setIsProcessing] = useState(false)

  const handleClose = () => {
    onOpenChange(false)
  }

  const handleConfirm = async () => {
    setIsProcessing(true)

    try {
      if (!data || data.length === 0) {
        toast.error('Không có dữ liệu để cập nhật')
        return
      }

      if (!company?.id || !brands?.[0]?.id) {
        toast.error('Thiếu thông tin công ty hoặc thương hiệu')
        return
      }

      const transformedCombos = data.map((row: ComboImportData, index: number) => {
          const generatePackageId = (): string => {
            if (row.combo_code && row.combo_code.trim() !== '') {
              return row.combo_code.trim()
            }
            const baseName = row.name ? row.name.replace(/\s+/g, '-').toUpperCase() : 'COMBO'
            return `${baseName}-${Date.now()}-${index}`
          }

          const getStoreUid = (): string => {
            if (!row.store_name) {
              return stores[0]?.id || ''
            }
            const store = stores.find(s => s.name === row.store_name)
            if (store) {
              return store.id
            }
            return stores[0]?.id || ''
          }

          const storeUid = getStoreUid()

          const getPromotionUid = (
            promotionName: string | null | undefined,
            targetStoreUid: string
          ): string | null => {
            if (!promotionName) return null
            const name = String(promotionName).trim()
            const group = promotions.find((g: ApiPromotion) => String(g.promotion_name).trim() === name)
            if (!group) return null
            const details = (group.promotions || []) as Array<{ store_uid: string; promotion_uid: string }>
            const match = details.find(p => p.store_uid === targetStoreUid)
            return match?.promotion_uid || null
          }

          const promotionUid = getPromotionUid(row.promotion_name, storeUid)

          return {
            company_uid: company?.id || '',
            brand_uid: brands?.[0]?.id || '',
            package_name: row.name,
            package_id: generatePackageId(),
            image_path: row.image || null,
            image_path_thumb: row.image || null,
            vat_tax_rate: Number(row.vat_percent || 0) / 100,
            ots_value: Number(row.price || 0),
            ta_value: Number(row.price || 0),
            from_date: convertDateToTimestamp(row.start_date),
            to_date: convertDateToTimestamp(row.end_date),
            time_sale_date_week: convertDaysToNumber(row.apply_date),
            time_sale_hour_day: convertHoursToNumber(row.apply_time),
            use_same_data: 0,
            deleted: false,
            package_detail: {
              LstItem_Options: [
                {
                  Name: row.group_name,
                  Min_Permitted: Number(row.require_selection) || 0,
                  Max_Permitted: Number(row.selection_limit) || 0,
                  LstItem: row.group_item_codes
                    ? [
                        {
                          item_id: row.group_item_codes,
                          ots_price: row.item_price || null,
                          ta_price: row.item_price || null,
                          item_name: row.name,
                          state_change_price: 1
                        }
                      ]
                    : []
                }
              ]
            },
            ...(promotionUid ? { promotion_uid: promotionUid } : {}),
            store_uid: storeUid
          }
        })

      await combosApi.updateCombos(transformedCombos)
      toast.success('Cập nhật combo thành công!')
      onConfirm()
    } catch (error) {
      toast.error('Có lỗi xảy ra khi cập nhật combo')
    } finally {
      setIsProcessing(false)
    }
  }

  const convertDateToTimestamp = (dateValue: string | number): number => {
    if (!dateValue) return 0

    if (typeof dateValue === 'number') return dateValue

    const dateString = String(dateValue)
    try {
      const [day, month, year] = dateString.split('-')
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
      return date.getTime()
    } catch {
      return 0
    }
  }

  const convertDaysToNumber = (dayValue: string | number): number => {
    if (!dayValue) return 0

    if (typeof dayValue === 'number') return dayValue

    const dayString = String(dayValue)

    const dayMapping: { [key: string]: number } = {
      'Chủ nhật': 2,
      'Thứ 2': 4,
      'Thứ 3': 8,
      'Thứ 4': 16,
      'Thứ 5': 32,
      'Thứ 6': 64,
      'Thứ 7': 128
    }

    let result = 0
    dayString.split(', ').forEach(day => {
      if (dayMapping[day.trim()]) {
        result += dayMapping[day.trim()]
      }
    })

    return result
  }

  const convertHoursToNumber = (hourValue: string | number): number => {
    if (!hourValue) return 0

    if (typeof hourValue === 'number') return hourValue

    const hourString = String(hourValue)

    let result = 0
    hourString.split(', ').forEach(hour => {
      const hourNum = parseInt(hour.replace('h', ''))
      if (!isNaN(hourNum) && hourNum >= 0 && hourNum <= 23) {
        result += 1 << hourNum
      }
    })

    return result
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='flex h-[90vh] w-[95vw] max-w-6xl flex-col lg:max-w-7xl'>
        <DialogHeader className='flex-shrink-0'>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className='flex min-h-0 flex-1 flex-col'>
          <div className='flex-1 overflow-auto'>
            <div className='min-w-max'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className='bg-background sticky left-0 w-[120px]'>Tên</TableHead>
                    <TableHead className='w-[200px]'>Cửa hàng</TableHead>
                    <TableHead className='w-[80px]'>Giá</TableHead>
                    <TableHead className='w-[150px]'>CTKM</TableHead>
                    <TableHead className='w-[120px]'>Mã Combo</TableHead>
                    <TableHead className='w-[80px]'>VAT (%)</TableHead>
                    <TableHead className='w-[120px]'>Ngày bắt đầu</TableHead>
                    <TableHead className='w-[120px]'>Ngày kết thúc</TableHead>
                    <TableHead className='w-[120px]'>Ngày áp dụng</TableHead>
                    <TableHead className='w-[120px]'>Giờ áp dụng</TableHead>
                    <TableHead className='w-[80px]'>Ảnh</TableHead>
                    <TableHead className='w-[120px]'>Tên nhóm</TableHead>
                    <TableHead className='w-[120px]'>Yêu cầu chọn</TableHead>
                    <TableHead className='w-[120px]'>Giới hạn chọn</TableHead>
                    <TableHead className='w-[150px]'>Mã món theo nhóm</TableHead>
                    <TableHead className='w-[120px]'>Giá món con</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.map((row, index) => (
                    <TableRow key={index}>
                      <TableCell className='bg-background sticky left-0 font-medium'>{row.name}</TableCell>
                      <TableCell className='max-w-[200px] truncate' title={row.store_name}>
                        {row.store_name}
                      </TableCell>
                      <TableCell>{row.price?.toLocaleString()}</TableCell>
                      <TableCell className='max-w-[150px] truncate' title={row.promotion_name}>
                        {row.promotion_name}
                      </TableCell>
                      <TableCell>{row.combo_code}</TableCell>
                      <TableCell>{row.vat_percent}</TableCell>
                      <TableCell>{row.start_date}</TableCell>
                      <TableCell>{row.end_date}</TableCell>
                      <TableCell className='max-w-[120px] truncate' title={convertDateToDays(row.apply_date)}>
                        {convertDateToDays(row.apply_date)}
                      </TableCell>
                      <TableCell className='max-w-[120px] truncate' title={convertTimeToHours(row.apply_time)}>
                        {convertTimeToHours(row.apply_time)}
                      </TableCell>
                      <TableCell>
                        {row.image && <img src={row.image} alt='combo' className='h-8 w-8 rounded object-cover' />}
                      </TableCell>
                      <TableCell>{row.group_name}</TableCell>
                      <TableCell>{row.require_selection}</TableCell>
                      <TableCell>{row.selection_limit}</TableCell>
                      <TableCell className='max-w-[150px] truncate' title={row.group_item_codes}>
                        {row.group_item_codes}
                      </TableCell>
                      <TableCell>{row.item_price?.toLocaleString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>

        <div className='mt-4 flex flex-shrink-0 items-center justify-between border-t pt-4'>
          <Button variant='outline' onClick={handleClose}>
            Đóng
          </Button>
          <Button onClick={handleConfirm} disabled={isLoading || isProcessing}>
            {isLoading || isProcessing ? (
              <>
                <div className='mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white' />
                Đang xử lý...
              </>
            ) : (
              'Lưu'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
