import { IconEdit } from '@tabler/icons-react'

import { Button } from '@/components/ui'

import { useExtraPointContext } from '../context'

export function ExtraPointTable() {
  const { data, isLoading, toggleActivation, setEditDialogOpen, setSelectedExtraPoint } = useExtraPointContext()

  // Helper function to decode time_date_week back to day names
  const decodeDaysOfWeek = (time_date_week: number) => {
    const dayMapping = [
      { value: 2, name: 'Chủ nhật' }, // CN = 2^1 = 2
      { value: 4, name: 'Thứ 2' }, // T2 = 2^2 = 4
      { value: 8, name: 'Thứ 3' }, // T3 = 2^3 = 8
      { value: 16, name: 'Thứ 4' }, // T4 = 2^4 = 16
      { value: 32, name: 'Thứ 5' }, // T5 = 2^5 = 32
      { value: 64, name: 'Thứ 6' }, // T6 = 2^6 = 64
      { value: 128, name: 'Thứ 7' } // T7 = 2^7 = 128
    ]

    return dayMapping.filter(day => (time_date_week & day.value) === day.value).map(day => day.name)
  }

  // Helper function to decode time_hour_day back to hour ranges
  const decodeHoursOfDay = (time_hour_day: number) => {
    const hours = []
    for (let i = 0; i < 24; i++) {
      if ((time_hour_day & Math.pow(2, i)) === Math.pow(2, i)) {
        const nextHour = i + 1
        hours.push(nextHour === 24 ? `${i}h-24h` : `${i}-${nextHour}h`)
      }
    }
    return hours
  }

  // Function to get time frame display text
  const getTimeFrameDisplay = (item: any) => {
    if (item.extra_rate_type === 'SPECIAL_TIME') {
      // Check if it's "all time" values
      if (item.time_hour_day === 16777215 && item.time_date_week === 254) {
        return 'Tất cả khung giờ'
      }

      // Decode the binary values
      const selectedDays = decodeDaysOfWeek(item.time_date_week)
      const selectedHours = decodeHoursOfDay(item.time_hour_day)

      if (selectedDays.length === 0 && selectedHours.length === 0) {
        return 'Chưa chọn khung thời gian'
      }

      const dayText = selectedDays.length > 0 ? `Ngày trong tuần: ${selectedDays.join(', ')}` : ''
      const hourText = selectedHours.length > 0 ? `Khung giờ: ${selectedHours.join(', ')}` : ''

      return [dayText, hourText].filter(Boolean).join('\n')
    }

    if (item.extra_rate_type === 'BIRTHDAY') {
      const beforeDays = item.day_before_birthday || 0
      const afterDays = item.day_after_birthday || 0

      if (beforeDays === 0 && afterDays === 0) {
        return 'Sinh nhật: đúng ngày sinh nhật'
      }

      return `Sinh nhật: từ trước sinh nhật ${beforeDays} ngày, đến sau sinh nhật ${afterDays} ngày`
    }

    return ''
  }

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate).toLocaleDateString('vi-VN')
    const end = new Date(endDate).toLocaleDateString('vi-VN')
    return `${start} - ${end}`
  }

  const formatCalculatedRate = (calculatedRate: number) => {
    return `${calculatedRate.toLocaleString('vi-VN', {
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    })}%`
  }

  const getStatusInfo = (startDate: string, endDate: string, active: number) => {
    if (active === 0) {
      return {
        text: 'Đã hủy',
        className: 'bg-red-100 text-red-800'
      }
    }

    const now = new Date()
    const start = new Date(startDate)
    const end = new Date(endDate)

    if (now < start) {
      return {
        text: 'Sắp đến',
        className: 'bg-yellow-100 text-yellow-800'
      }
    }

    if (now >= start && now <= end) {
      return {
        text: 'Đang hoạt động',
        className: 'bg-green-100 text-green-800'
      }
    }

    return {
      text: 'Hết hạn',
      className: 'bg-gray-100 text-gray-800'
    }
  }

  const handleEdit = (item: any) => {
    setSelectedExtraPoint(item)
    setEditDialogOpen(true)
  }

  if (isLoading) {
    return (
      <div className='flex items-center justify-center py-8'>
        <div className='text-muted-foreground'>Đang tải...</div>
      </div>
    )
  }

  return (
    <div className='overflow-x-auto'>
      <table className='w-full border-collapse'>
        <thead>
          <tr className='border-b'>
            <th className='px-4 py-3 text-left font-medium'>Khoảng thời gian</th>
            <th className='px-4 py-3 text-left font-medium'>Hạng thành viên</th>
            <th className='px-4 py-3 text-left font-medium'>Thay đổi tỷ lệ tích điểm</th>
            <th className='px-4 py-3 text-left font-medium'>Trạng thái</th>
            <th className='w-16'></th>
          </tr>
        </thead>
        <tbody>
          {data.map(item => (
            <tr key={item.id} className='hover:bg-muted/50 border-b'>
              <td className='px-4 py-3'>{formatDateRange(item.start_date, item.end_date)}</td>
              <td className='px-4 py-3'>{item.membershipTypeName}</td>
              <td className='px-4 py-3'>
                <div className='space-y-1'>
                  <div className='font-medium'>{formatCalculatedRate(item.calculatedRate)}</div>
                  <div className='text-xs whitespace-pre-line text-gray-600'>{getTimeFrameDisplay(item)}</div>
                </div>
              </td>
              <td className='px-4 py-3'>
                <div className='flex items-center gap-2'>
                  {(() => {
                    const statusInfo = getStatusInfo(item.start_date, item.end_date, item.active)
                    return (
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${statusInfo.className}`}
                      >
                        {statusInfo.text}
                      </span>
                    )
                  })()}
                  {(() => {
                    const statusInfo = getStatusInfo(item.start_date, item.end_date, item.active)
                    const isExpired = statusInfo.text === 'Hết hạn'

                    return (
                      <button
                        onClick={() => {
                          if (isExpired) {
                            handleEdit(item)
                          } else {
                            toggleActivation(item.id, item.active)
                          }
                        }}
                        className='cursor-pointer text-xs text-blue-600 hover:text-blue-800 hover:underline'
                      >
                        {isExpired ? 'Gia hạn' : item.active === 1 ? 'Hủy' : 'Kích hoạt'}
                      </button>
                    )
                  })()}
                </div>
              </td>
              <td className='px-4 py-3'>
                <Button variant='ghost' size='sm' onClick={() => handleEdit(item)} className='h-8 w-8 p-0'>
                  <IconEdit className='h-4 w-4' />
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {data.length === 0 && <div className='text-muted-foreground py-8 text-center'>Không có dữ liệu</div>}
    </div>
  )
}
