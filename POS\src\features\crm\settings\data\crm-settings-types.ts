import { z } from 'zod'

import { crmSettingsSchema } from './crm-settings-schema'

export type CrmSettingsFormValues = z.infer<typeof crmSettingsSchema>

export interface BrandConfig {
  brandName: string
  bannerImage?: string
  logo?: string
  hotline: string
  email: string[]
}

export interface DeliveryConfig {
  representativeStore: string
  serviceCharge: number
  vat: number
}

export interface MembershipConfig {
  enablePointAccumulation: boolean
  enableAutoMemberUpgrade: boolean
  autoUpgradeDays?: number
  resetPointsDays?: number
  excludeInvoiceOrigins: string[]
}

export interface TransactionAlertConfig {
  enableAccountBalanceAlert: boolean
  balanceThreshold?: number
  enableAccountBalanceAlertVND: boolean
  balanceThresholdVND?: number
  emailNotificationType: 'regular' | 'other'
  customEmails?: string[]
}

export interface OnlineOrderAlertConfig {
  emailNotificationType: 'regular' | 'point' | 'other'
  customEmails?: string[]
}
