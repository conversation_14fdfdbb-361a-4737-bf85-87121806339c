import { useNavigate } from '@tanstack/react-router'

import { useUsingDaily } from '@/hooks/crm'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui'

import type { UsingMonthData } from '../types'

interface TableDataProps {
  data: UsingMonthData
  searchParams: {
    year: string
    month: string
    pos_parent: string
  } | null
}

export function TableData({ data, searchParams }: TableDataProps) {
  const navigate = useNavigate()

  const dailyParams = searchParams
    ? {
        date_start: `${searchParams.year}-${searchParams.month.padStart(2, '0')}-01 00:00:00`,
        date_end: `${searchParams.year}-${searchParams.month.padStart(2, '0')}-31 23:59:59`,
        number_per_page: 40,
        pos_parent: searchParams.pos_parent
      }
    : null

  const dailyQuery = useUsingDaily(dailyParams)

  const formatDate = (day: number, month: number, year: number) => {
    return `${day}/${month}/${year}`
  }

  const getTypeCode = (columnName: string): string => {
    const typeMapping: Record<string, string> = {
      voucher_many_time_used: 'MANY_TIME',
      used_voucher_amount: 'PUBLISH',
      facebook_message: 'FACEBOOK',
      zalo_message: 'ZALO',
      sms_message: 'SMS',
      zns_message: 'ZNS',
      tin_gui_ho: 'ZALO_SEND_BY_APP_MERCHANT'
    }
    return typeMapping[columnName] || ''
  }

  const handleCellClick = (columnName: string, dailyItem: any) => {
    const dateParam = `${dailyItem.year}-${String(dailyItem.month).padStart(2, '0')}-${String(dailyItem.day).padStart(2, '0')} 00:00:00`

    const typeCode = getTypeCode(columnName)

    navigate({
      to: '/crm/billing-detail',
      search: {
        date: dateParam,
        type: typeCode
      }
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Dữ liệu sử dụng</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Thời gian</TableHead>
              <TableHead>Voucher sử dụng nhiều lần</TableHead>
              <TableHead>Voucher sử dụng 1 lần</TableHead>
              <TableHead>Tin nhắn Facebook</TableHead>
              <TableHead>Tin nhắn Zalo</TableHead>
              <TableHead>Tin nhắn giao dịch zalo</TableHead>
              <TableHead>Tin nhắn SMS OTP (Brandname iPOS.vn)</TableHead>
              <TableHead>Tin nhắn ZNS</TableHead>
              <TableHead>Tin gửi hộ</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {dailyQuery.data?.list_rp_daily_billing?.map(dailyItem => (
              <TableRow key={dailyItem.id}>
                <TableCell className='font-medium'>
                  {formatDate(dailyItem.day, dailyItem.month, dailyItem.year)}
                </TableCell>
                <TableCell>
                  <span
                    className='cursor-pointer transition-colors hover:text-blue-600 hover:underline'
                    onClick={() => handleCellClick('voucher_many_time_used', dailyItem)}
                  >
                    {dailyItem.voucher_many_time_used}
                  </span>
                </TableCell>
                <TableCell>
                  <span
                    className='cursor-pointer transition-colors hover:text-blue-600 hover:underline'
                    onClick={() => handleCellClick('used_voucher_amount', dailyItem)}
                  >
                    {dailyItem.used_voucher_amount}
                  </span>
                </TableCell>
                <TableCell>
                  <span
                    className='cursor-pointer transition-colors hover:text-blue-600 hover:underline'
                    onClick={() => handleCellClick('facebook_message', dailyItem)}
                  >
                    {dailyItem.facebook_message}
                  </span>
                </TableCell>
                <TableCell>
                  <span
                    className='cursor-pointer transition-colors hover:text-blue-600 hover:underline'
                    onClick={() => handleCellClick('zalo_message', dailyItem)}
                  >
                    {dailyItem.zalo_message}
                  </span>
                </TableCell>
                <TableCell>{dailyItem.zalo_transaction_message}</TableCell>
                <TableCell>
                  <span
                    className='cursor-pointer transition-colors hover:text-blue-600 hover:underline'
                    onClick={() => handleCellClick('sms_message', dailyItem)}
                  >
                    {dailyItem.sms_message}
                  </span>
                </TableCell>
                <TableCell>
                  <span
                    className='cursor-pointer transition-colors hover:text-blue-600 hover:underline'
                    onClick={() => handleCellClick('zns_message', dailyItem)}
                  >
                    {dailyItem.zns_message}
                  </span>
                </TableCell>
                <TableCell>
                  <span
                    className='cursor-pointer transition-colors hover:text-blue-600 hover:underline'
                    onClick={() => handleCellClick('tin_gui_ho', dailyItem)}
                  >
                    {dailyItem.zns_send_by_app_mc +
                      dailyItem.promo_send_by_app_mc +
                      dailyItem.trans_send_by_app_mc +
                      dailyItem.cs_send_by_app_mc}
                  </span>
                </TableCell>
              </TableRow>
            ))}

            <TableRow className='border-t-2 border-gray-300 bg-gray-50'>
              <TableCell className='font-bold'>Cả tháng</TableCell>
              <TableCell className='font-semibold'>{data.voucher_many_time_used}</TableCell>
              <TableCell className='font-semibold'>{data.used_voucher_amount}</TableCell>
              <TableCell className='font-semibold'>{data.facebook_message}</TableCell>
              <TableCell className='font-semibold'>{data.zalo_message}</TableCell>
              <TableCell className='font-semibold'>{data.zalo_transaction_message}</TableCell>
              <TableCell className='font-semibold'>{data.sms_message}</TableCell>
              <TableCell className='font-semibold'>{data.zns_message}</TableCell>
              <TableCell className='font-semibold'>
                {data.zns_send_by_app_mc +
                  data.promo_send_by_app_mc +
                  data.trans_send_by_app_mc +
                  data.cs_send_by_app_mc}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
