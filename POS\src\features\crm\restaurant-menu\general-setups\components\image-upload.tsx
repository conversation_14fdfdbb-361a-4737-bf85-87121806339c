import { useState } from 'react'
import { Upload, X } from 'lucide-react'

import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

interface ImageUploadProps {
  value?: string | File | null
  onChange: (file: File | null, preview: string) => void
  disabled?: boolean
  accept?: string
  maxSize?: number 
  placeholder?: string
  className?: string
}

export function ImageUpload({
  value,
  onChange,
  disabled = false,
  accept = 'image/*',
  maxSize = 10,
  placeholder = 'TẢI ẢNH LÊN',
  className = ''
}: ImageUploadProps) {
  const [preview, setPreview] = useState<string>('')
  const [fileName, setFileName] = useState<string>('')

  // Determine preview URL
  const getPreviewUrl = () => {
    if (preview) return preview
    if (typeof value === 'string') return value
    return ''
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      alert(`File size must be less than ${maxSize}MB`)
      return
    }

    const reader = new FileReader()
    reader.onload = (event) => {
      const previewUrl = event.target?.result as string
      setPreview(previewUrl)
      setFileName(file.name)
      onChange(file, previewUrl)
    }
    reader.readAsDataURL(file)
  }

  const handleRemove = () => {
    setPreview('')
    setFileName('')
    onChange(null, '')
  }

  const previewUrl = getPreviewUrl()

  return (
    <div className={`space-y-2 ${className}`}>
      {previewUrl ? (
        <div className="space-y-2">
          <div className="relative inline-block">
            <img
              src={previewUrl}
              alt="Preview"
              className="h-32 w-full max-w-md rounded-md border object-cover"
            />
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
              onClick={handleRemove}
              disabled={disabled}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
          {fileName && (
            <p className="text-xs text-muted-foreground">{fileName}</p>
          )}
        </div>
      ) : (
        <div className="rounded-md border-2 border-dashed border-gray-300 p-8 text-center transition-colors hover:border-gray-400">
          <div className="space-y-4">
            <div className="mx-auto h-12 w-12 text-gray-400">
              <Upload className="h-full w-full" />
            </div>
            <div>
              <label htmlFor="image-upload" className="cursor-pointer">
                <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                  {placeholder}
                </span>
                <Input
                  id="image-upload"
                  type="file"
                  accept={accept}
                  onChange={handleFileUpload}
                  disabled={disabled}
                  className="hidden"
                />
              </label>
            </div>
            <p className="text-xs text-gray-500">
              PNG, JPG, GIF up to {maxSize}MB
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
