import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'

interface CopyMenuConfirmDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => void
  title: string
  isLoading?: boolean
}

export function CopyMenuConfirmDialog({
  open,
  onOpenChange,
  onConfirm,
  title,
  isLoading = false
}: CopyMenuConfirmDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle className='text-center'>{title}</DialogTitle>
        </DialogHeader>

        <DialogFooter className='flex justify-between gap-4'>
          <Button variant='outline' onClick={() => onOpenChange(false)} disabled={isLoading} className='flex-1'>
            Hủy
          </Button>
          <Button onClick={onConfirm} disabled={isLoading} className='flex-1 bg-green-600 hover:bg-green-700'>
            {isLoading ? 'Đang đồng bộ...' : 'Đồng bộ'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
