import React from 'react'

import { format } from 'date-fns'

import { vi } from 'date-fns/locale'

import { TablePagination } from '@/components/table-pagination'
import { Badge, Button, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui'

export interface DataTableColumn<T> {
  key: keyof T | string
  header: string
  width?: string
  render?: (value: any, item: T, index: number) => React.ReactNode
  sortable?: boolean
}

interface DataTableProps<T> {
  data: T[]
  columns: DataTableColumn<T>[]
  isLoading?: boolean
  onRowClick?: (item: T) => void
  pageSize?: number
  emptyMessage?: string
  loadingMessage?: string
  className?: string
}

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  isLoading = false,
  onRowClick,
  pageSize = 20,
  emptyMessage = 'Không có dữ liệu',
  loadingMessage = 'Đang tải...',
  className
}: DataTableProps<T>) {
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return format(date, 'dd-MM-yyyy HH:mm:ss', { locale: vi })
    } catch {
      return dateString
    }
  }

  const renderCellValue = (column: DataTableColumn<T>, item: T, index: number) => {
    const value = item[column.key as keyof T]

    if (column.render) {
      return column.render(value, item, index)
    }

    // Default renderers for common data types
    if (column.key === 'Allow_Self_Order' || column.key === 'Allow_Take_Away') {
      return <Badge variant={value === 1 ? 'default' : 'destructive'}>{value === 1 ? '✓' : '✗'}</Badge>
    }

    if (column.key === 'Active') {
      return (
        <Badge variant={value === 1 ? 'default' : 'destructive'}>{value === 1 ? 'Hoạt động' : 'Không hoạt động'}</Badge>
      )
    }

    if (typeof value === 'string' && value.includes('T') && value.includes(':')) {
      return <span className='text-muted-foreground text-sm'>{formatDate(value)}</span>
    }

    if (typeof value === 'number' && (String(column.key).includes('Price') || String(column.key).includes('price'))) {
      return <span>{value.toLocaleString('vi-VN')} đ</span>
    }

    return value
  }

  if (isLoading) {
    return (
      <div className='flex h-64 items-center justify-center'>
        <div className='text-muted-foreground'>{loadingMessage}</div>
      </div>
    )
  }

  if (!data.length) {
    return (
      <div className='flex h-64 items-center justify-center'>
        <div className='text-muted-foreground'>{emptyMessage}</div>
      </div>
    )
  }

  return (
    <TablePagination data={data} pageSize={pageSize}>
      {(paginatedData, pagination) => (
        <div className={`rounded-md border ${className}`}>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className='w-12'>#</TableHead>
                {columns.map((column, index) => (
                  <TableHead key={index} style={{ width: column.width }}>
                    {column.header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.map((item, index) => (
                <TableRow
                  key={index}
                  className={onRowClick ? 'cursor-pointer hover:bg-gray-50' : ''}
                  onClick={() => onRowClick?.(item)}
                >
                  <TableCell>{pagination.startIndex + index + 1}</TableCell>
                  {columns.map((column, colIndex) => (
                    <TableCell key={colIndex} style={{ width: column.width }}>
                      {renderCellValue(column, item, index)}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </TablePagination>
  )
}

// Helper function to create combo column configurations
export const createComboColumns = (onEditItem?: (item: any) => void): DataTableColumn<any>[] => [
  {
    key: 'Item_Id',
    header: 'Mã combo',
    width: '150px',
    render: value => <span className='font-medium'>{value}</span>
  },
  {
    key: 'Item_Name',
    header: 'Tên combo',
    width: '250px',
    render: value => <span className='font-medium'>{value}</span>
  },
  {
    key: 'Item_Image_Path',
    header: 'Ảnh',
    width: '80px',
    render: value => (
      <div className='flex h-12 w-12 items-center justify-center rounded border-2 border-dashed border-gray-300 bg-gray-100'>
        {value ? (
          <img src={value} alt='combo' className='h-full w-full rounded object-cover' />
        ) : (
          <span className='text-xs text-gray-400'>Ảnh</span>
        )}
      </div>
    )
  },
  {
    key: 'Allow_Self_Order',
    header: 'Bán tại chỗ',
    width: '100px'
  },
  {
    key: 'Allow_Take_Away',
    header: 'Bán mang đi',
    width: '100px'
  },
  {
    key: 'Last_Updated',
    header: 'Thời gian cập nhật',
    width: '180px'
  },
  {
    key: 'actions',
    header: 'Chỉnh sửa',
    width: '100px',
    render: (_, item) => (
      <Button
        variant='default'
        size='sm'
        onClick={e => {
          e.stopPropagation()
          onEditItem?.(item)
        }}
      >
        Chỉnh sửa
      </Button>
    )
  }
]

// Helper function to create menu item column configurations
export const createMenuItemColumns = (onEditItem?: (item: any) => void): DataTableColumn<any>[] => [
  {
    key: 'Item_Id',
    header: 'Mã món',
    width: '120px',
    render: value => <span className='font-medium'>{value}</span>
  },
  {
    key: 'Item_Name',
    header: 'Tên món',
    render: value => <span className='font-medium'>{value}</span>
  },
  {
    key: 'Item_Image_Path',
    header: 'Ảnh',
    width: '80px',
    render: value => (
      <div className='flex h-12 w-12 items-center justify-center rounded border-2 border-dashed border-gray-300 bg-gray-100'>
        {value ? (
          <img src={value} alt='combo' className='h-full w-full rounded object-cover' />
        ) : (
          <span className='text-xs text-gray-400'>Ảnh</span>
        )}
      </div>
    )
  },
  {
    key: 'Item_Type_Id',
    header: 'Nhóm món',
    render: value => {
      if (value === 'ITEM_TYPE_OTHER') return 'Món thường'
      if (value?.startsWith('ITEM_TYPE-')) return value.replace('ITEM_TYPE-', '')
      return value
    }
  },
  {
    key: 'Is_Eat_With',
    header: 'Loại món',
    render: value => (value === 1 ? 'Món ăn kèm' : 'Món thường')
  },
  {
    key: 'Allow_Self_Order',
    header: 'Bán tại chỗ',
    width: '100px'
  },
  {
    key: 'Allow_Take_Away',
    header: 'Bán mang đi',
    width: '100px'
  },
  {
    key: 'Sort',
    header: 'Thứ tự',
    width: '80px'
  },
  {
    key: 'Last_Updated',
    header: 'Thời gian cập nhật',
    width: '180px'
  },
  {
    key: 'actions',
    header: 'Chỉnh sửa',
    width: '100px',
    render: (_, item) => (
      <Button
        variant='default'
        size='sm'
        onClick={e => {
          e.stopPropagation()
          onEditItem?.(item)
        }}
      >
        Chỉnh sửa
      </Button>
    )
  }
]
