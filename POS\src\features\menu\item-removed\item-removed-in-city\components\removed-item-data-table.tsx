import { useState, useEffect } from 'react'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  RowSelectionState,
} from '@tanstack/react-table'
import { RemovedItem } from '@/types/item-removed'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Button,
} from '@/components/ui'

interface RemovedItemDataTableProps {
  columns: ColumnDef<RemovedItem>[]
  data: RemovedItem[]
  onRestoreItem?: (item: RemovedItem) => void
  getCityName?: (cityUid: string) => string
  onBulkRestore?: (selectedItems: RemovedItem[]) => void
  clearSelection?: boolean
}

export function RemovedItemDataTable({
  columns,
  data,
  onRestoreItem,
  getCityName,
  onBulkRestore,
  clearSelection,
}: RemovedItemDataTableProps) {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})

  // Clear selection when clearSelection prop changes
  useEffect(() => {
    if (clearSelection) {
      setRowSelection({})
    }
  }, [clearSelection])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    state: {
      rowSelection,
    },
    meta: {
      onRestoreItem,
      getCityName,
    },
  })

  return (
    <div>
      {/* Button Khôi phục ở trên table */}
      {table.getFilteredSelectedRowModel().rows.length > 0 && onBulkRestore && (
        <div className='mb-4'>
          <Button
            variant='default'
            onClick={() => {
              const selectedItems = table
                .getFilteredSelectedRowModel()
                .rows.map((row) => row.original)
              onBulkRestore(selectedItems)
            }}
          >
            Khôi phục
          </Button>
        </div>
      )}

      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center'
                >
                  Không có dữ liệu món đã xóa.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className='flex items-center justify-end space-x-2 py-4'>
        <div className='text-muted-foreground text-sm'>
          {table.getFilteredSelectedRowModel().rows.length} of{' '}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
      </div>
    </div>
  )
}
