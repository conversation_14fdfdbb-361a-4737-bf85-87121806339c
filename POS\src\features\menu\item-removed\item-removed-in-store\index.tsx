import { useState, useMemo } from 'react'

import { format } from 'date-fns'

import { getStores, type ApiStore, removedItemsApi } from '@/lib'
import { RemovedItem } from '@/types'
import { getErrorMessage } from '@/utils'
import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import { useAuthStore } from '@/stores/authStore'

import { formatCurrency } from '@/lib/utils'

import {
  useRemovedItemsData,
  useRestoreRemovedItem,
  useBulkRestoreRemovedItems,
  useExportRemovedItemsReportByStores,
  useExportRemovedItemsReport,
  useStoresData
} from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ConfirmModal } from '@/components/pos'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { removedItemColumns, RemovedItemDataTable, ActionBar } from './components'

function generateExcelReport(data: RemovedItem[], storeDisplayName: string) {
  const workbook = XLSX.utils.book_new()

  const excelData = []

  excelData.push([`Món đã xóa tại cửa hàng ${storeDisplayName}`])

  excelData.push(['Mã món', 'Tên món', 'Giá', 'Người xóa', 'Thời gian xóa'])

  data.forEach(item => {
    const itemCode = item.item_id || ''
    const itemName = item.item_name || ''
    const price = formatCurrency(item.ots_price)
    const deletedBy = item.deleted_by || ''
    const deletedAt = format(new Date(item.deleted_at * 1000), 'dd/MM/yyyy HH:mm')

    excelData.push([itemCode, itemName, price, deletedBy, deletedAt])
  })

  const worksheet = XLSX.utils.aoa_to_sheet(excelData)

  worksheet['!cols'] = [
    { wch: 15 }, // Mã món
    { wch: 30 }, // Tên món
    { wch: 15 }, // Giá
    { wch: 25 }, // Người xóa
    { wch: 20 } // Thời gian xóa
  ]

  worksheet['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 4 } }]

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Món đã xóa')

  const fileName = `item-removed-in-store.xlsx`
  XLSX.writeFile(workbook, fileName)
}

function generateMultiStoreExcelReport(
  storesWithData: Array<{
    store: ApiStore
    data: RemovedItem[]
    success: boolean
  }>
) {
  const workbook = XLSX.utils.book_new()

  storesWithData.forEach(({ store, data }) => {
    const storeDisplayName = `${store.fb_store_id}-${store.store_name}`

    const excelData = []

    excelData.push([`Món đã xóa tại cửa hàng ${storeDisplayName}`])

    excelData.push(['Mã món', 'Tên món', 'Giá', 'Người xóa', 'Thời gian xóa'])

    data.forEach(item => {
      const itemCode = item.item_id || ''
      const itemName = item.item_name || ''
      const price = formatCurrency(item.ots_price)
      const deletedBy = item.deleted_by || ''
      const deletedAt = format(new Date(item.deleted_at * 1000), 'dd/MM/yyyy HH:mm')

      excelData.push([itemCode, itemName, price, deletedBy, deletedAt])
    })

    const worksheet = XLSX.utils.aoa_to_sheet(excelData)

    worksheet['!cols'] = [
      { wch: 15 }, // Mã món
      { wch: 30 }, // Tên món
      { wch: 15 }, // Giá
      { wch: 25 }, // Người xóa
      { wch: 20 } // Thời gian xóa
    ]

    worksheet['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 4 } }]

    const sheetName = storeDisplayName.replace(/[\\/?*[\]]/g, '').substring(0, 31)
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
  })

  const fileName = `mon-da-xoa-tat-ca-cua-hang-${format(new Date(), 'dd-MM-yyyy')}.xlsx`
  XLSX.writeFile(workbook, fileName)
}

export default function RemovedItemsInStorePage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<RemovedItem | null>(null)
  const [selectedItems, setSelectedItems] = useState<RemovedItem[]>([])
  const [clearSelection, setClearSelection] = useState(false)

  const restoreItemMutation = useRestoreRemovedItem()
  const bulkRestoreItemsMutation = useBulkRestoreRemovedItems()
  const exportReportByStoresMutation = useExportRemovedItemsReportByStores()
  const exportReportAllStoresMutation = useExportRemovedItemsReport()
  const { data: stores = [] } = useStoresData()
  const { company, brands } = useAuthStore(state => state.auth)

  useMemo(() => {
    if (stores.length > 0 && !selectedStoreId) {
      setSelectedStoreId(stores[0].id)
    }
  }, [stores, selectedStoreId])

  const listStoreUid = useMemo(() => {
    return selectedStoreId ? [selectedStoreId] : []
  }, [selectedStoreId])

  const {
    data: removedItems,
    isLoading,
    error
  } = useRemovedItemsData({
    searchTerm: searchTerm || undefined,
    listStoreUid
  })

  const handleRestoreItem = (item: RemovedItem) => {
    setSelectedItem(item)
    setSelectedItems([])
    setConfirmModalOpen(true)
  }

  const handleConfirmRestore = async () => {
    try {
      if (selectedItems.length > 0) {
        const itemUids = selectedItems.map(item => item.id)
        await bulkRestoreItemsMutation.mutateAsync(itemUids)
        toast.success(`${selectedItems.length} món đã được khôi phục thành công!`)
        setSelectedItems([])
        setClearSelection(true)
        setTimeout(() => setClearSelection(false), 100)
      } else if (selectedItem) {
        await bulkRestoreItemsMutation.mutateAsync([selectedItem.id])
        toast.success(`Món "${selectedItem.item_name}" đã được khôi phục thành công!`)
        setSelectedItem(null)
      }

      setConfirmModalOpen(false)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleBulkRestore = (items: RemovedItem[]) => {
    setSelectedItems(items)
    setSelectedItem(null)
    setConfirmModalOpen(true)
  }

  const handleExportReportBySelectedStore = async () => {
    if (!selectedStoreId) {
      toast.error('Không có cửa hàng nào được chọn')
      return
    }

    const selectedStore = stores.find(s => s.id === selectedStoreId)
    if (!selectedStore) {
      toast.error('Không tìm thấy thông tin cửa hàng')
      return
    }

    try {
      const selectedBrand = brands?.[0]
      if (!company?.id || !selectedBrand?.id) {
        toast.error('Không tìm thấy thông tin công ty hoặc thương hiệu')
        return
      }

      const rawStoresResponse = await getStores({
        company_uid: company.id,
        brand_uid: selectedBrand.id
      })

      const rawSelectedStore = rawStoresResponse.data.find(s => s.id === selectedStoreId)
      if (!rawSelectedStore) {
        toast.error('Không tìm thấy thông tin chi tiết cửa hàng')
        return
      }

      const removedItemsData = await removedItemsApi.getRemovedItems({
        listStoreUid: [selectedStoreId]
      })

      if (!removedItemsData || removedItemsData.length === 0) {
        toast.error('Không có dữ liệu món đã xóa để xuất báo cáo')
        return
      }

      const storeDisplayName = `${rawSelectedStore.fb_store_id}-${rawSelectedStore.store_name}`

      generateExcelReport(removedItemsData, storeDisplayName)
      toast.success('Báo cáo theo cửa hàng đã được xuất thành công!')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleExportReportAllStores = async () => {
    if (stores.length === 0) {
      toast.error('Không có cửa hàng nào để xuất báo cáo')
      return
    }

    try {
      const selectedBrand = brands?.[0]
      if (!company?.id || !selectedBrand?.id) {
        toast.error('Không tìm thấy thông tin công ty hoặc thương hiệu')
        return
      }

      const rawStoresResponse = await getStores({
        company_uid: company.id,
        brand_uid: selectedBrand.id
      })

      if (!rawStoresResponse.data || rawStoresResponse.data.length === 0) {
        toast.error('Không tìm thấy danh sách cửa hàng')
        return
      }

      const storeDataPromises = rawStoresResponse.data.map(async store => {
        try {
          const removedItemsData = await removedItemsApi.getRemovedItems({
            listStoreUid: [store.id]
          })

          return {
            store,
            data: removedItemsData || [],
            success: true
          }
        } catch (error) {
          return {
            store,
            data: [],
            success: false
          }
        }
      })

      const storeResults = await Promise.all(storeDataPromises)

      const storesWithData = storeResults.filter(result => result.success && result.data.length > 0)

      if (storesWithData.length === 0) {
        toast.error('Không có dữ liệu món đã xóa từ bất kỳ cửa hàng nào')
        return
      }

      generateMultiStoreExcelReport(storesWithData)

      const totalStores = rawStoresResponse.data.length
      const storesWithDataCount = storesWithData.length
      toast.success(
        `Báo cáo tất cả cửa hàng đã được xuất thành công! (${storesWithDataCount}/${totalStores} cửa hàng có dữ liệu)`
      )
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto px-4 py-8'>
          <ActionBar
            searchQuery={searchQuery}
            onSearchQueryChange={setSearchQuery}
            onSearchSubmit={setSearchTerm}
            selectedStoreId={selectedStoreId}
            onStoreChange={setSelectedStoreId}
            stores={stores}
            isExporting={exportReportByStoresMutation.isPending || exportReportAllStoresMutation.isPending}
            onExportBySelectedStore={handleExportReportBySelectedStore}
            onExportAllStores={handleExportReportAllStores}
          />

          {error && (
            <div className='py-8 text-center'>
              <p className='text-red-600'>{getErrorMessage(error)}</p>
            </div>
          )}
          {!error && isLoading && (
            <div className='py-8 text-center'>
              <p>Đang tải dữ liệu món đã xóa...</p>
            </div>
          )}
          {!error && !isLoading && (
            <RemovedItemDataTable
              columns={removedItemColumns}
              data={removedItems || []}
              onRestoreItem={handleRestoreItem}
              onBulkRestore={handleBulkRestore}
              clearSelection={clearSelection}
            />
          )}

          <ConfirmModal
            open={confirmModalOpen}
            onOpenChange={setConfirmModalOpen}
            content={
              selectedItems.length > 0
                ? `Bạn có muốn khôi phục ${selectedItems.length} món đã chọn?`
                : 'Bạn có muốn khôi phục?'
            }
            confirmText='Xác nhận'
            onConfirm={handleConfirmRestore}
            isLoading={restoreItemMutation.isPending || bulkRestoreItemsMutation.isPending}
          />
        </div>
      </Main>
    </>
  )
}
