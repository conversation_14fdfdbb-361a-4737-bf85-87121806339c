import { useState } from 'react'

import type { ItemsInStore } from '../data'

export function useItemsInStoreListState() {
  const [isCustomizationDialogOpen, setIsCustomizationDialogOpen] = useState(false)
  const [isBuffetItem, setIsBuffetItem] = useState(false)
  const [selectedMenuItem, setSelectedMenuItem] = useState<ItemsInStore | null>(null)
  const [isBuffetConfigModalOpen, setIsBuffetConfigModalOpen] = useState(false)
  const [selectedBuffetMenuItem, setSelectedBuffetMenuItem] = useState<string[]>([])
  const [selectedItemTypeUid, setSelectedItemTypeUid] = useState<string>('all')
  const [selectedStoreUid, setSelectedStoreUid] = useState<string>('')
  const [selectedDaysOfWeek, setSelectedDaysOfWeek] = useState<string[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedApplyWithStore, setSelectedApplyWithStore] = useState<string>('-1')

  return {
    isCustomizationDialogOpen,
    isBuffetItem,
    selectedMenuItem,
    isBuffetConfigModalOpen,
    selectedBuffetMenuItem,
    selectedItemTypeUid,
    selectedStoreUid,
    selectedDaysOfWeek,
    selectedStatus,
    selectedApplyWithStore,

    setIsCustomizationDialogOpen,
    setIsBuffetItem,
    setSelectedMenuItem,
    setIsBuffetConfigModalOpen,
    setSelectedBuffetMenuItem,
    setSelectedItemTypeUid,
    setSelectedStoreUid,
    setSelectedDaysOfWeek,
    setSelectedStatus,
    setSelectedApplyWithStore
  }
}
