import { useMemo } from 'react'

import type { PaymentMethodRevenueData } from '@/types/api'
import { <PERSON>, Pie, Pie<PERSON><PERSON>, ResponsiveContainer, Tooltip } from 'recharts'

import { formatCurrency } from '@/lib/utils'

interface PaymentMethodsPieChartProps {
  data: PaymentMethodRevenueData[]
  isLoading: boolean
  isError: boolean
}

const defaultColors = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
  const RADIAN = Math.PI / 180
  const radius = innerRadius + (outerRadius - innerRadius) * 0.6
  const x = cx + radius * Math.cos(-midAngle * RADIAN)
  const y = cy + radius * Math.sin(-midAngle * RADIAN)

  return (
    <text x={x} y={y} fill='white' textAnchor='middle' dominantBaseline='central' fontSize='12' fontWeight='bold'>
      {`${(percent * 100).toFixed(1)}%`}
    </text>
  )
}

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0]
    return (
      <div className='bg-background rounded-lg border p-2 shadow-md'>
        <p className='font-medium'>{data.name}</p>
        <p className='text-muted-foreground text-sm'>{formatCurrency(data.value)}</p>
      </div>
    )
  }
  return null
}

export function PaymentMethodsPieChart({ data: apiData, isLoading, isError }: PaymentMethodsPieChartProps) {
  const chartData = useMemo(() => {
    if (isLoading || isError || !apiData?.length) {
      return []
    }

    return apiData
      .sort((a, b) => b.total_amount - a.total_amount)
      .slice(0, 5)
      .map((item, index) => ({
        name: item.payment_method_name,
        value: item.total_amount,
        color: defaultColors[index] || defaultColors[0]
      }))
  }, [apiData, isLoading, isError])

  if (isLoading) {
    return (
      <div className='flex h-[400px] items-center justify-center'>
        <div className='text-center'>
          <div className='border-primary mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2'></div>
          <p className='text-muted-foreground text-sm'>Đang tải dữ liệu...</p>
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className='flex h-[400px] items-center justify-center'>
        <div className='text-center'>
          <p className='text-sm text-red-500'>Lỗi tải dữ liệu phương thức thanh toán</p>
        </div>
      </div>
    )
  }

  if (!chartData.length) {
    return (
      <div className='flex h-[400px] items-center justify-center'>
        <div className='text-muted-foreground text-sm'>Chưa có thông tin</div>
      </div>
    )
  }

  return (
    <div className='h-[250px] w-full'>
      <ResponsiveContainer width='100%' height='100%'>
        <PieChart>
          <Pie
            data={chartData}
            cx='50%'
            cy='50%'
            labelLine={false}
            label={renderCustomizedLabel}
            innerRadius={65}
            outerRadius={120}
            paddingAngle={2}
            dataKey='value'
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>

      {/* Legend */}
      <div className='mt-4 grid grid-cols-1 gap-2 text-sm'>
        {chartData.map((entry, index) => (
          <div key={index} className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <div className='h-3 w-3 rounded-full' style={{ backgroundColor: entry.color }} />
              <span className='text-muted-foreground'>{entry.name}</span>
            </div>
            <span className='font-bold'>{formatCurrency(entry.value)}</span>
          </div>
        ))}
      </div>
    </div>
  )
}
