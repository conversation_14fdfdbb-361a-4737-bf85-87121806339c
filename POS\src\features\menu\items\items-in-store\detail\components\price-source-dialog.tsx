import React, { useEffect, useState } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { Source } from '@/types/sources'
import { X } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'

import { Combobox } from '@/components/pos/combobox'

import { TimeFrameConfig } from '../../hooks'
import {
  formatDate,
  getDayLabels,
  getHourLabels,
  convertToPriceTimes,
  getDaysFromBitwise,
  getHoursFromBitwise
} from '../../utils/utils'
import { TimeFrameConfigDialog } from './time-frame-config-dialog'

const priceSourceSchema = z.object({
  price: z.coerce.number().min(0, 'Số tiền phải lớn hơn hoặc bằng 0'),
  source_id: z.string().optional(),
  source_name: z.string().optional(),
  price_times: z.array(z.any()).optional(),
  is_source_exist_in_city: z.boolean().optional()
})

type PriceSourceFormValues = z.infer<typeof priceSourceSchema>

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm?: (data: PriceSourceFormValues) => void
  sources: Source[]
  data?: any
}

export function PriceSourceDialog({ open, onOpenChange, onConfirm, sources, data }: Props) {
  const [selectedSource, setSelectedSource] = useState<Source | null>(null)
  const [timeFrameConfigOpen, setTimeFrameConfigOpen] = useState(false)
  const [timeFrameConfigs, setTimeFrameConfigs] = useState<TimeFrameConfig[]>([])
  const [editingTimeFrameId, setEditingTimeFrameId] = useState<string | null>(null)
  const [timeFrameDialogData, setTimeFrameDialogData] = useState<{
    amount?: number
    startDate?: Date
    endDate?: Date
    selectedDays?: number[]
    selectedHours?: number[]
  }>()

  const form = useForm<PriceSourceFormValues>({
    resolver: zodResolver(priceSourceSchema),
    defaultValues: {
      source_id: data?.source_id || '',
      price: data?.price || 0,
      price_times: data?.price_times || []
    }
  })

  useEffect(() => {
    if (open && data) {
      form.setValue('source_id', data.source_id)
      form.setValue('price', data.price)
      form.setValue('price_times', data.price_times)

      const source = sources.find(s => s.sourceId === data.source_id)
      setSelectedSource(source || null)

      if (Array.isArray(data.price_times) && data.price_times.length > 0) {
        const configs = data.price_times.map((pt: any) => ({
          id: Date.now().toString() + Math.random(),
          price: pt.price,
          startDate: new Date(pt.from_date),
          endDate: new Date(pt.to_date),
          selectedDays: pt.time_sale_date_week ? getDaysFromBitwise(pt.time_sale_date_week) : [],
          selectedHours: pt.time_sale_hour_day ? getHoursFromBitwise(pt.time_sale_hour_day) : []
        }))
        setTimeFrameConfigs(configs)
      } else {
        setTimeFrameConfigs([])
      }
    }
  }, [open, data, form, sources])

  const onSubmit = (data: PriceSourceFormValues) => {
    const dataWithSourceName = {
      ...data,
      source_id: selectedSource?.sourceId,
      source_name: selectedSource?.sourceName,
      price_times: convertToPriceTimes(timeFrameConfigs),
      is_source_exist_in_city: true
    }
    onConfirm?.(dataWithSourceName)
    onOpenChange(false)
    form.reset()
    setSelectedSource(null)
    setTimeFrameConfigs([])
  }

  const handleTimeFrameConfigConfirm = (data: any) => {
    const edited: TimeFrameConfig = {
      id: editingTimeFrameId || Date.now().toString(),
      price: Number(data.amount || 0),
      startDate: data.startDate,
      endDate: data.endDate,
      selectedDays: data.selectedDays,
      selectedHours: data.selectedHours
    }

    setTimeFrameConfigs(prev => {
      let updatedConfigs: TimeFrameConfig[]
      if (editingTimeFrameId) {
        updatedConfigs = prev.map(cfg => (cfg.id === editingTimeFrameId ? edited : cfg))
      } else {
        updatedConfigs = [...prev, edited]
      }
      const priceTimes = convertToPriceTimes(updatedConfigs)
      form.setValue('price_times', priceTimes)
      return updatedConfigs
    })
    setEditingTimeFrameId(null)
  }

  const removeTimeFrameConfig = (id: string) => {
    setTimeFrameConfigs(prev => {
      const updatedConfigs = prev.filter(config => config.id !== id)
      const priceTimes = convertToPriceTimes(updatedConfigs)
      form.setValue('price_times', priceTimes)
      return updatedConfigs
    })
  }

  const handleFormSubmit = (event: React.FormEvent) => {
    event.preventDefault()
    event.stopPropagation()
    form.handleSubmit(onSubmit)(event)
  }

  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange(newOpen)
    if (!newOpen) {
      form.reset()
      setSelectedSource(null)
      setTimeFrameConfigOpen(false)
      setTimeFrameConfigs([])
      form.setValue('price_times', [])
      setEditingTimeFrameId(null)
      setTimeFrameDialogData(undefined)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-md lg:max-w-2xl'>
        <DialogHeader>
          <DialogTitle>Cấu hình giá theo nguồn</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={handleFormSubmit} className='space-y-4'>
            <div className='grid grid-cols-[120px_1fr] items-center gap-4'>
              <FormLabel className='text-sm font-medium text-gray-600'>Nguồn đơn</FormLabel>
              <FormField
                control={form.control}
                name='source_id'
                render={({ field }) => (
                  <FormItem>
                    <Combobox
                      options={sources.map(source => ({ value: source.sourceId, label: source.sourceName }))}
                      value={field.value}
                      onValueChange={value => {
                        field.onChange(value)
                        const source = sources.find(s => s.sourceId === value)
                        setSelectedSource(source || null)
                      }}
                      placeholder='Chọn nguồn đơn'
                      searchPlaceholder='Tìm nguồn đơn...'
                      emptyText='Không có nguồn phù hợp.'
                      className='w-full'
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-[120px_1fr] items-center gap-4'>
              <FormLabel className='text-sm font-medium text-gray-600'>Số tiền</FormLabel>
              <FormField
                control={form.control}
                name='price'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type='text'
                        placeholder='0'
                        value={field.value}
                        onChange={e => {
                          field.onChange(e.target.value)
                        }}
                        onKeyDown={e => {
                          if (
                            !/[0-9]/.test(e.key) &&
                            !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)
                          ) {
                            e.preventDefault()
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Cấu hình giá theo khung thời gian - chỉ hiển thị khi đã chọn nguồn */}
            {selectedSource && (
              <div className='space-y-4 border-t pt-4'>
                <div className='space-y-3'>
                  <h3 className='text-base font-medium text-gray-900'>Cấu hình giá theo khung thời gian</h3>

                  <div className='text-sm leading-relaxed text-gray-600'>
                    Giá theo nguồn <span className='font-semibold text-blue-600'>{selectedSource.sourceName}</span> sẽ
                    được lấy theo số tiền{' '}
                    <span className='font-semibold text-blue-600'>{form.getValues('price') || '0'} đ</span>. Khi cấu
                    hình giá theo khung thời gian số tiền sẽ hiển thị theo các khung thời gian cấu hình dưới đây
                  </div>

                  <div className='flex justify-end'>
                    <Button
                      type='button'
                      variant='outline'
                      className='text-blue-600 hover:text-blue-700'
                      onClick={() => {
                        setEditingTimeFrameId(null)
                        setTimeFrameDialogData({ amount: Number(form.getValues('price') || 0) })
                        setTimeFrameConfigOpen(true)
                      }}
                    >
                      Thêm cấu hình
                    </Button>
                  </div>
                </div>

                {/* Khu vực hiển thị danh sách cấu hình khung thời gian */}
                {timeFrameConfigs.length === 0 ? (
                  <div className='flex min-h-[120px] items-center justify-center rounded-lg border-2 border-dashed border-gray-200 bg-gray-50'>
                    <div className='text-center text-gray-500'>
                      <p className='text-sm'>Chưa có cấu hình khung thời gian nào</p>
                      <p className='mt-1 text-xs'>Nhấn "Thêm cấu hình" để bắt đầu</p>
                    </div>
                  </div>
                ) : (
                  <div className='space-y-3'>
                    {timeFrameConfigs.map(config => (
                      <div
                        key={config.id}
                        className='flex cursor-pointer items-start gap-3 rounded-lg border border-gray-200 bg-white p-4'
                        onClick={e => {
                          e.preventDefault()
                          e.stopPropagation()
                          setEditingTimeFrameId(config.id)
                          setTimeFrameDialogData({
                            amount: Number(config.price || 0),
                            startDate: config.startDate,
                            endDate: config.endDate,
                            selectedDays: config.selectedDays,
                            selectedHours: config.selectedHours
                          })
                          setTimeFrameConfigOpen(true)
                        }}
                      >
                        {/* Icon tròn */}
                        <div className='mt-1 h-2 w-2 rounded-full bg-gray-400'></div>

                        {/* Thông tin cấu hình */}
                        <div className='flex-1 space-y-1'>
                          <div className='text-sm text-gray-900'>
                            Từ ngày <span className='font-semibold'>{formatDate(config.startDate)}</span> đến ngày{' '}
                            <span className='font-semibold'>{formatDate(config.endDate)}</span>
                          </div>
                          <div className='text-sm text-gray-900'>
                            Giá: <span className='font-semibold'>{config.price?.toLocaleString('vi-VN') || '0'} ₫</span>
                          </div>
                          <div className='text-sm text-gray-900'>
                            Khung giờ <span className='font-semibold'>{getHourLabels(config.selectedHours)}</span> Thứ{' '}
                            <span className='font-semibold'>{getDayLabels(config.selectedDays)}</span>
                          </div>
                        </div>

                        {/* Nút xóa */}
                        <Button
                          type='button'
                          variant='ghost'
                          size='sm'
                          onClick={() => removeTimeFrameConfig(config.id)}
                          className='h-8 w-8 p-0 text-gray-400 hover:text-gray-600'
                        >
                          <X className='h-4 w-4' />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            <DialogFooter className='pt-4'>
              <DialogClose asChild>
                <Button type='button' variant='outline'>
                  Hủy
                </Button>
              </DialogClose>
              <Button type='submit'>Xác nhận</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>

      <TimeFrameConfigDialog
        open={timeFrameConfigOpen}
        onOpenChange={setTimeFrameConfigOpen}
        onConfirm={handleTimeFrameConfigConfirm}
        sourceName={selectedSource?.sourceName || ''}
        data={timeFrameDialogData}
      />
    </Dialog>
  )
}
