import React from 'react'

import { useNavigate } from '@tanstack/react-router'

import { Plus, Search } from 'lucide-react'

import { useStoresData, useFilteredPaymentMethodsData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search as SearchComponent } from '@/components/search'
import { TablePagination } from '@/components/table-pagination'
import { ThemeSwitch } from '@/components/theme-switch'
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Table,
  TableBody
} from '@/components/ui'

import {
  PaymentMethodsTableEmpty,
  PaymentMethodsTableHeader,
  PaymentMethodsTableRow,
  PaymentMethodsTableSkeleton
} from './components'

export function PaymentMethodsList() {
  const navigate = useNavigate()
  const [searchQuery, setSearchQuery] = React.useState('')
  const [storeFilter, setStoreFilter] = React.useState<string>('all')

  const {
    data: paymentMethods,
    isLoading,
    error
  } = useFilteredPaymentMethodsData({
    searchTerm: searchQuery,
    storeUid: storeFilter
  })

  const { data: stores = [] } = useStoresData()

  const handleCreateNew = () => {
    navigate({ to: '/setting/payment-method/detail' })
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <SearchComponent />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto space-y-6 py-6'>
          {/* Header with Title, Search, Filter, and Button */}
          <div className='flex items-center gap-4'>
            <h1 className='text-3xl font-bold tracking-tight'>Danh sách PTTT</h1>
            <div className='flex-1'>
              <div className='relative'>
                <Search className='text-muted-foreground absolute top-3 left-3 h-4 w-4' />
                <Input
                  placeholder='Tìm kiếm PTTT'
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className='pl-10'
                />
              </div>
            </div>
            <Select value={storeFilter} onValueChange={setStoreFilter}>
              <SelectTrigger className='w-[200px]'>
                <SelectValue placeholder='Tất cả các điểm' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>Tất cả các điểm</SelectItem>
                {stores.map(store => (
                  <SelectItem key={store.id} value={store.id}>
                    {store.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button onClick={handleCreateNew}>
              <Plus className='mr-2 h-4 w-4' />
              Tạo phương thức mới
            </Button>
          </div>

          {/* Payment Methods Table */}
          {error && <p className='text-sm text-red-600'>{error instanceof Error ? error.message : 'Có lỗi xảy ra'}</p>}

          {isLoading && (
            <div className='rounded-md border'>
              <Table>
                <PaymentMethodsTableSkeleton />
              </Table>
            </div>
          )}

          {!isLoading && paymentMethods.length === 0 && (
            <PaymentMethodsTableEmpty searchQuery={searchQuery} storeFilter={storeFilter} />
          )}

          {!isLoading && paymentMethods.length > 0 && (
            <TablePagination data={paymentMethods} pageSize={20}>
              {(paginatedPaymentMethods, pagination) => (
                <div className='rounded-md border'>
                  <Table>
                    <PaymentMethodsTableHeader />
                    <TableBody>
                      {paginatedPaymentMethods.map((paymentMethod, index) => (
                        <PaymentMethodsTableRow
                          key={paymentMethod.id}
                          paymentMethod={paymentMethod}
                          index={pagination.startIndex + index}
                        />
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </TablePagination>
          )}
        </div>
      </Main>
    </>
  )
}
