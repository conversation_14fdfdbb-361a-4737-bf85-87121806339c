import { z } from 'zod'

export const createFormSchema = (isEditMode: boolean = false) =>
  z
    .object({
      email: z.string().email('Email không hợp lệ'),
      full_name: z.string().min(1, 'Tên nhân viên là bắt buộc'),
      phone: z.string().optional(),
      role_uid: z.string().min(1, 'Ch<PERSON>c vụ là bắt buộc'),
      password: isEditMode ? z.string().optional() : z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
      confirmPassword: isEditMode
        ? z.string().optional()
        : z.string().min(6, '<PERSON><PERSON><PERSON> nhận mật khẩu phải có ít nhất 6 ký tự'),
      brand_access: z.array(z.string()).optional()
    })
    .refine(
      data => {
        if (isEditMode) {
          // In edit mode, only validate password match if password is provided
          if (data.password && data.password.length > 0) {
            return data.password === data.confirmPassword
          }
          // If no password provided, skip validation (both should be empty)
          return !data.confirmPassword || data.confirmPassword.length === 0
        }
        // In create mode, both password and confirmPassword are required and must match
        return data.password === data.confirmPassword
      },
      {
        message: 'Mật khẩu xác nhận không khớp',
        path: ['confirmPassword']
      }
    )

export type CreateEmployeeFormData = z.infer<ReturnType<typeof createFormSchema>>
