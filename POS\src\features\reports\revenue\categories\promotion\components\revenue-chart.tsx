import React from 'react'

import { Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis, CartesianGrid } from 'recharts'

type PromotionPoint = {
  date_label: string
  total_amount: number
  total_bill: number
  discount_amount: number
}

interface RevenueChartProps {
  data: PromotionPoint[]
}

export const RevenueChart: React.FC<RevenueChartProps> = ({ data }) => {
  const chartData = data.map(point => ({
    date: point.date_label,
    revenue: point.total_amount,
    bills: point.total_bill,
    discount: point.discount_amount
  }))

  const maxRevenue = Math.max(...data.map(d => d.total_amount), 1)
  const yAxisMax = Math.ceil(maxRevenue / 1000) * 1000

  return (
    <ResponsiveContainer width='100%' height={300}>
      <LineChart data={chartData}>
        <CartesianGrid strokeDasharray='3 3' stroke='#f0f0f0' />
        <XAxis dataKey='date' stroke='#888888' fontSize={12} tickLine={false} axisLine={false} />
        <YAxis
          stroke='#888888'
          fontSize={12}
          tickLine={false}
          axisLine={false}
          domain={[0, yAxisMax]}
          tickFormatter={value => {
            if (value >= 1000) {
              return `${(value / 1000).toFixed(0)}K`
            }
            return value.toString()
          }}
        />
        <Tooltip
          content={({ active, payload, label }) => {
            if (active && payload && payload.length) {
              return (
                <div className='rounded-lg border bg-white p-3 shadow-lg'>
                  <p className='font-medium text-gray-900'>{label}</p>
                  {payload.map((entry, index) => (
                    <p key={index} className='text-sm text-gray-600'>
                      {entry.name}: {new Intl.NumberFormat('vi-VN').format(entry.value as number)} đ
                    </p>
                  ))}
                </div>
              )
            }
            return null
          }}
        />
        <Line
          type='monotone'
          dataKey='revenue'
          stroke='#3b82f6'
          strokeWidth={2}
          dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
          name='Doanh thu'
        />
      </LineChart>
    </ResponsiveContainer>
  )
}
