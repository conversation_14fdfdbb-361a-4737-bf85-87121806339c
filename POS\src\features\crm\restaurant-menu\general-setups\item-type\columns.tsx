import { ColumnDef } from '@tanstack/react-table'

import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header'

import type { MenuGroup } from '../types'

export const createDanhSachNhomColumns = (): ColumnDef<MenuGroup>[] => {
  const baseColumns: ColumnDef<MenuGroup>[] = [
    {
      id: 'index',
      header: () => <div className='text-left'>#</div>,
      cell: ({ row, table }) => {
        const pageIndex = table.getState().pagination.pageIndex
        const pageSize = table.getState().pagination.pageSize
        return <div className='w-[50px] text-left font-medium'>{pageIndex * pageSize + row.index + 1}</div>
      },
      enableSorting: false,
      enableHiding: false,
      size: 60
    }
  ]

  return [
    ...baseColumns,
    {
      accessorKey: 'code',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Mã nhóm' />
      ),
      cell: ({ row }) => (
        <div className='text-left font-medium'>{row.getValue('code')}</div>
      ),
      enableSorting: true
    },
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Tên nhóm' />
      ),
      cell: ({ row }) => (
        <div className='text-left'>{row.getValue('name')}</div>
      ),
      enableSorting: true
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Trạng thái' />
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as string
        return (
          <div className='text-center'>
            <span className={`text-sm font-medium ${
              status === 'available' ? 'text-green-600' : 'text-red-600'
            }`}>
              {status === 'available' ? 'Có bán' : 'Không bán'}
            </span>
          </div>
        )
      },
      enableSorting: true
    },
    {
      accessorKey: 'order',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Thứ tự' />
      ),
      cell: ({ row }) => (
        <div className='text-center'>{row.getValue('order')}</div>
      ),
      enableSorting: true
    },
    {
      accessorKey: 'maxItems',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Số món tối đa' />
      ),
      cell: ({ row }) => (
        <div className='text-center'>{row.getValue('maxItems') || 0}</div>
      ),
      enableSorting: true
    },
    {
      accessorKey: 'requiredItems',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Số món yêu cầu' />
      ),
      cell: ({ row }) => (
        <div className='text-center'>{row.getValue('requiredItems') || 0}</div>
      ),
      enableSorting: true
    },
    {
      accessorKey: 'lastUpdated',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Thời gian cập nhật' />
      ),
      cell: ({ row }) => (
        <div className='text-left text-sm text-muted-foreground'>
          {row.getValue('lastUpdated')}
        </div>
      ),
      enableSorting: true
    }
  ]
}
