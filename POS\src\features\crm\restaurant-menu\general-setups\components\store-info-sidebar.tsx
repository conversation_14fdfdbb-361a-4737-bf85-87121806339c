import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'

import type { Store } from '../types'
import { storeInfoSchema, type StoreInfoFormData } from '../data/schema'
import { EmailListInput } from './email-list-input'
import { ImageUpload } from './image-upload'
import { AddressMapDisplay } from './address-map-display'

interface StoreInfoSidebarProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  store: Store | null
  onSave: (data: StoreInfoFormData) => void
  isLoading?: boolean
}

export function StoreInfoSidebar({
  open,
  onOpenChange,
  store,
  onSave,
  isLoading = false
}: StoreInfoSidebarProps) {
  const [bannerFile, setBannerFile] = useState<File | null>(null)
  const [bannerPreview, setBannerPreview] = useState<string>('')

  const form = useForm<StoreInfoFormData>({
    resolver: zodResolver(storeInfoSchema),
    defaultValues: {
      name: '',
      address: '',
      phone: '',
      partnerDriverPhone: '',
      active: false,
      onlineSales: false,
      deliverySales: false,
      onlineReservation: false,
      emailList: [],
      banner: ''
    }
  })

  useEffect(() => {
    if (store) {
      form.reset({
        name: store.name || '',
        address: store.address || '',
        phone: store.phone || '',
        partnerDriverPhone: store.partnerDriverPhone || '',
        active: store.active || false,
        onlineSales: store.onlineSales || false,
        deliverySales: store.deliverySales || false,
        onlineReservation: store.onlineReservation || false,
        emailList: store.emailList || [],
        banner: store.banner || ''
      })
      setBannerPreview(store.banner || '')
      setBannerFile(null)
    }
  }, [store, form])

  const handleBannerChange = (file: File | null, preview: string) => {
    setBannerFile(file)
    setBannerPreview(preview)
    form.setValue('banner', preview)
  }

  const handleSubmit = (data: StoreInfoFormData) => {
    const submitData = {
      ...data,
      bannerFile: bannerFile
    }
    onSave(submitData as any)
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-[400px] sm:w-[500px]">
        <SheetHeader className="px-6 bg-blue-600 text-white">
          <SheetTitle className="text-white">Thông tin nhà hàng</SheetTitle>
          <SheetDescription className="text-blue-100">
            Cập nhật thông tin chi tiết của nhà hàng
          </SheetDescription>
        </SheetHeader>

        <ScrollArea className="h-[calc(100vh-80px)]">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6 px-6 py-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên nhà hàng</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Địa chỉ nhà hàng</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2">
                <AddressMapDisplay
                  address={form.watch('address')}
                  height="200px"
                  className="w-full"
                />
              </div>

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SĐT cửa hàng</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="partnerDriverPhone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SĐT để tài xế đối tác liên hệ với cửa hàng</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>ACTIVE</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="onlineSales"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>BÁN HÀNG ONLINE</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="deliverySales"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>BÁN HÀNG GIAO SAU</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="onlineReservation"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>ĐẶT BÀN ONLINE</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="emailList"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Danh sách email</FormLabel>
                    <FormControl>
                      <EmailListInput
                        value={field.value}
                        onChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="banner"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Banner cửa hàng</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={bannerPreview || field.value}
                        onChange={handleBannerChange}
                        disabled={isLoading}
                        placeholder="TẢI ẢNH LÊN"
                        maxSize={10}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="pt-4">
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? 'Đang lưu...' : 'Cập nhật'}
                </Button>
              </div>
            </form>
          </Form>
        </ScrollArea>


      </SheetContent>
    </Sheet>
  )
}
