import { useCurrentBrand } from '@/stores/posStore'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { InvoiceOverview } from './components/invoice-list-overview'

export default function SaleDetailAudit() {
  const { selectedBrand } = useCurrentBrand()

  const brandName = selectedBrand?.name

  const pageTitle = brandName ? `Bảng Kê Chi Tiết Hoá Đơn Bán Hàng - ${brandName}` : 'Bảng Kê Chi Tiết Hoá Đơn Bán Hàng'

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>{pageTitle}</h1>
        </div>
        <Tabs orientation='vertical' defaultValue='overview' className='space-y-4'>
          <div className='w-full overflow-x-auto pb-2'>
            <TabsList>
              <TabsTrigger value='overview'>Tổng Quan</TabsTrigger>
              <TabsTrigger value='stores'>Theo Cửa Hàng</TabsTrigger>
              <TabsTrigger value='products'>Theo Sản Phẩm</TabsTrigger>
              <TabsTrigger value='time'>Theo Thời Gian</TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value='overview' className='space-y-4'>
            <InvoiceOverview />
          </TabsContent>
          <TabsContent value='stores' className='space-y-4'>
            <div className='py-8 text-center'>
              <h3 className='text-lg font-medium'>Báo cáo theo cửa hàng</h3>
              <p className='text-muted-foreground mt-2'>Tính năng đang được phát triển</p>
            </div>
          </TabsContent>
          <TabsContent value='products' className='space-y-4'>
            <div className='py-8 text-center'>
              <h3 className='text-lg font-medium'>Báo cáo theo sản phẩm</h3>
              <p className='text-muted-foreground mt-2'>Tính năng đang được phát triển</p>
            </div>
          </TabsContent>
          <TabsContent value='time' className='space-y-4'>
            <div className='py-8 text-center'>
              <h3 className='text-lg font-medium'>Báo cáo theo thời gian</h3>
              <p className='text-muted-foreground mt-2'>Tính năng đang được phát triển</p>
            </div>
          </TabsContent>
        </Tabs>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Bảng Kê Chi Tiết',
    href: '/bao-cao/ke-toan/hoa-don',
    isActive: true,
    disabled: false
  },
  {
    title: 'Theo Cửa Hàng',
    href: '/bao-cao/ke-toan/hoa-don/cua-hang',
    isActive: false,
    disabled: true
  },
  {
    title: 'Theo Sản Phẩm',
    href: '/bao-cao/ke-toan/hoa-don/san-pham',
    isActive: false,
    disabled: true
  },
  {
    title: 'Theo Thời Gian',
    href: '/bao-cao/ke-toan/hoa-don/thoi-gian',
    isActive: false,
    disabled: true
  }
]
