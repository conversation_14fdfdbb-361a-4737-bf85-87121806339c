import { useDeleteQuantityDay } from '@/hooks/api/use-quantity-days'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { useQuantityDay } from '../context'
import { QuantityDayMutate } from './quantity-day-mutate'
import { ImportFromFileDialog } from './import-from-file-dialog'

export function QuantityDayDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useQuantityDay()
  const { deleteQuantityDay } = useDeleteQuantityDay()

  return (
    <>
      <QuantityDayMutate
        key='quantity-day-create'
        open={open === 'create'}
        onOpenChange={() => setOpen('create')}
      />

      <ImportFromFileDialog
        key='quantity-day-import-from-file'
        open={open === 'import-from-file'}
        onOpenChange={() => setOpen('import-from-file')}
      />

      {currentRow && (
        <>
          <QuantityDayMutate
            key={`quantity-day-update-${currentRow.id}`}
            open={open === 'update'}
            onOpenChange={() => {
              setOpen('update')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />

          <ConfirmDialog
            key='quantity-day-delete'
            destructive
            open={open === 'delete'}
            onOpenChange={() => {
              setOpen('delete')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            handleConfirm={async () => {
              setOpen(null)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
              deleteQuantityDay(currentRow.id)
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá cấu hình này?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}
    </>
  )
}
