import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { ItemTypeActionBar } from './action-bar'

import type { ItemsFilters } from '../types'

interface DanhSachNhomFiltersProps {
  filters: ItemsFilters
  onFiltersChange: (filters: Partial<ItemsFilters>) => void
}

export function DanhSachNhomFilters({ filters, onFiltersChange }: DanhSachNhomFiltersProps) {
  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value })
  }

  const handleStatusChange = (value: string) => {
    onFiltersChange({ status: value })
  }

  const handleOrderChange = (value: string) => {
    onFiltersChange({ order: value })
  }

  const handleFilter = () => {
    console.log('Applying filters:', filters)
  }

  const handleViewPreview = () => {
    console.log('Xem trước')
  }

  const handleCreateItem = () => {
    console.log('Tạo nhóm món')
  }

  const handleSyncItems = () => {
    console.log('Đồng bộ nhóm món')
  }

  const getItemCount = () => {
    // Mock count for groups
    return 1
  }

  return (
    <div className='space-y-4 mb-6'>
      {/* Filters */}
      <div className='flex items-center gap-2'>
        <div className='w-[220px]'>
          <Input
            placeholder='TÊN/MÃ NHÓM'
            value={filters.search}
            onChange={(e) => handleSearchChange(e.target.value)}
            className='h-10'
          />
        </div>
        <div className='w-[160px]'>
          <Select value={filters.status} onValueChange={handleStatusChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='TRẠNG THÁI' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='available'>Có bán</SelectItem>
              <SelectItem value='unavailable'>Hết hàng</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='w-[140px]'>
          <Select value={filters.order} onValueChange={handleOrderChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='THỨ TỰ' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='asc'>Tăng dần</SelectItem>
              <SelectItem value='desc'>Giảm dần</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='ml-2'>
          <Button onClick={handleFilter} className='h-10 px-6 bg-blue-600 text-white hover:bg-blue-700'>
            Lọc
          </Button>
        </div>
      </div>

      {/* Actions */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div className='flex items-center gap-4'>
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium'>SỐ LƯỢNG: {getItemCount()}</span>
            <button className='text-blue-600 text-sm hover:underline'>
              Sắp xếp thứ tự hiển thị
            </button>
          </div>
        </div>

        <ItemTypeActionBar
          onSyncItems={handleSyncItems}
          onViewPreview={handleViewPreview}
          onCreateItem={handleCreateItem}
        />
      </div>
    </div>
  )
}
