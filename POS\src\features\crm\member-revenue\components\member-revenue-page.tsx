import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ComposedChart, Bar, Line, XAxis, YAxis, ResponsiveContainer, ReferenceLine, Legend } from 'recharts'
import { format, subDays, startOfDay, endOfDay } from 'date-fns'
import { DateRangePicker } from '../../customer-report/components'

// Empty data structure
const mockData = {
  amount_before_discount: 0,
  number_of_bill: 0,
  amount_after_discount: 0,
  amount_average_bill: 0,
  amount_discount: 0,
  report_by_pos: [
    {
      store_name: "Cửa hàng A",
      amount_after_discount: 5000000,
      amount_discount: 500000,
      number_of_bill: 25,
      amount_average_bill: 200000
    },
    {
      store_name: "Cửa hàng B",
      amount_after_discount: 3500000,
      amount_discount: 350000,
      number_of_bill: 18,
      amount_average_bill: 194444
    },
    {
      store_name: "Cửa hàng C",
      amount_after_discount: 7200000,
      amount_discount: 800000,
      number_of_bill: 40,
      amount_average_bill: 180000
    }
  ],
  report_by_date: []
}

const chartData: any[] = []

export function MemberRevenuePage() {
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)
  const [dateRange, setDateRange] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [activePreset, setActivePreset] = useState<string>('7days')

  // Initialize with 7 days preset
  useEffect(() => {
    handlePresetDate('7days')
  }, [])

  // Format date range for display
  const formatDateRange = (start: Date, end: Date) => {
    return `${format(start, 'dd/MM/yyyy')} - ${format(end, 'dd/MM/yyyy')}`
  }

  // Handle preset date selections
  const handlePresetDate = (preset: string) => {
    const today = new Date()
    let start: Date
    let end: Date = endOfDay(today)

    switch (preset) {
      case 'today':
        start = startOfDay(today)
        break
      case 'yesterday':
        start = startOfDay(subDays(today, 1))
        end = endOfDay(subDays(today, 1))
        break
      case '7days':
        start = startOfDay(subDays(today, 6))
        break
      case '15days':
        start = startOfDay(subDays(today, 14))
        break
      case '30days':
        start = startOfDay(subDays(today, 29))
        break
      default:
        return
    }

    setStartDate(start)
    setEndDate(end)
    setDateRange(formatDateRange(start, end))
    setActivePreset(preset)
  }

  // Handle manual date input
  const handleDateRangeChange = (value: string) => {
    setDateRange(value)
    setActivePreset('')

    // Try to parse the date range format "dd/mm/yyyy - dd/mm/yyyy"
    const dateRangeRegex = /^(\d{2}\/\d{2}\/\d{4})\s*-\s*(\d{2}\/\d{2}\/\d{4})$/
    const match = value.match(dateRangeRegex)

    if (match) {
      try {
        const [, startStr, endStr] = match
        const [startDay, startMonth, startYear] = startStr.split('/').map(Number)
        const [endDay, endMonth, endYear] = endStr.split('/').map(Number)

        const start = new Date(startYear, startMonth - 1, startDay)
        const end = new Date(endYear, endMonth - 1, endDay)

        if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
          setStartDate(start)
          setEndDate(end)
        }
      } catch (error) {
        console.error('Invalid date format:', error)
      }
    }
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('vi-VN')} VND`
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Doanh thu thành viên</h1>
        <p className="text-gray-600 mt-2">
          Theo dõi và phân tích doanh thu từ các thành viên
        </p>
      </div>

      {/* Date Filter */}
      <div className="flex justify-end items-center gap-2 mb-6">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === 'today' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('today')}
          >
            Hôm nay
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === 'yesterday' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('yesterday')}
          >
            Hôm qua
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '7days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('7days')}
          >
            7 ngày trước
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '15days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('15days')}
          >
            15 ngày trước
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '30days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('30days')}
          >
            30 ngày trước
          </Button>
        </div>
        <DateRangePicker
          startDate={startDate}
          endDate={endDate}
          onDateChange={(start, end) => {
            setStartDate(start)
            setEndDate(end)
            if (start && end) {
              setDateRange(formatDateRange(start, end))
              setActivePreset('')
            }
          }}
          dateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
        />
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{formatCurrency(mockData.amount_after_discount)}</div>
            <div className="text-sm text-gray-600 mt-1">DOANH THU</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{formatCurrency(mockData.amount_discount)}</div>
            <div className="text-sm text-gray-600 mt-1">GIẢM GIÁ</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{mockData.number_of_bill}</div>
            <div className="text-sm text-gray-600 mt-1">SỐ LƯỢNG HÓA ĐƠN</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{formatCurrency(mockData.amount_average_bill)}</div>
            <div className="text-sm text-gray-600 mt-1">BÌNH QUÂN HÓA ĐƠN</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Chart */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex justify-center gap-6 mb-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-sm text-gray-600">Số lượng hóa đơn</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span className="text-sm text-gray-600">Doanh thu</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500 rounded"></div>
              <span className="text-sm text-gray-600">Giảm giá</span>
            </div>
          </div>
          <div className="h-[600px] relative">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={chartData} margin={{ top: 15, right: 50, left: 90, bottom: 25 }}>
                {/* Grid lines for left Y-axis (0.1, 0.2, 0.3...) */}
                {[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0].map(value => (
                  <ReferenceLine
                    key={`left-${value}`}
                    y={value}
                    stroke="#e5e7eb"
                    strokeDasharray="none"
                    yAxisId="left"
                  />
                ))}
                {/* Grid lines for right Y-axis (0.1, 0.2, 0.3...) */}
                {[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0].map(value => (
                  <ReferenceLine
                    key={`right-${value}`}
                    y={value}
                    stroke="#e5e7eb"
                    strokeDasharray="none"
                    yAxisId="right"
                  />
                ))}
                {/* Baseline at y=0 for both axes */}
                <ReferenceLine
                  y={0}
                  stroke="#9ca3af"
                  strokeWidth={1}
                  yAxisId="left"
                />
                <ReferenceLine
                  y={0}
                  stroke="#9ca3af"
                  strokeWidth={1}
                  yAxisId="right"
                />
                <XAxis
                  dataKey="name"
                  axisLine={true}
                  tickLine={true}
                  tick={{ fontSize: 14, fill: '#6b7280' }}
                  stroke="#9ca3af"
                  strokeWidth={1}
                />
                {/* Left Y-axis for revenue */}
                <YAxis
                  yAxisId="left"
                  domain={[0, 1]}
                  ticks={[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]}
                  tickFormatter={(value) => value.toFixed(1)}
                  axisLine={true}
                  tickLine={true}
                  tick={{ fontSize: 14, fill: '#6b7280' }}
                  width={40}
                  stroke="#9ca3af"
                  strokeWidth={1}
                />
                {/* Right Y-axis for number of bills */}
                <YAxis
                  yAxisId="right"
                  orientation="right"
                  domain={[0, 1]}
                  ticks={[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]}
                  tickFormatter={(value) => value.toFixed(1)}
                  axisLine={true}
                  tickLine={true}
                  tick={{ fontSize: 14, fill: '#6b7280' }}
                  width={40}
                  stroke="#9ca3af"
                  strokeWidth={1}
                />
                <Legend
                  wrapperStyle={{ fontSize: '16px', paddingTop: '20px' }}
                  iconType="rect"
                />
                {chartData.length > 0 && (
                  <>
                    <Bar
                      yAxisId="left"
                      dataKey="total_amount"
                      fill="#3b82f6"
                      name="Doanh thu"
                      barSize={60}
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="number_of_bills"
                      stroke="#22c55e"
                      strokeWidth={3}
                      dot={{ fill: '#22c55e', strokeWidth: 2, r: 6 }}
                    />
                  </>
                )}
              </ComposedChart>
            </ResponsiveContainer>
            {/* Y-axis labels */}
            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 -rotate-90">
              <span className="text-sm text-gray-600 whitespace-nowrap">DOANH THU/GIẢM GIÁ</span>
            </div>
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 rotate-90">
              <span className="text-sm text-gray-600 whitespace-nowrap">HÓA ĐƠN</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bottom Table */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            className="bg-blue-500 text-white hover:bg-blue-600"
          >
            Xuất file
          </Button>
          <CardTitle className="text-base font-medium text-gray-700 flex-1 text-center">
            DOANH THU THEO CỬA HÀNG
          </CardTitle>
          <Input
            placeholder="Tìm kiếm nhanh"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
        </CardHeader>
        <CardContent>
          <div className="overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="text-left p-3 text-sm font-medium text-gray-600">Cửa hàng</th>
                  <th className="text-left p-3 text-sm font-medium text-gray-600">Doanh thu</th>
                  <th className="text-left p-3 text-sm font-medium text-gray-600">Giảm giá</th>
                  <th className="text-left p-3 text-sm font-medium text-gray-600">Số lượng hóa đơn</th>
                  <th className="text-left p-3 text-sm font-medium text-gray-600">Bình quân hóa đơn</th>
                </tr>
              </thead>
              <tbody>
                {mockData.report_by_pos && mockData.report_by_pos.length > 0 ? (
                  mockData.report_by_pos
                    .filter((store: any) =>
                      !searchTerm ||
                      store.store_name?.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .map((store: any, index: number) => (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="p-3 text-sm text-gray-900">{store.store_name || 'N/A'}</td>
                        <td className="p-3 text-sm text-gray-900">{formatCurrency(store.amount_after_discount || 0)}</td>
                        <td className="p-3 text-sm text-gray-900">{formatCurrency(store.amount_discount || 0)}</td>
                        <td className="p-3 text-sm text-gray-900">{store.number_of_bill || 0}</td>
                        <td className="p-3 text-sm text-gray-900">{formatCurrency(store.amount_average_bill || 0)}</td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={5} className="text-center p-8 text-gray-500">
                      No data available in table
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
