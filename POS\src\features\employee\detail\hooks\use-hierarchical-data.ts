import { useMemo } from 'react'

import type { Brand, City, Store } from '@/types/auth'

import { useAuthStore } from '@/stores/authStore'

export interface BrandWithCities extends Brand {
  cities: (City & { stores: Store[] })[]
}

export function useHierarchicalData() {
  const { brands, cities, stores } = useAuthStore(state => state.auth)

  const hierarchicalData = useMemo((): BrandWithCities[] => {
    if (!brands || !cities || !stores) return []

    return brands
      .map((brand): BrandWithCities => {
        const brandCities = cities
          .filter(city => city.active === 1)
          .map(city => ({
            ...city,
            stores: stores.filter(
              store => store.active === 1 && store.brand_uid === brand.id && store.city_uid === city.id
            )
          }))
          .filter(city => city.stores.length > 0)

        return {
          ...brand,
          cities: brandCities
        }
      })
      .filter(brand => brand.cities.length > 0)
  }, [brands, cities, stores])

  const findBrandById = (brandId: string) => {
    return hierarchicalData.find(brand => brand.id === brandId)
  }

  const findCityById = (cityId: string) => {
    return hierarchicalData.flatMap(brand => brand.cities).find(city => city.id === cityId)
  }

  const findStoreById = (storeId: string) => {
    return hierarchicalData
      .flatMap(brand => brand.cities)
      .flatMap(city => city.stores)
      .find(store => store.id === storeId)
  }

  const findBrandByCity = (cityId: string) => {
    return hierarchicalData.find(brand => brand.cities.some(city => city.id === cityId))
  }

  const findCityByStore = (storeId: string) => {
    return hierarchicalData.flatMap(brand => brand.cities).find(city => city.stores.some(store => store.id === storeId))
  }

  const findBrandByStore = (storeId: string) => {
    return hierarchicalData.find(brand => brand.cities.some(city => city.stores.some(store => store.id === storeId)))
  }

  const getStoresByBrand = (brandId: string) => {
    const brand = findBrandById(brandId)
    return brand ? brand.cities.flatMap(city => city.stores) : []
  }

  const getStoresByCity = (cityId: string) => {
    const city = findCityById(cityId)
    return city ? city.stores : []
  }

  const filterHierarchicalData = (searchTerm: string): BrandWithCities[] => {
    if (!searchTerm) return hierarchicalData

    return hierarchicalData
      .map(brand => ({
        ...brand,
        cities: brand.cities
          .map(city => ({
            ...city,
            stores: city.stores.filter(store => store.store_name.toLowerCase().includes(searchTerm.toLowerCase()))
          }))
          .filter(city => city.city_name.toLowerCase().includes(searchTerm.toLowerCase()) || city.stores.length > 0)
      }))
      .filter(brand => brand.brand_name.toLowerCase().includes(searchTerm.toLowerCase()) || brand.cities.length > 0)
  }

  return {
    hierarchicalData,
    brands,
    cities,
    stores,
    findBrandById,
    findCityById,
    findStoreById,
    findBrandByCity,
    findCityByStore,
    findBrandByStore,
    getStoresByBrand,
    getStoresByCity,
    filterHierarchicalData,
    isLoading: !brands || !cities || !stores,
    isEmpty: hierarchicalData.length === 0
  }
}
