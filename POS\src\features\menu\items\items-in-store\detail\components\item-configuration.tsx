import { useState } from 'react'

import type { UseFormReturn } from 'react-hook-form'
import { useWatch } from 'react-hook-form'

import { useNavigate } from '@tanstack/react-router'

import { HelpCircle, ChevronDown, ChevronRight, X } from 'lucide-react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

import { ConfirmDialog } from '@/components/confirm-dialog'
import { Combobox } from '@/components/pos/combobox'

import { useItemConfigurationData, useItemConfigurationState } from '../../hooks'
import { BuffetConfigModal } from '../../list'
import { getSourceName } from '../../utils/utils'
import { PriceSourceDialog } from './price-source-dialog'

interface Props {
  form: UseFormReturn<any>
}

export function ItemConfiguration({ form }: Props) {
  const navigate = useNavigate()

  const [isVirtualItem, isBuffetItem, crossPrice, priceBySource, excludeItemsBuffet, upSizeBuffet] = useWatch({
    control: form.control,
    name: [
      'is_virtual_item',
      'is_buffet_item',
      'cross_price',
      'price_by_source',
      'exclude_items_buffet',
      'up_size_buffet'
    ]
  })

  const { selectedCustomizationUid, customizations, customizationDetails, items, sourcesData } =
    useItemConfigurationData({ form })

  const {
    showQuantityInputs,
    isCustomizationDetailsOpen,
    isPriceSourceDialogOpen,
    isBuffetConfigModalOpen,
    setIsCustomizationDetailsOpen,
    setIsPriceSourceDialogOpen,
    setIsBuffetConfigModalOpen,
    openPriceSourceDialog,
    openBuffetConfigModal,
    toggleQuantityInputs,
    handleRemovePriceSource,
    clearConfirmDelete,
    confirmDeleteIndex
  } = useItemConfigurationState()

  const [editingPriceSourceIndex, setEditingPriceSourceIndex] = useState<number | null>(null)
  const [editingPriceSourceData, setEditingPriceSourceData] = useState<any>(null)

  const handlePriceSourceConfirm = (data: {
    source_id?: string
    price: number
    source_name?: string
    price_times?: any[]
    is_source_exist_in_city?: boolean
  }) => {
    const current = form.getValues('price_by_source') || []

    if (editingPriceSourceIndex !== null) {
      const next = [...current]
      next[editingPriceSourceIndex] = {
        source_id: data.source_id,
        price: data.price,
        source_name: data.source_name,
        price_times: data.price_times,
        is_source_exist_in_city: data.is_source_exist_in_city
      }
      form.setValue('price_by_source', next)
      setEditingPriceSourceIndex(null)
      setEditingPriceSourceData(null)
    } else {
      const next = [
        ...current,
        {
          source_id: data.source_id,
          price: data.price,
          source_name: data.source_name,
          price_times: data.price_times,
          is_source_exist_in_city: data.is_source_exist_in_city
        }
      ]
      form.setValue('price_by_source', next)
    }
  }

  const handleEditPriceSource = (source: any, index: number) => {
    setEditingPriceSourceIndex(index)
    setEditingPriceSourceData(source)
    setIsPriceSourceDialogOpen(true)
  }

  const handleBuffetConfigChange = (items: string[]) => {
    form.setValue('exclude_items_buffet', items)
  }

  return (
    <div className='space-y-6'>
      {/* Cấu hình section */}
      <div className='space-y-6'>
        <h3 className='text-lg font-semibold text-gray-900'>Cấu hình sửa giá, nhập số lượng, bỏ món</h3>
        <div className='space-y-4'>
          {/* Cho phép sửa giá khi bán - Bit 2 */}
          <div className='grid grid-cols-3 items-center gap-4'>
            <FormLabel className='text-right font-medium text-gray-700'>Cho phép sửa giá khi bán</FormLabel>
            <div className='col-span-2'>
              <FormField
                control={form.control}
                name='enable_edit_price'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Checkbox
                        checked={(field.value & 2) === 2}
                        onCheckedChange={checked => {
                          const newValue = checked ? field.value | 2 : field.value & ~2
                          field.onChange(newValue)
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Yêu cầu nhập số lượng khi gọi món - Bit 4 */}
          <div className='grid grid-cols-3 items-center gap-4'>
            <FormLabel className='text-right font-medium text-gray-700'>Yêu cầu nhập số lượng khi gọi món</FormLabel>
            <div className='col-span-2'>
              <FormField
                control={form.control}
                name='enable_edit_price'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Checkbox
                        checked={(field.value & 4) === 4}
                        onCheckedChange={checked => {
                          const newValue = checked ? field.value | 4 : field.value & ~4
                          field.onChange(newValue)
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Cho phép bỏ món mà không cần quyền - Bit 8 */}
          <div className='grid grid-cols-3 items-center gap-4'>
            <FormLabel className='text-left font-medium text-gray-700'>
              Cho phép bỏ món mà không cần quyền áp dụng
            </FormLabel>
            <div className='col-span-2'>
              <FormField
                control={form.control}
                name='enable_edit_price'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Checkbox
                        checked={(field.value & 8) === 8}
                        onCheckedChange={checked => {
                          const newValue = checked ? field.value | 8 : field.value & ~8
                          field.onChange(newValue)
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>

        <div className='space-y-4 border-t pt-4'>
          {/* Cấu hình món ảo */}
          <div className='grid grid-cols-3 items-center gap-4'>
            <FormLabel className='text-right font-medium text-gray-700'>Cấu hình món ảo</FormLabel>
            <div className='col-span-2'>
              <FormField
                control={form.control}
                name='is_virtual_item'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Checkbox checked={!!field.value} onCheckedChange={checked => field.onChange(checked ? 1 : 0)} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Cấu hình món ăn là vé buffet */}
          <div className='grid grid-cols-3 items-center gap-4'>
            <FormLabel className='text-right font-medium text-gray-700'>Cấu hình món ăn là vé buffet</FormLabel>
            <div className='col-span-2'>
              <FormField
                control={form.control}
                name='is_buffet_item'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Checkbox checked={!!field.value} onCheckedChange={checked => field.onChange(checked ? 1 : 0)} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* InQR Formula */}
          <div className='grid grid-cols-3 items-center gap-4'>
            <FormLabel className='text-right font-medium text-gray-700'>Công thức inQr cho máy pha trà</FormLabel>
            <div className='col-span-2'>
              <FormField
                control={form.control}
                name='formula_qrcode'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input placeholder='Nhập công thức InQR' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Buffet Configuration - chỉ hiện khi check vào is_buffet_item */}
          {isBuffetItem === 1 && (
            <div className='space-y-4'>
              <div className='grid grid-cols-3 items-center gap-4'>
                <FormLabel className='text-right font-medium text-gray-700'>
                  Danh sách món không đi kèm vé buffet
                </FormLabel>
                <div className='col-span-2'>
                  <Button
                    type='button'
                    variant='outline'
                    className='w-full justify-start text-blue-600'
                    onClick={openBuffetConfigModal}
                  >
                    {excludeItemsBuffet.length} món
                  </Button>
                </div>
              </div>

              <div className='grid grid-cols-3 items-center gap-4'>
                <FormLabel className='text-right font-medium text-gray-700'>Danh sách vé buffet được upsize</FormLabel>
                <div className='col-span-2'>
                  <Button
                    type='button'
                    variant='outline'
                    className='w-full justify-start text-blue-600'
                    onClick={() => {}}
                  >
                    {upSizeBuffet.length} món
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Cấu hình món dịch vụ */}
        <div className='space-y-6'>
          <h3 className='text-lg font-semibold text-gray-900'>Cấu hình món dịch vụ</h3>
          <div className='grid grid-cols-3 items-center gap-4'>
            <FormLabel className='text-right font-medium text-gray-700'>Cấu hình món dịch vụ</FormLabel>
            <div className='col-span-2'>
              <FormField
                control={form.control}
                name='is_service'
                render={({ field }) => (
                  <FormItem>
                    <div className='flex items-center gap-4'>
                      <FormControl>
                        <Checkbox
                          checked={Boolean(field.value)}
                          onCheckedChange={checked => field.onChange(Boolean(checked))}
                        />
                      </FormControl>
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className='space-y-4'>
            {/* Conditional section when service is enabled */}
            {form.watch('is_service') ? (
              <div className='space-y-4 pt-4'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center space-x-2'>
                    <span className='text-sm font-medium text-gray-700'>Cấu hình giá thay đổi theo số lượng</span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            Nếu khai báo cấu hình đổi giá theo số lượng thì món sẽ ko tự động áp dụng giá và giảm giá
                            theo thời gian khi chạy của món dịch vụ nữa, chỉ được chọn 1 trong 2
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Button
                    type='button'
                    size='sm'
                    variant={showQuantityInputs || crossPrice.length > 0 ? 'outline' : 'default'}
                    className={showQuantityInputs || crossPrice.length > 0 ? '' : 'bg-blue-500 hover:bg-blue-600'}
                    onClick={toggleQuantityInputs}
                  >
                    {showQuantityInputs || crossPrice.length > 0 ? 'Xoá' : 'Thêm'}
                  </Button>
                </div>

                {(showQuantityInputs || crossPrice.length > 0) && (
                  <div className='space-y-4'>
                    <div className='space-y-4'>
                      <div className='grid grid-cols-3 items-center gap-4'>
                        <FormLabel className='text-right font-medium text-gray-700'>Khai báo số lượng</FormLabel>
                        <div className='col-span-2'>
                          <FormField
                            control={form.control}
                            name={'quantity'}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type='number'
                                    placeholder='1'
                                    className='bg-white'
                                    {...field}
                                    value={field.value || ''}
                                    onChange={e => {
                                      const quantity = Number(e.target.value) || 0
                                      field.onChange(quantity)
                                      const currentPrice = form.getValues('price') || 0
                                      form.setValue('cross_price', [
                                        {
                                          quantity: quantity,
                                          price: currentPrice
                                        }
                                      ])
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                      <div className='grid grid-cols-3 items-center gap-4'>
                        <FormLabel className='text-right font-medium text-gray-700'>
                          Khai báo giá khi vượt qua số lượng trên
                        </FormLabel>
                        <div className='col-span-2'>
                          <FormField
                            control={form.control}
                            name={'price'}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    placeholder='0'
                                    className='bg-white'
                                    value={
                                      typeof field.value === 'number' && field.value > 0
                                        ? field.value.toLocaleString('vi-VN')
                                        : ''
                                    }
                                    onChange={e => {
                                      const digitsOnly = e.target.value.replace(/\D/g, '')
                                      const price = digitsOnly ? parseInt(digitsOnly, 10) : 0
                                      field.onChange(price)
                                      const currentQuantity = form.getValues('quantity') || 0
                                      form.setValue('cross_price', [
                                        {
                                          quantity: currentQuantity,
                                          price: price
                                        }
                                      ])
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : null}

            {/* Cấu hình giá theo nguồn - chỉ hiện khi không phải món ảo */}
            {!isVirtualItem && (
              <div className='space-y-6'>
                <h3 className='text-lg font-semibold text-gray-900'>Cấu hình giá theo nguồn</h3>
                <div className='space-y-4'>
                  <div className='flex items-center justify-between'>
                    <FormLabel className='font-medium text-gray-700'>Cấu hình giá theo nguồn</FormLabel>
                    <Button
                      type='button'
                      size='sm'
                      className='bg-blue-500 hover:bg-blue-600'
                      onClick={openPriceSourceDialog}
                    >
                      Thêm nguồn
                    </Button>
                  </div>

                  {/* Hiển thị danh sách price sources */}
                  {priceBySource.length > 0 && (
                    <div className='space-y-2'>
                      {priceBySource.map((source: any, index: number) => (
                        <div
                          key={index}
                          className='flex cursor-pointer items-center justify-between rounded-md border p-3 hover:bg-gray-50'
                          onClick={() => handleEditPriceSource(source, index)}
                        >
                          <div>
                            <span className='font-medium'>
                              {getSourceName(source.source_id, sourcesData) || source.sourceName} - Số tiền:{' '}
                              {source.price.toLocaleString()} ₫
                            </span>
                          </div>
                          <div className='flex items-center gap-2'>
                            <Button
                              type='button'
                              variant='ghost'
                              size='sm'
                              onClick={e => {
                                e.stopPropagation()
                                handleEditPriceSource(source, index)
                              }}
                              className='text-blue-600 hover:text-blue-700'
                            >
                              Sửa
                            </Button>
                            <Button
                              type='button'
                              variant='ghost'
                              size='sm'
                              onClick={e => {
                                e.stopPropagation()
                                handleRemovePriceSource(index)
                              }}
                              className='text-gray-400 hover:text-gray-600'
                            >
                              <X className='h-4 w-4' />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Customization */}
            <div className='space-y-6'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-semibold text-gray-900'>Customization</h3>
                <Button
                  type='button'
                  size='sm'
                  className='bg-blue-500 hover:bg-blue-600'
                  onClick={() => navigate({ to: '/menu/customization/customization-in-store/detail' })}
                >
                  Tạo customization
                </Button>
              </div>
              <div className='space-y-4'>
                <div className='grid grid-cols-3 items-center gap-4'>
                  <FormLabel className='text-right font-medium text-gray-700'>Customization</FormLabel>
                  <div className='col-span-2'>
                    <FormField
                      control={form.control}
                      name='customization_uid'
                      render={({ field }) => (
                        <FormItem>
                          <Combobox
                            options={[
                              { value: 'none', label: 'Không có' },
                              ...customizations.map(c => ({ value: c.id, label: c.name }))
                            ]}
                            value={field.value ?? 'none'}
                            onValueChange={val => field.onChange(val === 'none' ? null : val)}
                            placeholder='Chọn customization'
                            searchPlaceholder='Tìm kiếm customization...'
                            emptyText='Không tìm thấy customization.'
                            className='w-full'
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Chi tiết Customize */}
                {selectedCustomizationUid && selectedCustomizationUid !== 'none' && customizationDetails && (
                  <div className='space-y-4'>
                    <Collapsible open={isCustomizationDetailsOpen} onOpenChange={setIsCustomizationDetailsOpen}>
                      <CollapsibleTrigger asChild>
                        <Button
                          type='button'
                          variant='ghost'
                          className='flex w-full items-center justify-between p-2 text-left'
                        >
                          <span className='text-blue-600'>Chi tiết Customize</span>
                          {isCustomizationDetailsOpen ? (
                            <ChevronDown className='h-4 w-4 text-blue-600' />
                          ) : (
                            <ChevronRight className='h-4 w-4 text-blue-600' />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                      <CollapsibleContent className='space-y-4'>
                        {customizationDetails.data?.LstItem_Options?.map(group => {
                          const groupItems = group.LstItem_Id.map(itemId => {
                            const foundItem = items.find((apiItem: any) => apiItem.item_id === itemId)
                            return {
                              id: foundItem?.id || itemId,
                              name: foundItem?.item_name || itemId,
                              price: foundItem?.ots_price || 0,
                              code: itemId,
                              active: foundItem?.active ?? 1
                            }
                          })

                          return (
                            <div key={group.id} className='space-y-3'>
                              <div>
                                <span className='font-medium'>{group.Name}</span>
                                <span className='ml-2 text-sm text-gray-500'>
                                  (Chọn từ {group.Min_Permitted} đến {group.Max_Permitted} món)
                                </span>
                              </div>

                              <div className='grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4'>
                                {groupItems.map(item => {
                                  const isActive = (item as any).active === 1
                                  return (
                                    <div
                                      key={item.id}
                                      className={`rounded-md border p-3 text-center ${
                                        isActive ? 'bg-gray-50' : 'cursor-not-allowed bg-gray-100 opacity-50'
                                      }`}
                                    >
                                      <p className={`text-sm font-medium ${isActive ? '' : 'text-gray-400'}`}>
                                        {item.name}
                                      </p>
                                      <p className='mt-1 text-xs text-gray-500'>({item.code})</p>
                                      <p
                                        className={`mt-1 text-sm font-medium ${
                                          isActive ? 'text-green-600' : 'text-gray-400'
                                        }`}
                                      >
                                        {item.price.toLocaleString('vi-VN', {
                                          minimumFractionDigits: 0,
                                          maximumFractionDigits: 0
                                        })}{' '}
                                        ₫
                                      </p>
                                    </div>
                                  )
                                })}
                              </div>
                            </div>
                          )
                        })}
                      </CollapsibleContent>
                    </Collapsible>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Khung thời gian bán - giữ nguyên layout như cũ */}
          <div className='space-y-4'>
            <FormLabel className='text-sm font-medium text-gray-700'>Khung thời gian bán</FormLabel>

            {/* Chọn ngày */}
            <FormField
              control={form.control}
              name='time_sale_date_week'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-sm text-gray-600'>Chọn ngày</FormLabel>
                  <div className='grid grid-cols-7 gap-2'>
                    {[
                      { name: 'Thứ 2', bit: 4 },
                      { name: 'Thứ 3', bit: 8 },
                      { name: 'Thứ 4', bit: 16 },
                      { name: 'Thứ 5', bit: 32 },
                      { name: 'Thứ 6', bit: 64 },
                      { name: 'Thứ 7', bit: 128 },
                      { name: 'Chủ nhật', bit: 2 }
                    ].map(({ name, bit }) => {
                      const bitValue = bit
                      const isSelected = (field.value & bitValue) !== 0
                      return (
                        <Button
                          key={name}
                          type='button'
                          variant={isSelected ? 'default' : 'outline'}
                          size='sm'
                          className={`text-xs ${isSelected ? 'bg-blue-600 text-white' : 'border-blue-600 text-blue-600'}`}
                          onClick={() => {
                            const currentValue = field.value || 0
                            const newValue = isSelected ? currentValue & ~bitValue : currentValue | bitValue
                            field.onChange(newValue)
                          }}
                        >
                          {name}
                        </Button>
                      )
                    })}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Chọn giờ */}
            <FormField
              control={form.control}
              name='time_sale_hour_day'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-sm text-gray-600'>Chọn giờ</FormLabel>
                  <div className='grid grid-cols-10 gap-2'>
                    {Array.from({ length: 24 }, (_, i) => ({ hour: i, label: `${i}h` })).map(({ hour, label }) => {
                      const bitValue = 1 << hour
                      const isSelected = (field.value & bitValue) !== 0
                      return (
                        <Button
                          key={hour}
                          type='button'
                          variant={isSelected ? 'default' : 'outline'}
                          size='sm'
                          className={`text-xs ${isSelected ? 'bg-blue-600 text-white' : 'border-blue-600 text-blue-600'}`}
                          onClick={() => {
                            const currentValue = field.value || 0
                            const newValue = isSelected
                              ? currentValue & ~bitValue // Remove bit
                              : currentValue | bitValue // Add bit
                            field.onChange(newValue)
                          }}
                        >
                          {label}
                        </Button>
                      )
                    })}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Thứ tự hiển thị */}
          <div className='space-y-4'>
            <div className='grid grid-cols-3 items-center gap-4'>
              <FormLabel className='text-right font-medium text-gray-700'>Thứ tự hiển thị</FormLabel>
              <div className='col-span-2'>
                <FormField
                  control={form.control}
                  name='sort'
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type='number'
                          placeholder='Nhập số thứ tự hiển thị'
                          {...field}
                          onChange={e => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <PriceSourceDialog
        open={isPriceSourceDialogOpen}
        onOpenChange={setIsPriceSourceDialogOpen}
        onConfirm={handlePriceSourceConfirm}
        sources={sourcesData}
        data={editingPriceSourceData}
      />

      <BuffetConfigModal
        itemsBuffet={excludeItemsBuffet || []}
        open={isBuffetConfigModalOpen}
        onOpenChange={setIsBuffetConfigModalOpen}
        onItemsChange={handleBuffetConfigChange}
        items={items}
      />

      <ConfirmDialog
        open={confirmDeleteIndex !== null}
        onOpenChange={open => !open && clearConfirmDelete()}
        title='Bạn có muốn bỏ cấu hình ?'
        desc=''
        confirmText='Xóa'
        cancelBtnText='Hủy'
        handleConfirm={() => {
          if (confirmDeleteIndex === null) return
          const current = form.getValues('price_by_source') || []
          const next = current.filter((_: unknown, i: number) => i !== confirmDeleteIndex)
          form.setValue('price_by_source', next)
          clearConfirmDelete()
        }}
      />
    </div>
  )
}
