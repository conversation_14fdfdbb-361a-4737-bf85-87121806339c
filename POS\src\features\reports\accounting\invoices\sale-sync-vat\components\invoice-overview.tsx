import { useState, useMemo } from 'react'
import {
  getDefaultMonthlyDateRange,
  getDefaultDailyDateRange,
} from '@/lib/date-utils'
import { ReportsContext } from '@/hooks/use-reports-context'
import { FiltersPanel } from './filters-panel'
import { SaleNotSyncVatList } from './sale-not-sync-vat-list'

export function InvoiceOverview() {
  const [filterType, setFilterType] = useState<'monthly' | 'daily'>('daily')

  const [dateRange, setDateRange] = useState<{
    from: Date
    to: Date
  }>(getDefaultDailyDateRange())
  const [selectedStores, setSelectedStores] = useState<string[]>(['all-stores'])
  const [selectedSources, setSelectedSources] = useState<string[]>([
    'all-sources',
  ])

  const handleFilterTypeChange = (newFilterType: 'monthly' | 'daily') => {
    setFilterType(newFilterType)

    if (newFilterType === 'monthly') {
      setDateRange(getDefaultMonthlyDateRange())
    } else if (newFilterType === 'daily') {
      setDateRange(getDefaultDailyDateRange())
    }
  }

  const contextValue = useMemo(
    () => ({
      dateRange,
      filterType,
      selectedStores,
      selectedSources,
    }),
    [dateRange, filterType, selectedStores, selectedSources]
  )

  return (
    <ReportsContext.Provider value={contextValue}>
      <div className='space-y-6'>
        {/* Filters Panel */}
        <FiltersPanel
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
          selectedStores={selectedStores}
          onStoreChange={setSelectedStores}
          selectedSources={selectedSources}
          onSourceChange={setSelectedSources}
          filterType={filterType}
          onFilterTypeChange={handleFilterTypeChange}
        />

        {/* Charts Section */}
        <div className='grid gap-6'>
          <div className='col-span-full'>
            <SaleNotSyncVatList
              dateRange={dateRange}
              selectedStores={selectedStores}
              sourceId={10000172}
              pageSize={10}
              showStoreInfo={true}
              showEmployeeInfo={false}
              showPaymentMethod={true}
              showPagination={true}
            />
          </div>
        </div>
      </div>
    </ReportsContext.Provider>
  )
}
