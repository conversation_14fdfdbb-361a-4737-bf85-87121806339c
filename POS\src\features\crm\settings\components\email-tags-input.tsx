import { useState, KeyboardEvent, useRef, useEffect } from 'react'

import { X, AlertCircle } from 'lucide-react'

import { cn } from '@/lib/utils'

import { Input } from '../../../../components/ui/input'

interface EmailTagsInputProps {
  value: string[]
  onChange: (emails: string[]) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  error?: string
  maxEmails?: number
}

export function EmailTagsInput({
  value = [],
  onChange,
  placeholder = 'Nhập email và ấn Enter hoặc dấu phẩy',
  disabled = false,
  className,
  error,
  maxEmails = 10
}: EmailTagsInputProps) {
  const [inputValue, setInputValue] = useState('')
  const [inputError, setInputError] = useState('')
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email.trim())
  }

  const addEmail = (email: string) => {
    const trimmedEmail = email.trim()

    if (!trimmedEmail) {
      setInputError('Email không được để trống')
      return
    }

    if (!isValidEmail(trimmedEmail)) {
      setInputError('Email không hợp lệ')
      return
    }

    if (value.includes(trimmedEmail)) {
      setInputError('Email đã tồn tại')
      return
    }

    if (value.length >= maxEmails) {
      setInputError(`Chỉ được phép tối đa ${maxEmails} email`)
      return
    }

    onChange([...value, trimmedEmail])
    setInputValue('')
    setInputError('')
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      addEmail(inputValue)
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      // Remove last email when backspace is pressed on empty input
      onChange(value.slice(0, -1))
    }
  }

  const handleBlur = () => {
    setIsFocused(false)
    if (inputValue.trim()) {
      addEmail(inputValue)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
    if (inputError) {
      setInputError('')
    }
  }

  const handleContainerClick = () => {
    if (!disabled && inputRef.current) {
      inputRef.current.focus()
    }
  }

  useEffect(() => {
    if (error) {
      setInputError(error)
    }
  }, [error])

  const removeEmail = (emailToRemove: string) => {
    onChange(value.filter(email => email !== emailToRemove))
  }

  return (
    <div className='w-full'>
      <div
        onClick={handleContainerClick}
        className={cn(
          'border-input flex min-h-9 w-full flex-wrap items-center gap-1 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          isFocused && 'border-ring ring-ring/50 ring-[3px]',
          (inputError || error) && 'border-red-500',
          disabled && 'cursor-not-allowed opacity-50',
          className
        )}
      >
        {/* Email Tags */}
        {value.map((email, index) => (
          <div
            key={index}
            className='inline-flex items-center gap-1 rounded-md bg-blue-100 px-2 py-1 text-sm text-blue-800 transition-colors hover:bg-blue-200'
          >
            <span>{email}</span>
            {!disabled && (
              <button
                type='button'
                onClick={e => {
                  e.stopPropagation()
                  removeEmail(email)
                }}
                className='flex h-4 w-4 items-center justify-center rounded-full transition-colors hover:bg-blue-300'
                aria-label={`Xóa email ${email}`}
              >
                <X className='h-3 w-3' />
              </button>
            )}
          </div>
        ))}

        {/* Input */}
        <Input
          ref={inputRef}
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={handleBlur}
          placeholder={value.length === 0 ? placeholder : ''}
          disabled={disabled}
          className='min-w-[120px] flex-1 border-0 bg-transparent p-0 shadow-none focus-visible:ring-0'
        />
      </div>

      {/* Error Message */}
      {(inputError || error) && (
        <div className='mt-1 flex items-center gap-1 text-sm text-red-600'>
          <AlertCircle className='h-4 w-4' />
          <span>{inputError || error}</span>
        </div>
      )}
    </div>
  )
}
