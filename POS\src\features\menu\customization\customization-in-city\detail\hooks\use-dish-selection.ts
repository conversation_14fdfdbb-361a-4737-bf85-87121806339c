import { useState } from 'react'

import { toast } from 'sonner'

interface DishItem {
  id: string
  item_id: string
  item_name: string
  ots_price: number
}

export function useDishSelection() {
  const [selectedDishes, setSelectedDishes] = useState<Set<string>>(new Set())
  const [dishSearchTerm, setDishSearchTerm] = useState('')
  const [selectedSectionOpen, setSelectedSectionOpen] = useState(true)
  const [remainingSectionOpen, setRemainingSectionOpen] = useState(true)

  const handleDishToggle = (dishId: string) => {
    const newSelected = new Set(selectedDishes)
    if (newSelected.has(dishId)) {
      newSelected.delete(dishId)
    } else {
      newSelected.add(dishId)
    }
    setSelectedDishes(newSelected)
  }

  const handleConfirmDishSelection = () => {
    toast.success(`Đã chọn ${selectedDishes.size} món`)
    return true
  }

  const resetDishSelection = () => {
    setDishSearchTerm('')
    setSelectedSectionOpen(true)
    setRemainingSectionOpen(true)
  }

  const getFilteredDishes = (items: DishItem[]) => {
    return items.filter((item: DishItem) =>
      item.item_name.toLowerCase().includes(dishSearchTerm.toLowerCase())
    )
  }

  const getSelectedDishItems = (items: DishItem[]) => {
    const filtered = getFilteredDishes(items)
    return filtered.filter((item: DishItem) => selectedDishes.has(item.id))
  }

  const getRemainingDishItems = (items: DishItem[]) => {
    const filtered = getFilteredDishes(items)
    return filtered.filter((item: DishItem) => !selectedDishes.has(item.id))
  }

  return {
    // State
    selectedDishes,
    dishSearchTerm,
    selectedSectionOpen,
    remainingSectionOpen,

    // Actions
    setSelectedDishes,
    setDishSearchTerm,
    setSelectedSectionOpen,
    setRemainingSectionOpen,
    handleDishToggle,
    handleConfirmDishSelection,
    resetDishSelection,

    // Computed
    getFilteredDishes,
    getSelectedDishItems,
    getRemainingDishItems,
    hasSelectedDishes: selectedDishes.size > 0,
    selectedDishesCount: selectedDishes.size
  }
}
