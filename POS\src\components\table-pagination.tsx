import * as React from 'react'

import { Pagination, usePagination } from './pagination'

interface TablePaginationProps<T> {
  data: T[]
  pageSize?: number
  className?: string
  showPageSizeSelector?: boolean
  showItemCount?: boolean
  showFirstLastButtons?: boolean
  pageSizeOptions?: number[]
  disabled?: boolean
  children: (paginatedData: T[], pagination: PaginationInfo) => React.ReactNode
}

interface PaginationInfo {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
  startIndex: number
  endIndex: number
}

export function TablePagination<T>({
  data,
  pageSize: defaultPageSize = 10,
  className,
  showPageSizeSelector = true,
  showItemCount = true,
  showFirstLastButtons = true,
  pageSizeOptions = [10, 20, 50, 100],
  disabled = false,
  children
}: TablePaginationProps<T>) {
  const { currentPage, pageSize: urlPageSize, setPage, setPageSize } = usePagination()

  const effectivePageSize = urlPageSize || defaultPageSize

  const totalItems = data.length
  const totalPages = Math.ceil(totalItems / effectivePageSize)

  const validCurrentPage = Math.min(Math.max(1, currentPage), totalPages || 1)

  const startIndex = (validCurrentPage - 1) * effectivePageSize
  const endIndex = Math.min(startIndex + effectivePageSize, totalItems)

  const paginatedData = React.useMemo(() => {
    return data.slice(startIndex, endIndex)
  }, [data, startIndex, endIndex])

  const paginationInfo: PaginationInfo = {
    currentPage: validCurrentPage,
    totalPages,
    pageSize: effectivePageSize,
    totalItems,
    startIndex,
    endIndex
  }

  React.useEffect(() => {
    if (currentPage !== validCurrentPage && totalPages > 0) {
      setPage(validCurrentPage)
    }
  }, [currentPage, validCurrentPage, totalPages, setPage])

  return (
    <>
      {children(paginatedData, paginationInfo)}

      {totalItems > 0 && (
        <Pagination
          currentPage={validCurrentPage}
          totalPages={totalPages}
          pageSize={effectivePageSize}
          totalItems={totalItems}
          onPageChange={setPage}
          onPageSizeChange={setPageSize}
          showPageSizeSelector={showPageSizeSelector}
          showItemCount={showItemCount}
          showFirstLastButtons={showFirstLastButtons}
          pageSizeOptions={pageSizeOptions}
          className={className}
          disabled={disabled}
        />
      )}
    </>
  )
}

export function usePaginatedData<T>(data: T[], defaultPageSize = 10) {
  const { currentPage, pageSize: urlPageSize } = usePagination()

  const effectivePageSize = urlPageSize || defaultPageSize
  const totalItems = data.length
  const totalPages = Math.ceil(totalItems / effectivePageSize)

  const validCurrentPage = Math.min(Math.max(1, currentPage), totalPages || 1)

  const startIndex = (validCurrentPage - 1) * effectivePageSize
  const endIndex = Math.min(startIndex + effectivePageSize, totalItems)

  const paginatedData = React.useMemo(() => {
    return data.slice(startIndex, endIndex)
  }, [data, startIndex, endIndex])

  return {
    paginatedData,
    currentPage: validCurrentPage,
    totalPages,
    pageSize: effectivePageSize,
    totalItems,
    startIndex,
    endIndex
  }
}
