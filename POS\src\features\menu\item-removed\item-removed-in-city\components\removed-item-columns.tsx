import { format } from 'date-fns'
import { ColumnDef } from '@tanstack/react-table'
import { IconRestore } from '@tabler/icons-react'
import { RemovedItem } from '@/types/item-removed'
import { formatCurrency } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'

export const removedItemColumns: ColumnDef<RemovedItem>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'id',
    header: '#',
    cell: ({ row }) => {
      const index = row.index + 1
      return <div className='w-[50px] font-medium'>{index}</div>
    },
    enableSorting: false,
  },
  {
    accessorKey: 'item_id',
    header: 'Mã món',
    cell: ({ row }) => {
      const item = row.original
      return <span className='font-medium'>{item.item_id}</span>
    },
  },
  {
    accessorKey: 'item_name',
    header: 'Tên món',
    cell: ({ row }) => {
      const item = row.original
      return <span className='font-medium'>{item.item_name}</span>
    },
  },
  {
    accessorKey: 'ta_price',
    header: 'Giá',
    cell: ({ row }) => {
      const item = row.original
      return (
        <span className='font-medium'>{formatCurrency(item.ta_price)}</span>
      )
    },
  },
  {
    accessorKey: 'city_name',
    header: 'Thành phố',
    cell: ({ row, table }) => {
      const item = row.original
      const meta = table.options.meta as {
        getCityName?: (cityUid: string) => string
      }
      const cityName = meta?.getCityName?.(item.city_uid) || item.city_uid
      return <span>{cityName}</span>
    },
  },
  {
    accessorKey: 'deleted_by',
    header: 'Người xoá',
    cell: ({ row }) => {
      const item = row.original
      return <span>{item.deleted_by}</span>
    },
  },
  {
    accessorKey: 'deleted_at',
    header: 'Thời gian xoá',
    cell: ({ row }) => {
      const item = row.original
      return (
        <span>
          {format(new Date(item.deleted_at * 1000), 'dd/MM/yyyy HH:mm')}
        </span>
      )
    },
  },
  {
    id: 'restore',
    header: '',
    cell: ({ row, table }) => {
      const item = row.original
      const meta = table.options.meta as {
        onRestoreItem?: (item: RemovedItem) => void
      }

      return (
        <Button
          variant='ghost'
          size='sm'
          onClick={() => meta?.onRestoreItem?.(item)}
          className='h-8 w-8 p-0 text-blue-600 hover:text-blue-700'
          title='Khôi phục món'
        >
          <IconRestore className='h-4 w-4' />
          <span className='sr-only'>Khôi phục món {item.item_name}</span>
        </Button>
      )
    },
  },
]
