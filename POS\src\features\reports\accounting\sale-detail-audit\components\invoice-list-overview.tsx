import { useState, useMemo } from 'react'
import { usePosStores } from '@/stores/posStore'
import {
  getDefaultMonthlyDateRange,
  getDefaultDailyDateRange,
} from '@/lib/date-utils'
import { ReportsContext } from '@/hooks/use-reports-context'
import { FiltersPanel } from './filters-panel'
import { InvoiceList } from './invoice-list'

export function InvoiceOverview() {
  // Get current brand to ensure component re-renders when brand changes
  const { selectedBrand } = usePosStores()
  const [filterType, setFilterType] = useState<'monthly' | 'daily'>('daily')

  const [dateRange, setDateRange] = useState<{
    from: Date
    to: Date
  }>(getDefaultDailyDateRange())
  const [selectedStores, setSelectedStores] = useState<string[]>(['all-stores'])
  const [selectedSources, setSelectedSources] = useState<string[]>([
    'all-sources',
  ])

  const handleFilterTypeChange = (newFilterType: 'monthly' | 'daily') => {
    setFilterType(newFilterType)

    if (newFilterType === 'monthly') {
      setDateRange(getDefaultMonthlyDateRange())
    } else if (newFilterType === 'daily') {
      setDateRange(getDefaultDailyDateRange())
    }
  }

  const contextValue = useMemo(
    () => ({
      dateRange,
      filterType,
      selectedStores,
      selectedSources,
      brandId: selectedBrand?.id, // Include brandId in context to trigger re-render
    }),
    [dateRange, filterType, selectedStores, selectedSources, selectedBrand?.id]
  )

  return (
    <ReportsContext.Provider value={contextValue}>
      <div className='space-y-6'>
        {/* Filters Panel */}
        <FiltersPanel
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
          selectedStores={selectedStores}
          onStoreChange={setSelectedStores}
          selectedSources={selectedSources}
          onSourceChange={setSelectedSources}
          filterType={filterType}
          onFilterTypeChange={handleFilterTypeChange}
        />

        {/* Charts Section */}
        <div className='w-full max-w-full overflow-hidden'>
          <InvoiceList
            key={selectedBrand?.id} // Force re-mount when brand changes
            dateRange={dateRange}
            selectedStores={selectedStores}
            sourceId={10000172}
            pageSize={10}
            showPagination={true}
          />
        </div>
      </div>
    </ReportsContext.Provider>
  )
}
