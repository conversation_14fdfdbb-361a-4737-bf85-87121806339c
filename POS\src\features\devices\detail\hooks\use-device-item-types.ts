import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'
import { api } from '@/lib/api/pos/pos-api'

interface UseDeviceItemTypesProps {
  storeUid?: string
}

export function useDeviceItemTypes({ storeUid }: UseDeviceItemTypesProps) {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: ['device-item-types', company?.id, selectedBrand?.id, storeUid],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id || !storeUid) return []

      const response = await api.get(
        `/mdata/v1/item-types?skip_limit=true&company_uid=${company.id}&brand_uid=${selectedBrand.id}&store_uid=${storeUid}`
      )
      return response.data?.data || []
    },
    enabled: !!(company?.id && selectedBrand?.id && storeUid)
  })
}
