import { <PERSON>rab<PERSON>rders<PERSON>hart } from './grab-orders-chart'
import { ProfitLossReportChart } from './profit-loss-report-chart'
import { RevenueChart } from './revenue-chart'
import { ShopeeOrdersChart } from './shopee-orders-chart'
import { TopPaymentMethodsChart } from './top-payment-methods-chart'
import { TopProductsChart } from './top-products-chart'
import { TopPromotionsChart } from './top-promotions-chart'
import { TopSourcesChart } from './top-sources-chart'
import { TopStoresChart } from './top-stores-chart'

export function ReportCharts() {
  return (
    <>
      {/* Báo cáo 7 ngày trước */}
      <div className='mb-8 text-center'>
        <h2 className='text-xl font-bold'>Báo cáo thống kê 7 ngày trước</h2>
      </div>

      {/* Dòng 1: 7 ngày trước */}
      <div className='mb-8 flex flex-wrap items-stretch gap-4'>
        <div className='h-[500px] min-w-80 flex-1'>
          <RevenueChart />
        </div>
        <div className='h-[500px] min-w-80 flex-1'>
          <TopProductsChart />
        </div>
        <div className='h-[500px] min-w-80 flex-1'>
          <TopPromotionsChart />
        </div>
      </div>

      {/* Dòng 2: 5 ngày trước */}
      <div className='mb-8 flex flex-wrap items-stretch gap-4'>
        <div className='min-w-80 flex-1'>
          <TopSourcesChart />
        </div>
        <div className='min-w-80 flex-1'>
          <TopPaymentMethodsChart />
        </div>
        <div className='min-w-80 flex-1'>
          <TopStoresChart />
        </div>
      </div>

      {/* Dòng 3: 3 ngày trước */}
      <div className='flex flex-wrap items-stretch gap-4'>
        <div className='min-w-80 flex-1'>
          <ShopeeOrdersChart />
        </div>
        <div className='min-w-80 flex-1'>
          <GrabOrdersChart />
        </div>
        <div className='min-w-80 flex-1'>
          <ProfitLossReportChart />
        </div>
      </div>
    </>
  )
}
