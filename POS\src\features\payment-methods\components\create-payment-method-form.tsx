import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { useCreatePaymentMethod } from '@/hooks/api/use-payment-methods'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import { StoreSelectionModal } from './store-selection-modal'

export function CreatePaymentMethodForm() {
  const navigate = useNavigate()
  const { createPaymentMethod, isCreating } = useCreatePaymentMethod()

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    autoGenerateCode: true,
    cardProcessingFee: '0',
    feeBearer: 'customer', // 'customer' | 'restaurant'
    requireTransactionCode: false,
    selectedStores: [] as string[],
    logoFile: null as File | null,
    logoPreview: '' as string
  })

  const [showStoreModal, setShowStoreModal] = useState(false)

  const handleBack = () => {
    navigate({ to: '/setting/payment-method' })
  }

  const handleSave = async () => {
    if (!isFormValid) return

    const paymentMethodData = {
      payment_method_name: formData.name,
      payment_method_id: formData.autoGenerateCode ? undefined : formData.code,
      payment_fee_extra: parseFloat(formData.cardProcessingFee) / 100, // Convert percentage to decimal
      payment_fee_type: 0,
      stores: formData.selectedStores,
      extra_data: formData.requireTransactionCode ? { require_traceno: 1 } : undefined,
      logoFile: formData.logoFile
    }

    createPaymentMethod(paymentMethodData, {
      onSuccess: () => {
        navigate({ to: '/setting/payment-method' })
      }
    })
  }

  const isFormValid = formData.name.trim() !== '' && formData.selectedStores.length > 0

  const handleStoreSelection = () => {
    setShowStoreModal(true)
  }

  const handleStoreSelectionChange = (storeIds: string[]) => {
    setFormData({ ...formData, selectedStores: storeIds })
  }

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = event => {
        const preview = event.target?.result as string
        setFormData({
          ...formData,
          logoFile: file,
          logoPreview: preview
        })
      }
      reader.readAsDataURL(file)
    }
  }

  const handleRemoveLogo = () => {
    setFormData({
      ...formData,
      logoFile: null,
      logoPreview: ''
    })
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header */}
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <Button
            type='button'
            disabled={isCreating || !isFormValid}
            className='min-w-[100px]'
            onClick={handleSave}
          >
            {isCreating ? 'Đang tạo...' : 'Lưu'}
          </Button>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>Tạo phương thức thanh toán mới</h1>
        </div>
      </div>

      {/* Form Content */}
      <div className='mx-auto max-w-4xl'>
        <div className='p-6'>
          <div className='space-y-6'>
            {/* Section Title */}
            <div>
              <h2 className='text-lg font-medium text-gray-900'>Thông tin chi tiết</h2>
            </div>

            {/* Tên phương thức */}
            <div className='flex items-center gap-4'>
              <Label htmlFor='payment-method-name' className='min-w-[200px] text-sm font-medium'>
                Tên phương thức <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='payment-method-name'
                value={formData.name}
                onChange={e => setFormData({ ...formData, name: e.target.value })}
                placeholder='Nhập tên phương thức thanh toán'
                className='flex-1'
              />
            </div>

            {/* Cửa hàng áp dụng */}
            <div className='flex items-center gap-4'>
              <Label className='min-w-[200px] text-sm font-medium'>
                Cửa hàng áp dụng <span className='text-red-500'>*</span>
              </Label>
              <Button
                variant='outline'
                onClick={handleStoreSelection}
                className='flex-1 justify-start'
              >
                {formData.selectedStores.length > 0
                  ? `${formData.selectedStores.length} điểm`
                  : '0 điểm'}
              </Button>
            </div>

            {/* Mã PTTT */}
            <div className='flex items-center gap-4'>
              <Label htmlFor='payment-method-code' className='min-w-[200px] text-sm font-medium'>
                Mã PTTT
              </Label>
              <Input
                id='payment-method-code'
                value={formData.code}
                onChange={e => setFormData({ ...formData, code: e.target.value })}
                placeholder='Nếu để trống, hệ thống sẽ tự động tạo một mã PTTT'
                disabled={formData.autoGenerateCode}
                className='flex-1'
              />
              <Checkbox
                id='auto-generate-code'
                checked={formData.autoGenerateCode}
                onCheckedChange={checked =>
                  setFormData({
                    ...formData,
                    autoGenerateCode: checked as boolean,
                    code: checked ? '' : formData.code
                  })
                }
              />
            </div>

            {/* Phí cà thẻ */}
            <div className='flex items-center gap-4'>
              <Label htmlFor='card-processing-fee' className='min-w-[200px] text-sm font-medium'>
                Phí cà thẻ
              </Label>
              <div className='flex-1'>
                <div className='relative'>
                  <Input
                    id='card-processing-fee'
                    type='number'
                    value={formData.cardProcessingFee}
                    onChange={e => setFormData({ ...formData, cardProcessingFee: e.target.value })}
                    placeholder='0'
                    className='pr-8'
                    min='0'
                    max='100'
                    step='0.01'
                  />
                  <span className='absolute top-1/2 right-3 -translate-y-1/2 text-sm text-gray-500'>
                    %
                  </span>
                </div>
              </div>
            </div>

            {/* Bên chịu phí cà thẻ */}
            <div className='flex items-center gap-4'>
              <Label className='min-w-[200px] text-sm font-medium'>Bên chịu phí cà thẻ</Label>
              <Select
                value={formData.feeBearer}
                onValueChange={value => setFormData({ ...formData, feeBearer: value })}
              >
                <SelectTrigger className='flex-1'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='customer'>Khách hàng</SelectItem>
                  <SelectItem value='restaurant'>Nhà hàng</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Yêu cầu nhập mã giao dịch */}
            <div className='flex items-center gap-4'>
              <Label className='min-w-[200px] text-sm font-medium'>
                Yêu cầu nhập mã giao dịch khi thanh toán
              </Label>
              <div className='flex-1'>
                <Checkbox
                  id='require-transaction-code'
                  checked={formData.requireTransactionCode}
                  onCheckedChange={checked =>
                    setFormData({ ...formData, requireTransactionCode: checked as boolean })
                  }
                />
              </div>
            </div>

            {/* Logo Section */}
            <div className='space-y-4'>
              <h2 className='text-lg font-medium text-gray-900'>Logo</h2>

              <div className='flex items-start gap-4'>
                <Label className='min-w-[200px] pt-2 text-sm font-medium'>Logo tải lên</Label>
                <div className='flex-1'>
                  {/* Logo Preview */}
                  {formData.logoPreview ? (
                    <div className='space-y-2'>
                      <div className='relative inline-block'>
                        <img
                          src={formData.logoPreview}
                          alt='Logo preview'
                          className='h-24 w-24 rounded-md border object-cover'
                        />
                        <Button
                          type='button'
                          variant='destructive'
                          size='sm'
                          className='absolute -top-2 -right-2 h-6 w-6 rounded-full p-0'
                          onClick={handleRemoveLogo}
                        >
                          ×
                        </Button>
                      </div>
                      <p className='text-muted-foreground text-xs'>{formData.logoFile?.name}</p>
                    </div>
                  ) : (
                    /* Upload Area */
                    <div className='rounded-md border-2 border-dashed border-gray-300 p-6 text-center transition-colors hover:border-gray-400'>
                      <div className='space-y-2'>
                        <div className='mx-auto h-12 w-12 text-gray-400'>
                          <svg fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                            <path
                              strokeLinecap='round'
                              strokeLinejoin='round'
                              strokeWidth={1}
                              d='M12 6v6m0 0v6m0-6h6m-6 0H6'
                            />
                          </svg>
                        </div>
                        <div>
                          <label htmlFor='payment-method-logo' className='cursor-pointer'>
                            <span className='text-sm font-medium text-blue-600 hover:text-blue-500'>
                              Tải ảnh lên
                            </span>
                            <Input
                              id='payment-method-logo'
                              type='file'
                              accept='image/*'
                              onChange={handleLogoUpload}
                              className='hidden'
                            />
                          </label>
                        </div>
                        <p className='text-xs text-gray-500'>PNG, JPG, GIF up to 10MB</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Store Selection Modal */}
      <StoreSelectionModal
        open={showStoreModal}
        onOpenChange={setShowStoreModal}
        selectedStoreIds={formData.selectedStores}
        onStoreSelectionChange={handleStoreSelectionChange}
      />
    </div>
  )
}
