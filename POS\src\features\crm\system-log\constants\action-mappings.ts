/**
 * System Log Action Mappings
 * Maps API request paths to Vietnamese descriptions
 */
export const SYSTEM_LOG_ACTION_MAP: Record<string, string> = {
  // Existing mappings
  '/crm/setting/crm_config/update': 'SYSTEM_LOG:crm:settingupdate',
  '/crm/setting/update_pos_parent': 'Cập nhật thông tin thương hiệu',
  '/crm/setting/update-setting-config': 'SYSTEM_LOG:crm:settingupdate',
  '/crm/setting/update-setting-loyalty': 'Cập nhật chương trình thành viên',
  '/crm/setting/update-cheat-config': 'Cập nhật cảnh báo giao dịch',
  '/crm/loyalty/membership_type/update': 'SYSTEM_LOG.crmloyaltymembership_typeupdate',
  '/crm/loyalty/membership_type/create': 'SYSTEM_LOG.crmloyaltymembership_typecreate',
  '/crm/maketing/update_campaign': 'Cậ<PERSON> nhật CTKM',
  '/crm/setting/register_form/config': 'SYSTEM_LOG.crmsettingregister_formconfig',
  '/crm/loyalty/reset_point': 'Reset điểm hội viên',
  '/crm/loyalty/membership_type_extra_rate/update': 'SYSTEM_LOG.crmloyaltymembership_type_extra_rateupdate',
  '/crm/loyalty/membership_type_extra_rate/create': 'SYSTEM_LOG.crmloyaltymembership_type_extra_ratecreate',
  '/crm/maketing/export_vouchers': 'Xuất danh sách mã voucher',
  '/crm/setting/sync': 'Đồng bộ',
  '/crm/setting/update_warning_cheat_config': 'SYSTEM_LOG.crmsettingupdate_warning_cheat_config',
  '/crm/loyalty/loyalty_config/update': 'SYSTEM_LOG.crmloyaltyloyalty_configupdate',
  '/crm/report/sale_manager': 'SYSTEM_LOG.crmreportsale_manager',
  '/crm/setting/delivery/config': 'SYSTEM_LOG.crmsettingdeliveryconfig',
  '/crm/maketing/create_trigger': 'Tạo trigger',
  '/crm/loyalty/company_membership/update/point': 'SYSTEM_LOG.crmloyaltymembership_typecreate',
  '/crm/filter/bulk_update/add_tag': 'SYSTEM_LOG.crmloyaltycompany_membershipupdatemembership_type',
  '/crm/member/update': 'SYSTEM_LOG.crmmemberupdate',
  '/crm/loyalty/company_membership/update/membership_type':
    'SYSTEM_LOG.crmloyaltycompany_membershipupdatemembership_type',
  '/crm/common/update_marketing_metric': 'SYSTEM_LOG.crmloyaltymembership_typeupdate',
  '/crm/account/update': 'SYSTEM_LOG.crmloyaltymembership_typeupdate',

  // New mappings based on requirements
  '/crm/customer-care/scenario/reopen': 'Mở lại kịch bản CSKH',
  '/crm/customer-care/schedule/create': 'Tạo lịch CSKH',
  '/crm/marketing/voucher/export-history': 'Xuất lịch sử mã voucher từ CTKM',
  '/crm/marketing/broadcast/send': 'Gửi broadcast',
  '/crm/customer-care/scenario/update': 'Cập nhật kịch bản CSKH',
  '/crm/marketing/voucher/reactivate': 'Kích hoạt lại mã voucher',
  '/crm/o2o/config/create': 'Tạo cấu hình O2O',
  '/crm/o2o/config/update': 'Cập nhật cấu hình O2O',
  '/crm/online-sales/time-config/create': 'Thêm cấu hình thời gian bán online',
  '/crm/menu/parent-item/create': 'Tạo món cha',
  '/crm/online-sales/time-config/delete': 'Xóa thời gian bán online',
  '/crm/facebook/page/connect': 'Kết nối Facebook page',
  '/crm/facebook/config/save': 'Lưu cấu hình Facebook',
  '/crm/menu/sync': 'Đồng bộ món',
  '/crm/zalo/config/update': 'Cập nhật cấu hình Zalo',
  '/crm/online-sales/time-config/update': 'Cập nhật thời gian bán online'
}

/**
 * Get Vietnamese description for a request path
 */
export function getActionLabel(requestPath: string): string {
  return SYSTEM_LOG_ACTION_MAP[requestPath] || requestPath
}

/**
 * Fixed filter options for System Log actions (Vietnamese only)
 */
export const SYSTEM_LOG_FILTER_OPTIONS = [
  'Reset điểm hội viên',
  'Mở lại kịch bản CSKH',
  'Tạo lịch CSKH',
  'Tạo trigger',
  'Xuất lịch sử mã voucher từ CTKM',
  'Xuất danh sách mã voucher',
  'Gửi broadcast',
  'Cập nhật CTKM',
  'Cập nhật kịch bản CSKH',
  'Kích hoạt lại mã voucher',
  'Tạo cấu hình O2O',
  'Cập nhật cấu hình O2O',
  'Thêm cấu hình thời gian bán online',
  'Tạo món cha',
  'Xóa thời gian bán online',
  'Kết nối Facebook page',
  'Đồng bộ',
  'Lưu cấu hình Facebook',
  'Đồng bộ món',
  'Cập nhật cấu hình Zalo',
  'Cập nhật thông tin thương hiệu',
  'Cập nhật thời gian bán online'
]
