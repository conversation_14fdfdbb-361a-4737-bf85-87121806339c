import { useMemo } from 'react'

import { ClockIcon } from 'lucide-react'

import { usePosData } from '@/hooks/use-pos-data'

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui'

import { usePromotionContext } from '../context'
import { useSaleChangeLog, type InvoiceEntry } from '../hooks'

interface OrderLogModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  invoice: InvoiceEntry
}

export function OrderLogModal({ open, onOpenChange, invoice }: OrderLogModalProps) {
  if (!invoice) return null

  const { selectedStoreIds, dateRange } = usePromotionContext()
  const { company, activeBrands } = usePosData()

  const startDate = useMemo(() => {
    const start = new Date(dateRange.from)
    start.setHours(0, 0, 0, 0)
    return start.getTime()
  }, [dateRange.from])

  const endDate = useMemo(() => {
    const end = new Date(dateRange.to)
    end.setHours(23, 59, 59, 999)
    return end.getTime()
  }, [dateRange.to])

  const {
    data: changeLogData,
    isLoading: isLoadingLog,
    error: logError
  } = useSaleChangeLog(
    {
      company_uid: company?.id || '',
      brand_uid: activeBrands?.[0]?.id || '',
      store_uid: selectedStoreIds?.[0] || '',
      tran_id: invoice.tran_id,
      start_date: startDate,
      end_date: endDate
    },
    open && !!invoice.tran_id
  )

  const formatTime = (timestamp: number) => {
    if (!timestamp) return 'N/A'
    const date = new Date(timestamp)
    return date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-2xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center justify-center'>
            <span>Nhật ký order</span>
          </DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          {isLoadingLog ? (
            <div className='py-4 text-center text-sm text-gray-500'>Đang tải nhật ký...</div>
          ) : logError ? (
            <div className='py-4 text-center text-sm text-red-500'>Lỗi khi tải nhật ký: {logError.message}</div>
          ) : changeLogData?.data && changeLogData.data.length > 0 ? (
            changeLogData.data.map((log, index) => (
              <div key={index} className='space-y-4 rounded-lg border p-4'>
                <div className='space-y-2'>
                  <div className='flex items-center justify-between'>
                    <div className='text-lg font-bold text-gray-900'>
                      #{log.tran_id?.slice(-5) || 'N/A'} - {log.change_data?.tran_no || 'N/A'}
                    </div>
                    <div className='text-sm text-gray-500'>
                      {new Date(log.created_at * 1000).toLocaleDateString('vi-VN')}{' '}
                      {formatTime(log.change_data?.tran_date || 0)}
                    </div>
                  </div>
                  <div className='text-sm text-gray-600'>{log.table_name || 'N/A'}</div>
                  <div className='text-sm text-gray-600'>
                    TN: {log.change_data?.employee_name || 'N/A'} - STT: {log.change_data?.order_no || 'N/A'}
                  </div>
                </div>

                {log.change_data?.sale_detail && log.change_data.sale_detail.length > 0 && (
                  <div className='space-y-3'>
                    {log.change_data.sale_detail.map((item, itemIndex) => (
                      <div key={itemIndex} className='border-b border-gray-200 pb-3'>
                        <div className='flex items-center justify-between'>
                          <div className='flex flex-1 items-center gap-2'>
                            <span className='text-gray-900'>{item.item_name || 'N/A'}</span>
                            <div className='flex items-center'>
                              <ClockIcon className='mr-2 h-4 w-4 text-gray-400' />
                              <span className='text-sm text-gray-500'>
                                {formatTime(log.change_data?.tran_date || 0)}
                              </span>
                            </div>
                          </div>
                          <div className='text-right'>
                            <span className='text-gray-900'>{item.quantity}</span>
                          </div>
                        </div>

                        {item.toppings && item.toppings.length > 0 && (
                          <div className='mt-2 text-xs text-gray-500'>
                            {item.toppings.map((topping, toppingIndex) => (
                              <span key={toppingIndex}>
                                {topping.item_name || 'N/A'} {topping.quantity || 1}
                                {toppingIndex < item.toppings.length - 1 ? ', ' : ''}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className='py-4 text-center text-sm text-gray-500 italic'>Không có nhật ký thay đổi</div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
