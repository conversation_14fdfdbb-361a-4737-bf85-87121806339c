import { useState, useMemo, useEffect } from 'react'

import { ChevronDown, ChevronRight } from 'lucide-react'

import type { Item } from '@/lib/item-api'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'

import { PosModal } from '@/components/pos'
import { Label } from '@/components/ui'

import type { ItemsInStore } from '../../../data'

interface BuffetConfigModalProps {
  itemsBuffet: string[]
  open: boolean
  onOpenChange: (open: boolean) => void
  onItemsChange: (items: string[]) => void
  items: Item[] | ItemsInStore[]
  onOpenUpSizeList?: () => void
  hide?: boolean
  enable?: boolean
  onEnableChange?: (enabled: boolean) => void
}

export function BuffetConfigModal({
  itemsBuffet,
  open,
  onOpenChange,
  onItemsChange,
  items,
  hide = true,
  enable = true,
  onEnableChange
}: BuffetConfigModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [tempSelectedItems, setTempSelectedItems] = useState<string[]>([])
  const [selectedCollapsed, setSelectedCollapsed] = useState(false)
  const [remainingCollapsed, setRemainingCollapsed] = useState(false)
  const [enabled, setEnabled] = useState(false)

  useEffect(() => {
    if (open) {
      setTempSelectedItems(Array.isArray(itemsBuffet) ? itemsBuffet : [])
      setEnabled(enable)
    }
  }, [itemsBuffet, open])

  const filteredItems = useMemo(() => {
    if (!searchTerm) return items
    return items.filter(item => item.item_name?.toLowerCase().includes(searchTerm.toLowerCase()))
  }, [items, searchTerm])

  const selectedItems = useMemo(() => {
    if (!filteredItems.length) return []
    return filteredItems.filter(item => tempSelectedItems.includes(item.item_id || ''))
  }, [filteredItems, tempSelectedItems])

  const unselectedItems = useMemo(() => {
    if (!filteredItems.length) return []
    return filteredItems.filter(item => !tempSelectedItems.includes(item.item_id || ''))
  }, [filteredItems, tempSelectedItems])

  const handleItemToggle = (itemId: string) => {
    setTempSelectedItems(prev => (prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]))
  }

  const selectedCount = selectedItems.length
  const totalFilteredCount = filteredItems.length
  const isAllSelected = totalFilteredCount > 0 && selectedCount === totalFilteredCount
  const isIndeterminate = selectedCount > 0 && selectedCount < totalFilteredCount

  const handleSelectAll = () => {
    if (isAllSelected) {
      const allFilteredIds = filteredItems.map(item => item.item_id)
      setTempSelectedItems(prev => prev.filter(id => !allFilteredIds.includes(id)))
    } else {
      const allFilteredIds = filteredItems.map(item => item.item_id)
      setTempSelectedItems(prev => {
        const newSelection = [...prev]
        allFilteredIds.forEach(id => {
          if (!newSelection.includes(id || '')) {
            newSelection.push(id || '')
          }
        })
        return newSelection
      })
    }
  }

  const handleConfirm = () => {
    onItemsChange(tempSelectedItems)
    onOpenChange(false)
  }

  const handleCancel = () => {
    setTempSelectedItems([])
    onOpenChange(false)
  }

  return (
    <PosModal
      title={`Chọn danh sách món không đi kèm vé buffet`}
      centerTitle
      open={open}
      onOpenChange={onOpenChange}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      confirmText='Lưu'
      cancelText='Hủy'
      maxWidth='sm:max-w-2xl'
    >
      <div className='space-y-4'>
        {/* Collapsed header (image 1) */}
        {!hide && (
          <div className='flex items-center space-x-2'>
            <Checkbox
              id='enable-buffet'
              checked={enabled}
              onCheckedChange={checked => {
                const next = Boolean(checked)
                setEnabled(next)
                onEnableChange?.(next)
              }}
            />
            <Label htmlFor='enable-buffet' className='cursor-pointer text-blue-600'>
              Cấu hình món ăn là vé buffet
            </Label>
          </div>
        )}

        {/* Expanded content (image 2) */}
        {(hide || enabled) && (
          <>
            <div className='flex items-center gap-2'>
              <Input
                placeholder='Tìm kiếm'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='w-full'
              />
            </div>

            {/* Top action row */}
            {!hide && (
              <div className='flex items-center gap-2'>
                <Button type='button' variant='outline' className='flex-1 justify-start'>
                  Danh sách món không đi kèm vé buffet
                </Button>

                <Button type='button' variant='link' className='flex-1 justify-start text-blue-600' onClick={() => {}}>
                  Danh sách vé buffet được upsize
                </Button>
              </div>
            )}

            {/* Selected summary bar */}
            <div className='rounded-lg bg-green-50 p-3'>
              <div className='flex items-center space-x-3'>
                <Checkbox
                  id='select-all'
                  checked={isAllSelected}
                  {...(isIndeterminate && { 'data-indeterminate': 'true' })}
                  onCheckedChange={handleSelectAll}
                  className='data-[state=checked]:border-green-600 data-[state=checked]:bg-green-600'
                />
                <label htmlFor='select-all' className='cursor-pointer text-sm font-medium text-green-700'>
                  Đã chọn {selectedCount}
                </label>
                <Button
                  variant='ghost'
                  size='sm'
                  className='ml-auto h-6 px-2 text-xs'
                  onClick={() => setSelectedCollapsed(!selectedCollapsed)}
                >
                  {selectedCollapsed ? <ChevronRight className='h-3 w-3' /> : <ChevronDown className='h-3 w-3' />}
                </Button>
              </div>

              {!selectedCollapsed && selectedItems.length > 0 && (
                <div className='mt-3 space-y-2'>
                  {selectedItems.map((item: any) => (
                    <div key={item.item_id} className='flex items-center space-x-3'>
                      <Checkbox
                        id={`selected-${item.item_id}`}
                        checked={tempSelectedItems.includes(item.item_id)}
                        onCheckedChange={() => handleItemToggle(item.item_id)}
                      />
                      <label htmlFor={`selected-${item.item_id}`} className='flex-1 cursor-pointer text-sm'>
                        {item.item_name}
                      </label>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Remaining items list */}
            <div className='rounded-lg bg-gray-50 p-3'>
              <div className='flex items-center space-x-3'>
                <div className='text-sm font-medium text-gray-700'>Còn lại {unselectedItems.length}</div>
                <Button
                  variant='ghost'
                  size='sm'
                  className='ml-auto h-6 px-2 text-xs'
                  onClick={() => setRemainingCollapsed(!remainingCollapsed)}
                >
                  {remainingCollapsed ? <ChevronRight className='h-3 w-3' /> : <ChevronDown className='h-3 w-3' />}
                </Button>
              </div>

              {!remainingCollapsed && (
                <div className='mt-3 max-h-60 space-y-2 overflow-y-auto'>
                  {unselectedItems.map((item: any) => (
                    <div key={item.item_id} className='flex items-center space-x-3'>
                      <Checkbox
                        id={item.item_id}
                        checked={tempSelectedItems.includes(item.item_id)}
                        onCheckedChange={() => handleItemToggle(item.item_id)}
                      />
                      <label htmlFor={item.item_id} className='flex-1 cursor-pointer text-sm'>
                        {item.item_name}
                      </label>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </PosModal>
  )
}
