import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { Dialog, DialogContent, DialogHeader, DialogTitle, Button, Input } from '@/components/ui'

interface CancelDeviceModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  storeName?: string
  storeId?: string
}

export function CancelDeviceModal({ open, onOpenChange, storeName, storeId }: CancelDeviceModalProps) {
  const [deviceCode, setDeviceCode] = useState('')
  const navigate = useNavigate()

  const handleGetDeviceCode = () => {
    if (storeId) {
      navigate({
        to: `/setting/store/detail/${storeId}`,
        hash: 'manageDeviceCode'
      })
      onOpenChange(false)
    }
  }

  const handleConfirm = () => {
    onOpenChange(false)
  }

  const handleCancel = () => {
    setDeviceCode('')
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='text-center text-xl font-semibold'>Hủy thiết bị</DialogTitle>
        </DialogHeader>

        <div className='space-y-6 py-4'>
          {/* Description */}
          <div className='text-center text-gray-700'>
            <p>
              Bạn cần nhập mã quản lý thiết bị của cửa hàng{' '}
              <span className='font-semibold'>{storeName || 'Tutimi-Bình Lợi'}</span> để xác nhận hủy thiết bị.
            </p>
          </div>

          {/* Get Device Code Button */}
          <div className='flex justify-center'>
            <Button
              type='button'
              variant='secondary'
              onClick={handleGetDeviceCode}
              className='bg-gray-500 text-white hover:bg-gray-600'
            >
              Lấy mã quản lý thiết bị
            </Button>
          </div>

          {/* Device Code Input */}
          <div className='space-y-2'>
            <Input
              placeholder='Nhập mã quản lý thiết bị'
              value={deviceCode}
              onChange={e => setDeviceCode(e.target.value)}
              className='text-center'
            />
          </div>

          {/* Action Buttons */}
          <div className='flex justify-between gap-4'>
            <Button type='button' variant='outline' onClick={handleCancel} className='flex-1'>
              Hủy
            </Button>
            <Button
              type='button'
              onClick={handleConfirm}
              disabled={!deviceCode.trim()}
              className='flex-1 bg-red-500 text-white hover:bg-red-600 disabled:bg-gray-300 disabled:text-gray-500'
            >
              Xác nhận
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
