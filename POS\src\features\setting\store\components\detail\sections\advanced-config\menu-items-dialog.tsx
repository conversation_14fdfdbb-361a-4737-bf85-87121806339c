import { useNavigate } from '@tanstack/react-router'

import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Badge,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui'

interface MenuItemsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  menuItems: any[]
  isLoading?: boolean
}

export function MenuItemsDialog({ open, onOpenChange, menuItems, isLoading = false }: MenuItemsDialogProps) {
  const navigate = useNavigate()

  const handleRowClick = (itemId: string) => {
    navigate({ to: '/menu/items/items-in-store/detail/$id', params: { id: itemId } })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-xl overflow-hidden lg:max-w-5xl'>
        <DialogHeader>
          <DialogTitle className='text-center'><PERSON>h sách món thay đổi so với thực đơn gốc</DialogTitle>
        </DialogHeader>

        <div className='max-h-[60vh] overflow-auto'>
          {isLoading ? (
            <div className='flex items-center justify-center py-8'>
              <div className='text-sm text-gray-500'>Đang tải danh sách món...</div>
            </div>
          ) : menuItems.length > 0 ? (
            <Table>
              <TableHeader className='bg-background sticky top-0'>
                <TableRow>
                  <TableHead className='w-[60px]'>#</TableHead>
                  <TableHead className='w-[250px]'>Tên món</TableHead>
                  <TableHead>Giá bán</TableHead>
                  <TableHead>Nhóm món</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead className='w-[100px]'></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {menuItems.map((item, index) => (
                  <TableRow
                    key={item.id}
                    className='hover:bg-muted/50 cursor-pointer'
                    onClick={() => handleRowClick(item.id)}
                  >
                    <TableCell className='font-medium'>{index + 1}</TableCell>
                    <TableCell>{item.item_name}</TableCell>
                    <TableCell>{(item.ots_price || item.ta_price || 0).toLocaleString('vi-VN')} ₫</TableCell>
                    <TableCell className='text-muted-foreground'></TableCell>
                    <TableCell>{item.status}</TableCell>
                    <TableCell>
                      {item.active === 1 ? (
                        <Badge variant='secondary' className='bg-green-100 text-green-800'>
                          Active
                        </Badge>
                      ) : (
                        <Badge variant='outline' className='bg-red-100 text-red-800'>
                          Deactive
                        </Badge>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className='flex items-center justify-center py-8'>
              <div className='text-sm text-gray-500'>Không có món nào thay đổi</div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
