import React from 'react'

import { usePromotionsData } from '@/hooks/api'

import { But<PERSON> } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'

interface PromotionPickerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedIds: string[]
  onSelectedChange: (ids: string[]) => void
}

export const PromotionPickerDialog: React.FC<PromotionPickerDialogProps> = ({
  open,
  onOpenChange,
  selectedIds,
  onSelectedChange
}) => {
  const [search, setSearch] = React.useState('')

  const { promotions, isLoading } = usePromotionsData({ enabled: open, searchTerm: search })

  const toggle = (id: string, checked: boolean) => {
    if (checked) onSelectedChange([...selectedIds, id])
    else onSelectedChange(selectedIds.filter(x => x !== id))
  }

  const filtered = React.useMemo(() => {
    const term = search.trim().toLowerCase()
    if (!term) return promotions
    return promotions.filter(p => p.name.toLowerCase().includes(term))
  }, [promotions, search])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] w-[800px] max-w-4xl lg:max-w-4xl'>
        <div className='space-y-6 p-2'>
          <Input placeholder='Tìm kiếm' value={search} onChange={e => setSearch(e.target.value)} />
          <ScrollArea className='h-[60vh] w-full'>
            <div className='space-y-4'>
              {selectedIds.length > 0 && (
                <div>
                  <div className='mb-3 flex items-center gap-2 rounded bg-green-50 p-3 text-base font-medium text-green-600'>
                    <span>✓ Đã chọn {selectedIds.length}</span>
                  </div>
                  <div className='space-y-3'>
                    {filtered
                      .filter(p => selectedIds.includes(p.code))
                      .map(p => (
                        <div key={p.code} className='flex items-center space-x-3 rounded p-2 hover:bg-gray-50'>
                          <Checkbox
                            id={p.code}
                            checked={true}
                            onCheckedChange={checked => toggle(p.code, !!checked)}
                            className='h-5 w-5'
                          />
                          <Label htmlFor={p.code} className='flex-1 cursor-pointer text-base'>
                            {p.name}
                          </Label>
                        </div>
                      ))}
                  </div>
                </div>
              )}
              <div>
                <div className='mb-3 rounded bg-gray-50 p-3 text-base font-medium text-gray-600'>
                  <span>Còn lại {filtered.filter(p => !selectedIds.includes(p.code)).length}</span>
                </div>
                <div className='space-y-3'>
                  {isLoading ? (
                    <div className='text-muted-foreground p-4 text-center'>Đang tải...</div>
                  ) : (
                    filtered
                      .filter(p => !selectedIds.includes(p.code))
                      .map(p => (
                        <div key={p.code} className='flex items-center space-x-3 rounded p-2 hover:bg-gray-50'>
                          <Checkbox
                            id={p.code}
                            checked={false}
                            onCheckedChange={checked => toggle(p.code, !!checked)}
                            className='h-5 w-5'
                          />
                          <Label htmlFor={p.code} className='flex-1 cursor-pointer text-base'>
                            {p.name}
                          </Label>
                        </div>
                      ))
                  )}
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>
        <DialogFooter>
          <Button variant='outline' onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button onClick={() => onOpenChange(false)}>Xong</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
