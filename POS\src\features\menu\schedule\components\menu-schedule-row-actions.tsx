import { Row } from '@tanstack/react-table'

import { Trash2 } from 'lucide-react'

import { Button } from '@/components/ui/button'

import { useMenuSchedule } from '../context'
import { MenuSchedule } from '../data'

interface MenuScheduleRowActionsProps {
  row: Row<MenuSchedule>
}

export function MenuScheduleRowActions({ row }: MenuScheduleRowActionsProps) {
  const { setOpen, setCurrentRow } = useMenuSchedule()
  const schedule = row.original

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation() // Ngăn click event bubble up
    setCurrentRow(schedule)
    setOpen('delete')
  }

  return (
    <Button
      variant='ghost'
      size='sm'
      onClick={handleDelete}
      className='h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700'
    >
      <Trash2 className='h-4 w-4' />
      <span className='sr-only'>Xóa</span>
    </Button>
  )
}
