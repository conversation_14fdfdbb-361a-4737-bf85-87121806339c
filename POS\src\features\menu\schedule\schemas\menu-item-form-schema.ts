import { z } from 'zod'

export const menuItemFormSchema = z.object({
  item_name: z.string().optional(),
  ots_price: z.coerce.number().min(0, '<PERSON><PERSON><PERSON> ph<PERSON>i lớn hơn hoặc bằng 0').optional(),
  item_id: z.string().optional(),
  item_id_barcode: z.string().optional(),
  is_eat_with: z.boolean().optional(),
  no_update_quantity: z.boolean().optional(),
  item_class_uid: z.string().optional(),
  item_type_uid: z.string().optional(),
  description: z.string().optional(),
  store_uid: z.string().optional(),
  sku: z.string().optional(),
  unit_uid: z.string().optional(),
  unit_secondary_uid: z.string().optional(),
  // Advanced fields
  vat_rate: z.coerce.number().min(0).max(100).optional(),
  cooking_time: z.coerce.number().min(0).optional(),
  allow_edit_price: z.boolean().optional(),
  require_quantity_input: z.boolean().optional(),
  allow_remove_without_permission: z.boolean().optional(),
  is_featured: z.boolean().optional(),
  is_buffet: z.boolean().optional(),
  inqr_formula: z.string().optional(),
  is_service: z.boolean().optional(),
  customization: z.string().optional(),
  selected_days: z.array(z.boolean()).optional(),
  selected_hours: z.record(z.boolean()).optional(),
  display_order: z.coerce.number().optional(),
  time_sale_date_week: z.coerce.number().optional(),
  time_sale_hour_day: z.coerce.number().optional()
})

export type MenuItemFormData = z.infer<typeof menuItemFormSchema>
