import { IconPlus, IconChevronDown, IconDownload, IconFileImport } from '@tabler/icons-react'

import { toast } from 'sonner'

import { useExportItemCategories } from '@/hooks/api'

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

import { Button, Input } from '@/components/ui'

import { useCategoriesImport } from '../hooks/use-categories-import'
import { ImportCategoriesModal } from './modals/import-categories-modal'

function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  if (typeof error === 'string') {
    return error
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }
  return 'Đã xảy ra lỗi không xác định'
}

interface ActionBarProps {
  searchQuery: string
  onSearchQueryChange: (value: string) => void
  onSearchSubmit: () => void
  onCreateCategory: () => void
  isCreating: boolean
}

export function ActionBar({
  searchQuery,
  onSearchQueryChange,
  onSearchSubmit,
  onCreateCategory,
  isCreating
}: ActionBarProps) {
  const exportCategoriesMutation = useExportItemCategories()
  const importHook = useCategoriesImport()

  const handleExportCategories = async () => {
    try {
      const blob = await exportCategoriesMutation.mutateAsync({})
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `nhom-mon-${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      toast.success('Xuất nhóm món thành công!')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleImportCategories = () => {
    importHook.handleOpenImportModal()
  }

  const handleImportModalCancel = () => {
    importHook.resetImportState()
  }

  const handleImportModalConfirm = async () => {
    const success = await importHook.handleSaveImportedCategories()
    if (success) {
      // Modal will be closed by resetImportState in the hook
    }
  }

  return (
    <div className='mb-6 flex items-center justify-between'>
      <div className='flex flex-col'>
        <h2 className='text-2xl font-semibold'>Nhóm món toàn thương hiệu</h2>
        <p className='mt-1 text-sm text-gray-600'>
          Nhóm món giúp bạn sắp xếp và tổ chức các món, báo cáo về doanh số bán hàng và định tuyến các món đến máy in cụ
          thể.
        </p>
      </div>
      <div className='flex items-center gap-4'>
        <Input
          placeholder='Tìm kiếm nhóm món...'
          className='w-64'
          value={searchQuery}
          onChange={e => onSearchQueryChange(e.target.value)}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              e.preventDefault()
              onSearchSubmit()
            }
          }}
        />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size='sm'>
              Tiện ích
              <IconChevronDown className='ml-2 h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={handleImportCategories} className='flex items-center gap-2'>
              <IconFileImport className='h-4 w-4' />
              Thêm nhóm từ file
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleExportCategories}
              className='flex items-center gap-2'
              disabled={exportCategoriesMutation.isPending}
            >
              <IconDownload className='h-4 w-4' />
              {exportCategoriesMutation.isPending ? 'Đang xuất...' : 'Xuất nhóm món'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <Button onClick={onCreateCategory} className='flex items-center gap-2' disabled={isCreating}>
          <IconPlus className='h-4 w-4' />
          {isCreating ? 'Đang tạo...' : 'Tạo nhóm'}
        </Button>
      </div>

      <ImportCategoriesModal
        open={importHook.importModalOpen}
        onOpenChange={importHook.setImportModalOpen}
        showImportParsedData={importHook.showImportParsedData}
        importSelectedFile={importHook.importSelectedFile}
        importParsedData={importHook.importParsedData}
        isLoading={importHook.isLoading}
        onCancel={handleImportModalCancel}
        onConfirm={handleImportModalConfirm}
        onDownloadTemplate={importHook.handleDownloadTemplate}
        onImportFileUpload={importHook.handleImportFileUpload}
      />

      {/* Hidden file input for import */}
      <input
        ref={importHook.importFileInputRef}
        type='file'
        accept='.xlsx,.xls'
        onChange={importHook.handleImportFileChange}
        className='hidden'
      />
    </div>
  )
}
