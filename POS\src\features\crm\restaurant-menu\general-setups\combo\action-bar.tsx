import { Button } from '@/components/ui/button'
import { RotateCcw, Eye } from 'lucide-react'

interface ComboActionBarProps {
  onSyncItems: () => void
  onViewPreview: () => void
  className?: string
}

export function ComboActionBar({ 
  onSyncItems, 
  onViewPreview,
  className = '' 
}: ComboActionBarProps) {
  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      <Button 
        variant='outline' 
        onClick={onSyncItems} 
        className='text-orange-600 border-orange-600 hover:bg-orange-50'
      >
        <RotateCcw className='h-4 w-4 mr-2' />
        Đồng bộ combo
      </Button>
      <Button variant='outline' onClick={onViewPreview}>
        <Eye className='h-4 w-4 mr-2' />
        Xem trước
      </Button>
    </div>
  )
}
