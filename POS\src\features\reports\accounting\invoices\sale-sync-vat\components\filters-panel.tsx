import { useMemo } from 'react'
import { usePosStores } from '@/stores/posStore'
import { useAllStores } from '@/hooks/use-all-stores'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DateRangePicker } from './date-range-picker'

interface FiltersPanelProps {
  dateRange: {
    from: Date
    to: Date
  }
  onDateRangeChange: (dateRange: { from: Date; to: Date }) => void
  selectedStores: string[]
  onStoreChange: (stores: string[]) => void
  selectedSources?: string[]
  onSourceChange?: (sources: string[]) => void
  filterType: 'monthly' | 'daily'
  onFilterTypeChange: (filterType: 'monthly' | 'daily') => void
  className?: string
}

export function FiltersPanel({
  dateRange,
  onDateRangeChange,
  selectedStores,
  onStoreChange,
  filterType,
  onFilterTypeChange,
  className,
}: FiltersPanelProps) {
  const { currentBrandStores } = usePosStores()
  const { stores: allStoresFromAPI } = useAllStores()

  const availableStores =
    allStoresFromAPI.length > 0 ? allStoresFromAPI : currentBrandStores

  const activeStores = useMemo(() => {
    return availableStores.filter((store) => store.active === 1)
  }, [availableStores])

  return (
    <div className={className}>
      <div className='space-y-4'>
        {/* Filter Type Toggle */}
        <div className='space-y-2'>
          <Label className='text-sm font-medium'>Loại bộ lọc</Label>
          <div className='flex gap-2'>
            <Button
              variant={filterType === 'monthly' ? 'default' : 'outline'}
              size='sm'
              onClick={() => onFilterTypeChange('monthly')}
            >
              Theo tháng
            </Button>
            <Button
              variant={filterType === 'daily' ? 'default' : 'outline'}
              size='sm'
              onClick={() => onFilterTypeChange('daily')}
            >
              Theo ngày
            </Button>
          </div>
        </div>

        {/* Date Range Picker */}
        <DateRangePicker
          dateRange={dateRange}
          onDateRangeChange={onDateRangeChange}
          filterType={filterType}
        />

        {/* Other Filters */}
        <div className='flex flex-wrap gap-4'>
          {/* <div className='space-y-2'>
            <Label htmlFor='promotion-select' className='text-sm font-medium'>
              Tên CTKM
            </Label>
            <Select defaultValue='all-ctm'>
              <SelectTrigger id='promotion-select' className='w-[180px]'>
                <SelectValue placeholder='Chọn CTKM' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all-ctm'>Tất cả</SelectItem>
                <SelectItem value='promotion-1'>Khuyến mãi 1</SelectItem>
                <SelectItem value='promotion-2'>Khuyến mãi 2</SelectItem>
              </SelectContent>
            </Select>
          </div> */}

          <div className='space-y-2'>
            <Label htmlFor='store-select' className='text-sm font-medium'>
              Cửa hàng
            </Label>
            <Select
              value={selectedStores[0] || 'all-stores'}
              onValueChange={(value) => onStoreChange([value])}
            >
              <SelectTrigger id='store-select' className='w-[180px]'>
                <SelectValue placeholder='Chọn cửa hàng' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all-stores'>Tất cả cửa hàng</SelectItem>
                {activeStores.map((store) => (
                  <SelectItem key={store.id} value={store.id}>
                    {store.store_name}
                  </SelectItem>
                ))}
                {activeStores.length === 0 && (
                  <SelectItem value='no-stores' disabled>
                    Không có cửa hàng
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* <div className='space-y-2'>
            <Label htmlFor='source-select' className='text-sm font-medium'>
              Nguồn bán hàng
            </Label>
            <Select
              value={selectedSources[0] || 'all-sources'}
              onValueChange={(value) => {
                if (onSourceChange) {
                  if (value === 'all-sources') {
                    onSourceChange(['all-sources'])
                  } else {
                    onSourceChange([value])
                  }
                }
              }}
            >
              <SelectTrigger id='source-select' className='w-[180px]'>
                <SelectValue placeholder='Chọn nguồn' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all-sources'>Tất cả nguồn</SelectItem>
                {availableSources.map((source) => (
                  <SelectItem key={source.sourceId} value={source.sourceId}>
                    {source.sourceName}
                  </SelectItem>
                ))}
                {sourcesLoading && (
                  <SelectItem value='loading-sources' disabled>
                    Đang tải nguồn...
                  </SelectItem>
                )}
                {!sourcesLoading && availableSources.length === 0 && (
                  <SelectItem value='no-sources' disabled>
                    Không có nguồn
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div> */}
        </div>
      </div>
    </div>
  )
}
