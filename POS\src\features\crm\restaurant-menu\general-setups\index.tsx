import { useState } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { useStoreMenu } from './hooks'
import { StoresDataTable, createStoresColumns, SearchBar, StoreInfoSidebar } from './components'

import type { StoreMenuPageProps, Store } from './types'

const StoreMenuPage = ({ className }: StoreMenuPageProps) => {
  const navigate = useNavigate()
  const { stores, filters, updateFilters, isLoading } = useStoreMenu()

  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [selectedStore, setSelectedStore] = useState<Store | null>(null)
  const [isSaving, setIsSaving] = useState(false)

  const handleSearchChange = (search: string) => {
    updateFilters({ search })
  }

  const handleRowClick = (store: Store) => {
    console.log('Store clicked:', store)
  }

  const handleStoreInfoClick = (store: Store) => {
    setSelectedStore(store)
    setSidebarOpen(true)
  }

  const handleMenuClick = (store: Store) => {
    navigate({
      to: '/crm/general-setups/items',
      search: { pos: String(store.id) }
    })
  }

  const handleSidebarClose = (open: boolean) => {
    setSidebarOpen(open)
    if (!open) {
      setSelectedStore(null)
    }
  }

  const handleSaveStoreInfo = async () => {
    if (!selectedStore) return

    setIsSaving(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))

      setSidebarOpen(false)
      setSelectedStore(null)
    } catch (error) {
      console.error('Error saving store info:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const columns = createStoresColumns({
    onStoreInfoClick: handleStoreInfoClick,
    onMenuClick: handleMenuClick
  })

  if (isLoading) {
    return (
      <div className={`container mx-auto p-6 ${className || ''}`}>
        <div className='mb-6'>
          <h1 className='text-3xl font-bold mb-2'>Nhà hàng và thực đơn</h1>
        </div>
        <div className='rounded-md border'>
          <div className='p-8 text-center'>
            <p className='text-muted-foreground'>Đang tải...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`container mx-auto p-6 ${className || ''}`}>
      <div className='mb-6'>
        <h1 className='text-3xl font-bold mb-2'>Nhà hàng và thực đơn</h1>
      </div>

      <div className='mb-4'>
        <SearchBar
          value={filters.search}
          onChange={handleSearchChange}
          placeholder='Tên / ID nhà hàng'
        />
      </div>

      <StoresDataTable
        columns={columns}
        data={stores}
        onRowClick={handleRowClick}
      />

      {stores.length > 0 && (
        <div className='mt-4 text-sm text-muted-foreground text-center'>
          Tổng số {stores.length} nhà hàng
        </div>
      )}

      <StoreInfoSidebar
        open={sidebarOpen}
        onOpenChange={handleSidebarClose}
        store={selectedStore}
        onSave={handleSaveStoreInfo}
        isLoading={isSaving}
      />
    </div>
  )
}

export default StoreMenuPage

export { default as MenuItemsPage } from './menu-items'
export { StoreMenuPage }
export * from './components'
export * from './item-type'
export * from './items'
export * from './combo'
export * from './combo-special'
export * from './hooks'
export * from './types'
