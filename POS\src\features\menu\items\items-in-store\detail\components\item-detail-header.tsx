import { useRouter, useCanGoBack } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { Button } from '@/components/ui/button'

import { ItemInStore } from '../../hooks'

interface ItemDetailHeaderProps {
  isUpdate: boolean
  currentRow?: ItemInStore
  isLoading: boolean
  isCopy: boolean
  onSave: () => void
  onDeactive?: () => void
  onActive?: () => void
  isDeactivating?: boolean
  isActivating?: boolean
}

export function ItemDetailHeader({
  isUpdate,
  currentRow,
  isLoading,
  onSave,
  onDeactive,
  onActive,
  isDeactivating = false,
  isActivating = false
}: ItemDetailHeaderProps) {
  const router = useRouter()
  const canGoBack = useCanGoBack()

  const handleClose = () => {
    if (canGoBack) {
      router.history.back()
    } else {
      router.invalidate()
    }
  }

  const getTitle = () => {
    if (isUpdate) return 'Chi tiết món'
    return 'Tạo món'
  }

  return (
    <div className='mb-8'>
      <div className='mb-4 flex items-center justify-between'>
        <div>
          <Button variant='ghost' size='sm' onClick={handleClose} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
        </div>
        <h2 className='mb-2 text-3xl font-medium'>{getTitle()}</h2>
        <div className='flex gap-2'>
          {isUpdate && currentRow?.active && onDeactive && (
            <Button
              type='button'
              variant='outline'
              className='border-red-500 text-red-500 hover:bg-red-50'
              disabled={isDeactivating}
              onClick={onDeactive}
            >
              {isDeactivating ? 'Đang deactive...' : 'Deactive'}
            </Button>
          )}
          {isUpdate && !currentRow?.active && onActive && (
            <Button
              type='button'
              variant='outline'
              className='border-green-500 text-green-500 hover:bg-green-50'
              disabled={isActivating}
              onClick={onActive}
            >
              {isActivating ? 'Đang active...' : 'Active'}
            </Button>
          )}

          <Button type='button' disabled={isLoading} onClick={onSave}>
            {isLoading ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>
      </div>
    </div>
  )
}
