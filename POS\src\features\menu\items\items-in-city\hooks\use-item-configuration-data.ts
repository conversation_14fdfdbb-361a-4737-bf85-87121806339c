import { UseFormReturn } from 'react-hook-form'

import { useAuthStore } from '@/stores'

import { useCurrentBrand } from '@/stores/posStore'

import {
  useCitiesData,
  useCustomizationsData,
  useCustomizationById,
  useItemsData,
  useSourcesData,
  useItemTypesData,
  useUnitsData,
  useItemClassesData
} from '@/hooks/api'

import type { FormValues } from '../detail/components/item-detail-form'

interface UseItemConfigurationDataProps {
  form?: UseFormReturn<FormValues>
}

export function useItemConfigurationData({ form }: UseItemConfigurationDataProps = {}) {
  const selectedCityUid = form?.watch('city_uid')
  const selectedCustomizationUid = form?.watch('customization_uid')

  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const { data: sourcesData = [] } = useSourcesData({
    company_uid: company?.id,
    brand_uid: selectedBrand?.id,
    skip_limit: true,
    enabled: !!company?.id && !!selectedBrand?.id
  })
  const { data: customizations = [] } = useCustomizationsData({
    skip_limit: true,
    list_city_uid: selectedCityUid !== 'all' ? [selectedCityUid || ''] : undefined,
    enabled: !!selectedCityUid
  })
  const { data: customizationDetails } = useCustomizationById(
    selectedCustomizationUid || '',
    !!selectedCustomizationUid && selectedCustomizationUid !== 'none'
  )
  const { data: items = [] } = useItemsData({
    params: {
      city_uid: selectedCityUid,
      skip_limit: true
    },
    enabled: !!selectedCityUid
  })

  const { data: cities = [] } = useCitiesData()
  const { data: itemTypes = [] } = useItemTypesData()
  const { data: units = [] } = useUnitsData()
  const { data: itemClasses = [] } = useItemClassesData({ skip_limit: true })

  return {
    selectedCityUid,
    selectedCustomizationUid,

    company,
    selectedBrand,

    customizations,
    customizationDetails,
    items,
    sourcesData,
    cities,
    itemTypes,
    units,
    itemClasses
  }
}
