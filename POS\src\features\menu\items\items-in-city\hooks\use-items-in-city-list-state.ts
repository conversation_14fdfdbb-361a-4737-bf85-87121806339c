import { useState } from 'react'

import type { ItemsInCity } from '../data'

export function useItemsInCityListState() {
  const [isCustomizationDialogOpen, setIsCustomizationDialogOpen] = useState(false)
  const [isBuffetItem, setIsBuffetItem] = useState(false)
  const [selectedMenuItem, setSelectedMenuItem] = useState<ItemsInCity | null>(null)
  const [isBuffetConfigModalOpen, setIsBuffetConfigModalOpen] = useState(false)
  const [selectedBuffetMenuItem, setSelectedBuffetMenuItem] = useState<string[]>([])
  const [selectedItemTypeUid, setSelectedItemTypeUid] = useState<string>('all')
  const [selectedCityUid, setSelectedCityUid] = useState<string>('all')
  const [selectedDaysOfWeek, setSelectedDaysOfWeek] = useState<string[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  return {
    isCustomizationDialogOpen,
    isBuffetItem,
    selectedMenuItem,
    isBuffetConfigModalOpen,
    selectedBuffetMenuItem,
    selectedItemTypeUid,
    selectedCityUid,
    selectedDaysOfWeek,
    selectedStatus,

    setIsCustomizationDialogOpen,
    setIsBuffetItem,
    setSelectedMenuItem,
    setIsBuffetConfigModalOpen,
    setSelectedBuffetMenuItem,
    setSelectedItemTypeUid,
    setSelectedCityUid,
    setSelectedDaysOfWeek,
    setSelectedStatus
  }
}
