import type { Item } from '@/types/api'

import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import type { MenuItem } from '../types/menu-schedule-api'

interface MenuItemSelectorProps {
  selectedItemId: string
  onItemSelect: (itemId: string) => void
  selectedItem?: MenuItem | null
  items: Item[]
}

export function MenuItemSelector({
  selectedItemId,
  onItemSelect,
  selectedItem,
  items
}: MenuItemSelectorProps) {
  return (
    <div className='flex items-center gap-4'>
      <Label className='w-34 rounded-sm bg-gray-100 px-3 py-2 text-sm font-medium whitespace-nowrap text-gray-700'>
        Chọn món <span className='text-red-500'>*</span>
      </Label>
      {selectedItem ? (
        <div className='flex-1 rounded-md border bg-gray-50 px-3 py-2 text-sm text-gray-700'>
          {selectedItem.name} ({selectedItem.code})
        </div>
      ) : (
        <Select value={selectedItemId} onValueChange={onItemSelect} disabled={!items.length}>
          <SelectTrigger className='flex-1'>
            <SelectValue
              placeholder={!items.length ? 'Không có món nào' : 'Chọn món để cập nhật'}
            />
          </SelectTrigger>
          <SelectContent>
            {items.map(item => (
              <SelectItem key={item.id} value={item.id}>
                {item.item_name} ({item.item_id})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
    </div>
  )
}
