import { IconChevronDown, IconChevronUp } from '@tabler/icons-react'
import { Checkbox } from '@/components/ui'
import type { BrandWithCities } from '../types'

interface HierarchicalListProps {
  filteredData: BrandWithCities[]
  expandedBrands: Set<string>
  expandedCities: Set<string>
  toggleBrandExpansion: (brandId: string) => void
  toggleCityExpansion: (cityId: string) => void
  handleItemToggle: (itemId: string, type: 'brand' | 'city' | 'store') => void
  isItemSelected: (itemId: string, type: 'brand' | 'city' | 'store') => boolean
  isItemIndeterminate: (itemId: string, type: 'brand' | 'city') => boolean
}

export function HierarchicalList({
  filteredData,
  expandedBrands,
  expandedCities,
  toggleBrandExpansion,
  toggleCityExpansion,
  handleItemToggle,
  isItemSelected,
  isItemIndeterminate,
}: HierarchicalListProps) {
  return (
    <div className='max-h-96 overflow-y-auto rounded-md border'>
      {filteredData.map((brand) => (
        <div key={brand.id} className='border-b last:border-b-0'>
          <div className='flex items-center gap-2 p-3 hover:bg-gray-50'>
            <Checkbox
              checked={isItemSelected(brand.id, 'brand')}
              onCheckedChange={() => handleItemToggle(brand.id, 'brand')}
              ref={(el) => {
                if (el) {
                  const input = el.querySelector('input') as HTMLInputElement
                  if (input) {
                    input.indeterminate = isItemIndeterminate(brand.id, 'brand')
                  }
                }
              }}
            />
            <button
              onClick={() => toggleBrandExpansion(brand.id)}
              className='flex flex-1 items-center gap-1 text-left font-medium'
            >
              {expandedBrands.has(brand.id) ? (
                <IconChevronUp className='h-4 w-4' />
              ) : (
                <IconChevronDown className='h-4 w-4' />
              )}
              {brand.brand_name}
            </button>
          </div>

          {expandedBrands.has(brand.id) && (
            <div className='pl-6'>
              {brand.cities.map((city) => (
                <div key={city.id}>
                  <div className='flex items-center gap-2 p-2 hover:bg-gray-50'>
                    <Checkbox
                      checked={isItemSelected(city.id, 'city')}
                      onCheckedChange={() => handleItemToggle(city.id, 'city')}
                      ref={(el) => {
                        if (el) {
                          const input = el.querySelector(
                            'input'
                          ) as HTMLInputElement
                          if (input) {
                            input.indeterminate = isItemIndeterminate(
                              city.id,
                              'city'
                            )
                          }
                        }
                      }}
                    />
                    <button
                      onClick={() => toggleCityExpansion(city.id)}
                      className='flex flex-1 items-center gap-1 text-left'
                    >
                      {expandedCities.has(city.id) ? (
                        <IconChevronUp className='h-4 w-4' />
                      ) : (
                        <IconChevronDown className='h-4 w-4' />
                      )}
                      {city.city_name}
                    </button>
                  </div>

                  {expandedCities.has(city.id) && (
                    <div className='pl-6'>
                      {city.stores.map((store) => (
                        <div
                          key={store.id}
                          className='flex items-center gap-2 p-2 hover:bg-gray-50'
                        >
                          <Checkbox
                            checked={isItemSelected(store.id, 'store')}
                            onCheckedChange={() =>
                              handleItemToggle(store.id, 'store')
                            }
                          />
                          <span className='flex-1'>{store.store_name}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}
