import { useEffect, useState } from 'react'
import L from 'leaflet'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'

delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png'
})

interface AddressMapDisplayProps {
  address: string
  className?: string
  height?: string
}

export function AddressMapDisplay({ 
  address, 
  className = '',
  height = '300px'
}: AddressMapDisplayProps) {
  const defaultCenter: [number, number] = [10.8231, 106.6297]
  const [coordinates, setCoordinates] = useState<[number, number]>(defaultCenter)
  const [isLoading, setIsLoading] = useState(false)
  const [hasValidLocation, setHasValidLocation] = useState(false)

  const geocodeAddress = async (addressToGeocode: string) => {
    if (!addressToGeocode.trim()) {
      setHasValidLocation(false)
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(addressToGeocode)}&accept-language=vi&limit=1&countrycodes=vn`
      )
      const data = await response.json()
      
      if (data.length > 0) {
        const lat = parseFloat(data[0].lat)
        const lng = parseFloat(data[0].lon)
        setCoordinates([lat, lng])
        setHasValidLocation(true)
      } else {
        setHasValidLocation(false)
      }
    } catch (error) {
      console.error('Error geocoding address:', error)
      setHasValidLocation(false)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (address) {
      geocodeAddress(address)
    } else {
      setHasValidLocation(false)
    }
  }, [address])

  if (!address.trim()) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 rounded-md border-2 border-dashed border-gray-300 ${className}`}
        style={{ height }}
      >
        <p className="text-gray-500 text-sm">Nhập địa chỉ để hiển thị bản đồ</p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 rounded-md border ${className}`}
        style={{ height }}
      >
        <p className="text-gray-500 text-sm">Đang tải bản đồ...</p>
      </div>
    )
  }

  return (
    <div className={`relative rounded-md overflow-hidden border ${className}`} style={{ height }}>
      <MapContainer
        center={coordinates}
        zoom={hasValidLocation ? 16 : 12}
        style={{ height: '100%', width: '100%' }}
        key={`${coordinates[0]}-${coordinates[1]}`}
        zoomControl={true}
        scrollWheelZoom={false}
        doubleClickZoom={false}
        dragging={true}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
        />
        {hasValidLocation && (
          <Marker position={coordinates} />
        )}
      </MapContainer>
      
      {!hasValidLocation && (
        <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
          <div className="bg-white px-3 py-2 rounded-md shadow-md">
            <p className="text-sm text-gray-600">Không tìm thấy vị trí chính xác</p>
          </div>
        </div>
      )}
      
      <div className="absolute top-2 right-2 flex flex-col gap-1">
        <div className="bg-white rounded shadow-md p-1">
          <div className="w-8 h-8 flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  )
}
