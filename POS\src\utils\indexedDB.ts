const DB_NAME = 'POS_AUTH_DB'
const DB_VERSION = 1
const STORE_NAME = 'auth_data'

interface AuthDBData {
  key: string
  value: unknown
  timestamp: number
}

class AuthIndexedDB {
  private db: IDBDatabase | null = null

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION)

      request.onerror = () => reject(request.error)
      request.onsuccess = () => {
        this.db = request.result
        resolve()
      }

      request.onupgradeneeded = event => {
        const db = (event.target as IDBOpenDBRequest).result

        if (!db.objectStoreNames.contains(STORE_NAME)) {
          const store = db.createObjectStore(STORE_NAME, { keyPath: 'key' })
          store.createIndex('timestamp', 'timestamp', { unique: false })
        }
      }
    })
  }

  async setItem(key: string, value: unknown): Promise<boolean> {
    try {
      if (!this.db) await this.init()

      return new Promise(resolve => {
        const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
        const store = transaction.objectStore(STORE_NAME)

        const data: AuthDBData = {
          key,
          value,
          timestamp: Date.now()
        }

        const request = store.put(data)

        request.onsuccess = () => resolve(true)
        request.onerror = () => resolve(false)
      })
    } catch {
      return false
    }
  }

  async getItem<T>(key: string, defaultValue: T): Promise<T> {
    try {
      if (!this.db) await this.init()

      return new Promise(resolve => {
        const transaction = this.db!.transaction([STORE_NAME], 'readonly')
        const store = transaction.objectStore(STORE_NAME)
        const request = store.get(key)

        request.onsuccess = () => {
          const result = request.result
          resolve(result ? result.value : defaultValue)
        }

        request.onerror = () => resolve(defaultValue)
      })
    } catch {
      return defaultValue
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      if (!this.db) await this.init()

      return new Promise(resolve => {
        const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
        const store = transaction.objectStore(STORE_NAME)
        const request = store.delete(key)

        request.onsuccess = () => resolve()
        request.onerror = () => resolve()
      })
    } catch {
      // Catch
    }
  }

  async clear(): Promise<void> {
    try {
      if (!this.db) await this.init()

      return new Promise(resolve => {
        const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
        const store = transaction.objectStore(STORE_NAME)
        const request = store.clear()

        request.onsuccess = () => resolve()
        request.onerror = () => resolve()
      })
    } catch {
      // Catch
    }
  }
}

export const authDB = new AuthIndexedDB()

export const hybridStorage = {
  async setItem(key: string, value: unknown): Promise<boolean> {
    const serialized = JSON.stringify(value)
    const sizeInBytes = new Blob([serialized]).size
    const sizeInMB = sizeInBytes / (1024 * 1024)

    if (sizeInMB < 1) {
      try {
        localStorage.setItem(key, serialized)
        return true
      } catch {
        return await authDB.setItem(key, value)
      }
    } else {
      return await authDB.setItem(key, value)
    }
  },

  async getItem<T>(key: string, defaultValue: T): Promise<T> {
    try {
      const item = localStorage.getItem(key)
      if (item) {
        return JSON.parse(item)
      }
    } catch {
      // Catch
    }

    return await authDB.getItem(key, defaultValue)
  },

  async removeItem(key: string): Promise<void> {
    localStorage.removeItem(key)
    await authDB.removeItem(key)
  }
}
