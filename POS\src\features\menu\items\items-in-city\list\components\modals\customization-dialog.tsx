'use client'

import { useEffect } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { useCurrentBrand } from '@/stores'
import type { Customization } from '@/types/customizations'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { Button } from '@/components/ui/button'
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'

import { Combobox } from '@/components/pos/combobox'

import { ItemsInCity } from '../../../data'
import { UpdateItemInCityRequest, useUpdateItemInCity } from '../../../hooks'

const customizationFormSchema = z.object({
  customization_uid: z.string().nullable()
})

type CustomizationFormValues = z.infer<typeof customizationFormSchema>

interface CustomizationDialogProps {
  item: ItemsInCity | null
  customizations: Customization[]
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CustomizationDialog({ item, customizations, open, onOpenChange }: CustomizationDialogProps) {
  const { updateItemAsync } = useUpdateItemInCity()
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const form = useForm<CustomizationFormValues>({
    resolver: zodResolver(customizationFormSchema),
    defaultValues: {
      customization_uid: 'none'
    }
  })

  useEffect(() => {
    if (!open) return
    try {
      form.reset({ customization_uid: item?.customization_uid ?? null })
    } catch (_error) {
      toast.error('Lỗi khi load customization data')
    }
  }, [open, form, item])

  const onSubmit = async (values: CustomizationFormValues) => {
    try {
      if (!item?.id || !company?.id || !selectedBrand?.id) {
        throw new Error('Required data is missing')
      }

      const customizationUid = values.customization_uid === 'none' ? null : values.customization_uid

      await updateItemAsync({
        ...item,
        customization_uid: customizationUid
      } as unknown as UpdateItemInCityRequest)

      onOpenChange(false)
    } catch (error) {
      toast.error('Lỗi khi cập nhật customization')
    }
  }

  if (!item) return null

  return (
    <Dialog
      open={open}
      onOpenChange={v => {
        onOpenChange(v)
        form.reset()
      }}
    >
      <DialogContent className='top-[20%] w-full max-w-4xl translate-y-[-50%]'>
        <DialogHeader>
          <DialogTitle className='text-center'>Cấu hình customization</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='customization_uid'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customization áp dụng cho món</FormLabel>
                  <FormControl>
                    <Combobox
                      value={field.value ?? ''}
                      onValueChange={val => field.onChange(val === '' ? null : val)}
                      options={customizations.map(o => ({ value: o.id, label: o.name }))}
                      placeholder='Chọn customization...'
                      searchPlaceholder='Tìm kiếm customization...'
                      emptyText='Không có dữ liệu'
                      className='w-full'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <DialogClose asChild>
                <Button variant='outline' type='button'>
                  Hủy
                </Button>
              </DialogClose>
              <Button type='submit' disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? 'Đang lưu...' : 'Lưu'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
