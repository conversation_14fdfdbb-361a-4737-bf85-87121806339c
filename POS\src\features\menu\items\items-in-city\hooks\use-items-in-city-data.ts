import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import { QUERY_KEYS } from '@/constants/query-keys'

import { useItemTypesData, useItemClassesData, useUnitsData } from '@/hooks/api'

import type { ItemsInCity } from '../data'
import { itemsInCityApiService } from './items-in-city-api'
import { type GetItemsInCityParams, type GetItemByListIdParams, type ItemInCity } from './items-in-city-types'

export interface UseItemsInCityDataOptions {
  params?: Partial<GetItemsInCityParams>
  enabled?: boolean
}

export const useItemsInCityData = (options: UseItemsInCityDataOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  const dynamicParams: GetItemsInCityParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
    reverse: 1,
    limit: 50,
    ...params
  }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  const itemsQuery = useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_CITY_LIST, JSON.stringify(dynamicParams)],
    queryFn: async (): Promise<ItemInCity[]> => {
      const response = await itemsInCityApiService.getItemsInCity(dynamicParams)
      return response.data || []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000 // 10 minutes
  })

  const nextPageParams: GetItemsInCityParams = {
    ...dynamicParams,
    page: (dynamicParams.page || 1) + 1
  }

  const nextPageQuery = useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_CITY_LIST, 'next', JSON.stringify(nextPageParams)],
    queryFn: async (): Promise<ItemInCity[]> => {
      const response = await itemsInCityApiService.getItemsInCity(nextPageParams)
      return response.data || []
    },
    enabled: enabled && hasRequiredAuth && (itemsQuery.data ? itemsQuery.data.length > 0 : false),
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000
  })

  const pageSize = dynamicParams.limit || 50
  const hasNextPage =
    (nextPageQuery.data ? nextPageQuery.data.length > 0 : false) ||
    (itemsQuery.data ? itemsQuery.data.length === pageSize : false)

  return {
    data: itemsQuery.data,
    isLoading: itemsQuery.isLoading,
    error: itemsQuery.error,
    refetch: itemsQuery.refetch,
    isFetching: itemsQuery.isFetching,
    nextPageData: nextPageQuery.data || [],
    hasNextPage
  }
}

export const useItemInCityDetail = (id?: string, enabled = true) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_CITY_DETAIL, id],
    queryFn: () => itemsInCityApiService.getItemById({ id: id! }),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000
  })
}

export const useItemByListId = (params: GetItemByListIdParams, enabled = true) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_CITY_LIST, 'by_list_id', params],
    queryFn: () => itemsInCityApiService.getItemByListId(params),
    enabled: enabled && !!params.list_item_id,
    staleTime: 5 * 60 * 1000
  })
}

export const useItemsInCityForTable = (options: UseItemsInCityDataOptions = {}) => {
  const itemsQuery = useItemsInCityData(options)
  const cityUid = options.params?.city_uid
  const listCityUid = options.params?.list_city_uid

  const { data: itemTypesData = [] } = useItemTypesData({
    skip_limit: true,
    ...(cityUid && cityUid !== 'all' ? { city_uid: cityUid } : {}),
    ...(listCityUid ? { list_city_uid: listCityUid } : {})
  })
  const { data: itemClassesData = [] } = useItemClassesData({ skip_limit: true })
  const { data: unitsData = [] } = useUnitsData()

  const transformedData =
    (itemsQuery.data?.map(item => {
      const itemData = item as unknown as Record<string, unknown>
      const cities = (itemData.cities as Array<{ city_name: string }>) || []
      const cityName = cities[0]?.city_name || ''

      const itemType = itemData.item_type_uid ? itemTypesData.find(type => type.id === itemData.item_type_uid) : null
      const itemTypeName = itemType?.item_type_name || ''

      const itemClass = itemData.item_class_uid ? itemClassesData.find(cls => cls.id === itemData.item_class_uid) : null
      const itemClassName = itemClass?.item_class_name || ''

      const unit = itemData.unit_uid ? unitsData.find(u => u.id === itemData.unit_uid) : null
      const unitName = unit?.unit_name || ''

      const hasEatWith = itemData.is_eat_with === 1 || (itemData.item_id_eat_with && itemData.item_id_eat_with !== '')
      const sideItemsValue = hasEatWith ? (itemData.item_id_eat_with as string) || 'Món ăn kèm' : ''

      return {
        ...item,
        code: (itemData.item_id as string) || '',
        name: item.item_name,
        price: item.ots_price,
        vatPercent: item.ots_tax,
        cookingTime: item.time_cooking,
        categoryGroup: itemTypeName,
        itemType: itemTypeName,
        itemClass: itemClassName,
        unit: unitName,
        sideItems: sideItemsValue || undefined,
        city: cityName,
        buffetConfig:
          (itemData.extra_data as Record<string, unknown>)?.is_buffet_item === 1 ? 'Đã cấu hình' : 'Chưa cấu hình',
        customization: (itemData.customization_uid as string) || undefined,
        isActive: Boolean(item.active),
        createdAt:
          typeof item.created_at === 'number'
            ? new Date(item.created_at * 1000)
            : new Date(new Date(item.created_at as unknown as string).getTime())
      }
    }) as unknown as ItemsInCity[]) || []

  return {
    data: transformedData,
    isLoading: itemsQuery.isLoading,
    error: itemsQuery.error,
    refetch: itemsQuery.refetch,
    isFetching: itemsQuery.isFetching,
    nextPageData: itemsQuery.nextPageData,
    hasNextPage: itemsQuery.hasNextPage
  }
}
