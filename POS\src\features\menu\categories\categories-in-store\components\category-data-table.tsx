import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table'

import { ItemType } from '@/lib/item-types-api'

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui'

interface CategoryDataTableProps {
  columns: ColumnDef<ItemType>[]
  data: ItemType[]
  onEditCategory?: (category: ItemType) => void
  onDeleteCategory?: (category: ItemType) => void
  onToggleStatus?: (category: ItemType) => void
  onRowClick?: (category: ItemType) => void
}

export function CategoryDataTable({
  columns,
  data,
  onEditCategory,
  onDeleteCategory,
  onToggleStatus,
  onRowClick
}: CategoryDataTableProps) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    meta: {
      onEditCategory,
      onDeleteCategory,
      onToggleStatus
    }
  })

  return (
    <ScrollArea className='h-[calc(100vh-300px)] rounded-md border'>
      <Table className='relative'>
        <TableHeader>
          {table.getHeaderGroups().map(headerGroup => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map(header => {
                return (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                )
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map(row => (
              <TableRow
                key={row.id}
                className={onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''}
                onClick={() => onRowClick?.(row.original)}
              >
                {row.getVisibleCells().map(cell => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className='h-24 text-center'>
                Không có dữ liệu.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <ScrollBar orientation='horizontal' />
    </ScrollArea>
  )
}
