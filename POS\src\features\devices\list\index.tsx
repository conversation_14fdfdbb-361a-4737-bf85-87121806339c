import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { Device } from '@/types'
import { toast } from 'sonner'

import { getErrorMessage } from '@/utils/error-utils'

import { useDevicesData, useStoresData, useCopyDevice } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Button, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

import { deviceColumns, DeviceCopyModal, DeviceDataTable } from './components'

export default function ListDevicesPage() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStore, setSelectedStore] = useState('all')
  const [selectedDeviceType, setSelectedDeviceType] = useState('all')
  const [copyModalOpen, setCopyModalOpen] = useState(false)
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null)
  const [currentPage, setCurrentPage] = useState(1)

  const copyDeviceMutation = useCopyDevice()

  const { data: storesData, isLoading: storesLoading, error: storesError } = useStoresData()

  const activeStoreIds = storesData?.filter(store => store.isActive).map(store => store.id) || []

  const shouldSearch = !searchTerm || searchTerm.length >= 3

  const {
    data: devices,
    isLoading: devicesLoading,
    error: devicesError
  } = useDevicesData({
    searchTerm: shouldSearch ? searchTerm || undefined : undefined,
    listStoreUid: selectedStore !== 'all' ? selectedStore : activeStoreIds.join(','),
    deviceType: selectedDeviceType !== 'all' ? selectedDeviceType : undefined,
    page: currentPage,
    storesData: storesData?.map(store => ({
      id: store.id,
      store_name: store.name
    }))
  })

  const isLoading = storesLoading || devicesLoading
  const error = storesError || devicesError

  const handleCopyDevice = (device: Device) => {
    setSelectedDevice(device)
    setCopyModalOpen(true)
  }

  const handleCopyConfirm = async (deviceName: string) => {
    if (!selectedDevice) return

    try {
      await copyDeviceMutation.mutateAsync({
        deviceId: selectedDevice.id,
        newDeviceName: deviceName
      })

      toast.success(`Thiết bị "${deviceName}" đã được sao chép thành công!`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Đã xảy ra lỗi khi sao chép thiết bị'
      toast.error(errorMessage)
      throw error
    }
  }

  const handleCreateDevice = () => {
    navigate({ to: '/devices/new' })
  }

  const handleRowClick = (device: Device) => {
    navigate({
      to: '/devices/detail/$id',
      params: { id: device.id },
      search: { store_uid: device.storeId }
    })
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto px-4 py-8'>
          <div className='mb-2 flex items-center justify-between'>
            <div className='flex items-center gap-4'>
              <h2 className='text-xl font-semibold'>Danh sách thiết bị</h2>
              <Input
                placeholder='Tìm kiếm thiết bị...'
                className='w-64'
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                onKeyDown={e => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    setSearchTerm(searchQuery)
                    setCurrentPage(1) // Reset to page 1 when searching
                  }
                }}
              />
              <Select
                value={selectedStore}
                onValueChange={value => {
                  setSelectedStore(value)
                  setCurrentPage(1) // Reset to page 1 when changing store
                }}
              >
                <SelectTrigger className='w-48'>
                  <SelectValue placeholder='Tất cả các điểm' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>Tất cả các điểm</SelectItem>
                  {storesData
                    ?.filter(store => store.isActive)
                    ?.map(store => (
                      <SelectItem key={store.id} value={store.id}>
                        {store.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <Select
                value={selectedDeviceType}
                onValueChange={value => {
                  setSelectedDeviceType(value)
                  setCurrentPage(1) // Reset to page 1 when changing device type
                }}
              >
                <SelectTrigger className='w-52'>
                  <SelectValue placeholder='Tất cả loại thiết bị' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>Tất cả loại thiết bị</SelectItem>
                  <SelectItem value='POS'>POS</SelectItem>
                  <SelectItem value='POS_MINI'>POS MINI</SelectItem>
                  <SelectItem value='PDA'>PDA</SelectItem>
                  <SelectItem value='KDS'>KDS</SelectItem>
                  <SelectItem value='KDS_ORDER_CONTROL'>KDS ORDER CONTROL</SelectItem>
                  <SelectItem value='KDS_MAKER'>KDS MAKER</SelectItem>
                  <SelectItem value='SELF_ORDER'>SELF ORDER</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button size='sm' onClick={handleCreateDevice}>
              Tạo thiết bị
            </Button>
          </div>

          {error && (
            <div className='py-8 text-center'>
              <p className='text-red-600'>{getErrorMessage(error)}</p>
            </div>
          )}
          {!error && isLoading && (
            <div className='py-8 text-center'>
              <p>Đang tải dữ liệu thiết bị...</p>
            </div>
          )}
          {!error && !isLoading && searchTerm && searchTerm.length > 0 && searchTerm.length < 3 && (
            <div className='py-8 text-center'>
              <p className='text-amber-600'>
                Truyền thiếu dữ liệu : "search" length must be at least 3 characters long
              </p>
            </div>
          )}
          {!error && !isLoading && (!searchTerm || searchTerm.length === 0 || searchTerm.length >= 3) && (
            <DeviceDataTable
              columns={deviceColumns}
              data={devices || []}
              onCopyDevice={handleCopyDevice}
              onRowClick={handleRowClick}
            />
          )}

          <DeviceCopyModal
            device={selectedDevice}
            open={copyModalOpen}
            onOpenChange={setCopyModalOpen}
            onCopy={handleCopyConfirm}
          />
        </div>
      </Main>
    </>
  )
}
