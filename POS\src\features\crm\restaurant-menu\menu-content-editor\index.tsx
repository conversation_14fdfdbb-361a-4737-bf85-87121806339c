import { getErrorMessage } from '@/utils/error-utils'

import { DataTable, createMenuItemColumns, createComboColumns } from '@/components/ui/data-table'

import { ComboEditDialog } from '@/features/crm/restaurant-menu/menu-content-editor/components/dialog/combo-edit-dialog'

import { MenuItemEditDialog } from './components/dialog'
import { MenuFilters } from './components/menu-filters'
import { useMenuContentEditor } from './hooks/use-menu-content-editor'

const DEFAULT_POS_PARENT = 'BRAND-953H'
const PAGE_SIZE = 20
const LOADING_MESSAGE = 'Đang tải...'
const EMPTY_MESSAGE = 'Không có dữ liệu'
const LOADING_DATA_MESSAGE = 'Đang tải dữ liệu thực đơn...'

const renderContent = (error: any, isLoading: boolean, items: any[], filters: any, handleEditItem: any) => {
  if (error) {
    return (
      <div className='py-8 text-center'>
        <p className='text-red-600'>{getErrorMessage(error)}</p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className='py-8 text-center'>
        <p>{LOADING_DATA_MESSAGE}</p>
      </div>
    )
  }

  const columns =
    filters.activeTab === 'menu' ? createMenuItemColumns(handleEditItem) : createComboColumns(handleEditItem)

  return (
    <DataTable
      data={items}
      columns={columns}
      isLoading={isLoading}
      pageSize={PAGE_SIZE}
      emptyMessage={EMPTY_MESSAGE}
      loadingMessage={LOADING_MESSAGE}
    />
  )
}

const renderEditDialog = (
  filters: any,
  selectedItem: any,
  isEditDialogOpen: boolean,
  handleCloseEditDialog: any,
  handleSaveItem: any,
  handleSaveCombo: any,
  isUpdating: boolean,
  selectedComboData: any
) => {
  const isMenuTab = filters.activeTab === 'menu'

  if (isMenuTab) {
    return (
      <MenuItemEditDialog
        item={selectedItem}
        open={isEditDialogOpen}
        onOpenChange={handleCloseEditDialog}
        onSave={handleSaveItem}
        isLoading={isUpdating}
      />
    )
  }

  const comboType = filters.activeTab === 'combo' ? 'combo' : 'special-combo'

  return (
    <ComboEditDialog
      item={selectedItem}
      open={isEditDialogOpen}
      onOpenChange={handleCloseEditDialog}
      onSave={handleSaveCombo}
      isLoading={isUpdating}
      comboType={comboType}
      comboData={selectedComboData}
    />
  )
}

export default function MenuContentEditor() {
  const {
    items,
    isLoading,
    isUpdating,
    error,
    filters,
    updateFilter,
    selectedItem,
    selectedComboData,
    isEditDialogOpen,
    handleEditItem,
    handleSaveItem,
    handleSaveCombo,
    handleCloseEditDialog
  } = useMenuContentEditor(DEFAULT_POS_PARENT)

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-4 flex items-center justify-between'>
        <h1 className='text-xl font-semibold'>Biên tập nội dung thực đơn</h1>
      </div>

      <MenuFilters filters={filters} onFilterChange={updateFilter} />

      {renderContent(error, isLoading, items, filters, handleEditItem)}

      {renderEditDialog(
        filters,
        selectedItem,
        isEditDialogOpen,
        handleCloseEditDialog,
        handleSaveItem,
        handleSaveCombo,
        isUpdating,
        selectedComboData
      )}
    </div>
  )
}
