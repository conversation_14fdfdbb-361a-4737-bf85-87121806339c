import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { menuScheduleApi, type CreateMenuSchedulePayload } from '@/lib/menu-schedule-api'

export const useCreateMenuSchedule = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateMenuSchedulePayload[]) => menuScheduleApi.createMenuSchedule(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menu-schedule'] })
      queryClient.invalidateQueries({ queryKey: ['menu-schedules'] })
      queryClient.invalidateQueries({ queryKey: ['item-schedule'] })
      toast.success('Lịch trình menu đã được tạo thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi tạo lịch trình')
    }
  })
}
