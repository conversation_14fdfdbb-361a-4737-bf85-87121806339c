# Global State Management

This document explains how to access and manage global state in the POS application, including current user, company, and brand information.

## Overview

The application uses a unified state management system built with Zustand that handles all POS-related data including authentication, user information, company details, brands, and stores.

## Quick Reference

```typescript
// Get current user
import { useCurrentUser } from '@/stores/posStore'
const { user, isAuthenticated, userRole, company } = useCurrentUser()

// Get current brand
import { useCurrentBrand } from '@/stores/posStore'
const { selectedBrand, setSelectedBrand, brands } = useCurrentBrand()

// Get current company
import { useCurrentCompany } from '@/stores/posStore'
const { company, companyUid, companyName } = useCurrentCompany()

// Get stores data
import { usePosStores } from '@/stores/posStore'
const { currentBrandStores, currentBrandApiStores } = usePosStores()
```

## Detailed Usage

### 1. Current User Information

```typescript
import { useCurrentUser } from '@/stores/posStore'

function MyComponent() {
  const { 
    user,           // User object with id, email, full_name, etc.
    isAuthenticated, // Boolean - true if user is logged in
    userRole,       // User role object with permissions
    userPermissions, // User permissions object
    company         // Company object (same as useCurrentCompany)
  } = useCurrentUser()

  if (!isAuthenticated) {
    return <div>Please log in</div>
  }

  return (
    <div>
      <h1>Welcome, {user.full_name}</h1>
      <p>Email: {user.email}</p>
      <p>Role: {userRole?.role_name}</p>
    </div>
  )
}
```

### 2. Current Company Information

```typescript
import { useCurrentCompany } from '@/stores/posStore'

function CompanyInfo() {
  const { 
    company,      // Full company object
    companyUid,   // Company ID (string)
    companyName,  // Company name (string)
    userId        // Current user ID (string)
  } = useCurrentCompany()

  return (
    <div>
      <h2>{companyName}</h2>
      <p>Company ID: {companyUid}</p>
    </div>
  )
}
```

### 3. Current Brand Information

```typescript
import { useCurrentBrand } from '@/stores/posStore'

function BrandSelector() {
  const { 
    selectedBrand,    // Currently selected brand object
    setSelectedBrand, // Function to change selected brand
    brands,          // Array of all available brands
    activeBrands,    // Array of active brands only
    isLoading        // Boolean - true during brand switching
  } = useCurrentBrand()

  const handleBrandChange = (brand) => {
    setSelectedBrand(brand)
  }

  return (
    <div>
      <h3>Current Brand: {selectedBrand?.name}</h3>
      <select onChange={(e) => {
        const brand = brands.find(b => b.id === e.target.value)
        if (brand) handleBrandChange(brand)
      }}>
        {brands.map(brand => (
          <option key={brand.id} value={brand.id}>
            {brand.name}
          </option>
        ))}
      </select>
    </div>
  )
}
```

### 4. Stores Information

```typescript
import { usePosStores } from '@/stores/posStore'

function StoresList() {
  const { 
    stores,                  // All stores from auth data
    apiStores,              // Stores fetched from API
    selectedBrand,          // Currently selected brand
    currentBrandStores,     // Stores for current brand (from auth)
    currentBrandApiStores,  // API stores for current brand
    setApiStores,          // Function to update API stores
    getStoresByBrand,      // Function to get stores by brand ID
    getApiStoresByBrand    // Function to get API stores by brand ID
  } = usePosStores()

  return (
    <div>
      <h3>Stores for {selectedBrand?.name}</h3>
      <p>Total stores: {currentBrandApiStores.length}</p>
      {currentBrandApiStores.map(store => (
        <div key={store.id}>
          <h4>{store.store_name}</h4>
          <p>{store.address}</p>
        </div>
      ))}
    </div>
  )
}
```

## Data Structure Reference

### User Object
```typescript
interface User {
  id: string
  email: string
  full_name: string
  phone: string
  role_uid: string
  company_uid: string
  active: number
  // ... other properties
}
```

### Company Object
```typescript
interface Company {
  id: string
  company_id: string
  company_name: string
  description: string | null
  active: number
  // ... other properties
}
```

### Brand Object (for UI)
```typescript
interface Brand {
  id: string
  name: string
  logo: React.ElementType
  plan: string
  brandId: string
  currency: string
  active: boolean
}
```

### Raw Brand Object (from API)
```typescript
interface RawBrand {
  id: string
  brand_name: string
  brand_id: string
  currency: string
  active: number
  // ... other properties
}
```

## Best Practices

### 1. Always Check Authentication
```typescript
const { isAuthenticated } = useCurrentUser()

if (!isAuthenticated) {
  // Handle unauthenticated state
  return <LoginForm />
}
```

### 2. Handle Loading States
```typescript
const { selectedBrand, isLoading } = useCurrentBrand()

if (isLoading) {
  return <LoadingSpinner />
}
```

### 3. Use Proper Error Handling
```typescript
const { user, isAuthenticated } = useCurrentUser()

if (!isAuthenticated || !user) {
  return <ErrorMessage>User not found</ErrorMessage>
}
```

### 4. Brand-Dependent Data
```typescript
// Always check if brand is selected before using brand-dependent data
const { selectedBrand } = useCurrentBrand()
const { currentBrandStores } = usePosStores()

if (!selectedBrand) {
  return <div>Please select a brand</div>
}

// Now safe to use brand-dependent data
return <StoresList stores={currentBrandStores} />
```

## Automatic Features

### Auto-Brand Selection
- When user logs in, the first available brand is automatically selected
- If no brand is selected on app startup, the first brand is auto-selected
- Brand selection persists across page refreshes

### Data Persistence
- User authentication state persists in localStorage
- Selected brand persists across sessions
- All POS data is automatically cached

### Event-Driven Updates
- Brand changes trigger automatic store data refetching
- Authentication changes clear all related data
- Cross-component synchronization via custom events

## Migration from Old Hooks

If you're migrating from the old hook system:

```typescript
// OLD (deprecated)
import { useCurrentUser } from '@/hooks/use-auth'
import { useCurrentBrand } from '@/hooks/use-current-brand'

// NEW (recommended)
import { useCurrentUser, useCurrentBrand } from '@/stores/posStore'
```

The new hooks provide the same interface but with better performance and consistency.

## Troubleshooting

### Brand Switcher Not Showing
- Check if user is authenticated: `const { isAuthenticated } = useCurrentUser()`
- Verify brands data exists: `const { brands } = useCurrentBrand()`
- Check console for auto-selection logs

### Data Not Updating
- Ensure you're using the new hooks from `@/stores/posStore`
- Check if brand is selected: `const { selectedBrand } = useCurrentBrand()`
- Verify API calls are using correct company/brand UIDs

### Performance Issues
- Use specific hooks instead of accessing the store directly
- Avoid unnecessary re-renders by destructuring only needed values
- Use React.memo() for components that don't need frequent updates
