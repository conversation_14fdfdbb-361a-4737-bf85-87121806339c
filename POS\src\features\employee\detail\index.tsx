import { useNavigate, useParams } from '@tanstack/react-router'

import type { UpdateUserParams } from '@/types'
import { XIcon } from 'lucide-react'
import { toast } from 'sonner'

import { getErrorMessage } from '@/utils/error-utils'

import { useCreateUser, useUserData, useUpdateUser, useDeactivateUser, useActivateUser } from '@/hooks/api'

import { Button } from '@/components/ui'

import { CreateEmployeeForm } from './components/form'

type CreateEmployeeSubmitData = {
  email: string
  full_name: string
  phone?: string
  role_uid: string
  password?: string
  brand_access?: string[]
}

export default function EmployeeDetailPage() {
  const navigate = useNavigate()

  // Try to get params from the parameterized route, but handle gracefully if not available
  let userId: string | undefined
  try {
    const params = useParams({
      from: '/_authenticated/employee/detail/$userId'
    })
    userId = params?.userId
  } catch {
    // If we're on the non-parameterized route, userId will be undefined
    userId = undefined
  }

  // Determine if this is edit mode or create mode
  const isEditMode = !!userId

  // Hooks
  const createUserMutation = useCreateUser()
  const updateUserMutation = useUpdateUser()
  const deactivateUserMutation = useDeactivateUser()
  const activateUserMutation = useActivateUser()

  // Fetch user data if in edit mode
  const { data: userData, isLoading: userLoading, error: userError } = useUserData(userId || '', isEditMode)

  const handleSubmitEmployee = async (data: CreateEmployeeSubmitData) => {
    try {
      if (isEditMode && userId) {
        // Update existing user
        const updateParams: UpdateUserParams = { ...data }
        if (data.password) {
          updateParams.confirm_password = data.password
        }
        await updateUserMutation.mutateAsync({
          userId,
          params: updateParams,
          userData: userData
        })
        toast.success('Cập nhật nhân viên thành công!')
      } else {
        // Create new user - password is required
        if (!data.password) {
          toast.error('Mật khẩu là bắt buộc khi tạo nhân viên mới')
          return
        }
        await createUserMutation.mutateAsync({
          ...data,
          password: data.password,
          confirm_password: data.password
        })
        toast.success('Tạo nhân viên thành công!')
      }
      navigate({ to: '/employee/list' })
    } catch (error) {
      toast.error(getErrorMessage(error))
    }
  }

  const handleDeactivateEmployee = async () => {
    if (!userId) return

    try {
      await deactivateUserMutation.mutateAsync(userId)
      navigate({ to: '/employee/list' })
    } catch (error) {
      toast.error(getErrorMessage(error))
    }
  }

  const handleActivateEmployee = async () => {
    if (!userId) return

    try {
      await activateUserMutation.mutateAsync(userId)
      navigate({ to: '/employee/list' })
    } catch (error) {
      toast.error(getErrorMessage(error))
    }
  }

  const handleCancel = () => {
    navigate({ to: '/employee/list' })
  }

  // Show loading state while fetching user data in edit mode
  if (isEditMode && userLoading) {
    return (
      <div className='min-h-screen bg-white'>
        <div className='bg-white px-4 py-8'>
          <div className='flex items-center justify-between'>
            <Button variant='ghost' size='icon' onClick={handleCancel} className='h-8 w-8'>
              <XIcon className='h-4 w-4' />
            </Button>
            <h1 className='mb-2 text-3xl font-medium'>Chi tiết nhân viên</h1>
            <div></div>
          </div>
        </div>
        <div className='px-6 py-6'>
          <div className='text-center'>Đang tải thông tin nhân viên...</div>
        </div>
      </div>
    )
  }

  // Show error state if user data failed to load
  if (isEditMode && userError) {
    return (
      <div className='min-h-screen bg-white'>
        <div className='bg-white px-4 py-8'>
          <div className='flex items-center justify-between'>
            <Button variant='ghost' size='icon' onClick={handleCancel} className='h-8 w-8'>
              <XIcon className='h-4 w-4' />
            </Button>
            <h1 className='mb-2 text-3xl font-medium'>Chi tiết nhân viên</h1>
            <div></div>
          </div>
        </div>
        <div className='px-6 py-6'>
          <div className='text-center text-red-600'>{getErrorMessage(userError)}</div>
        </div>
      </div>
    )
  }

  const isLoading = isEditMode
    ? updateUserMutation.isPending || deactivateUserMutation.isPending || activateUserMutation.isPending
    : createUserMutation.isPending

  return (
    <div className='min-h-screen bg-white'>
      <div className='bg-white px-4 py-8'>
        <div className='flex items-center justify-between'>
          <Button variant='ghost' size='icon' onClick={handleCancel} className='h-8 w-8'>
            <XIcon className='h-4 w-4' />
          </Button>
          <h1 className='mb-2 text-3xl font-medium'>{isEditMode ? 'Chi tiết nhân viên' : 'Tạo nhân viên mới'}</h1>
          <div className='flex items-center gap-3'>
            {isEditMode && userData && (
              <>
                {userData.active === 1 ? (
                  <Button
                    type='button'
                    variant='outline'
                    className='border-red-500 text-red-500 hover:bg-red-50'
                    onClick={handleDeactivateEmployee}
                    disabled={isLoading}
                  >
                    {deactivateUserMutation.isPending ? 'Active' : 'Deactive'}
                  </Button>
                ) : (
                  <Button
                    type='button'
                    variant='outline'
                    className='border-red-500 text-red-500 hover:bg-red-50'
                    onClick={handleActivateEmployee}
                    disabled={isLoading}
                  >
                    {activateUserMutation.isPending ? 'Active' : 'Active'}
                  </Button>
                )}
              </>
            )}
            <Button type='button' onClick={() => {}}>
              Lưu và đồng bộ
            </Button>
            <Button type='submit' form='employee-form' disabled={isLoading}>
              {isLoading ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </div>

      <div className='px-6 py-6'>
        <CreateEmployeeForm
          key={isEditMode ? userData?.id || 'edit' : 'create'}
          onSubmit={handleSubmitEmployee}
          onCancel={handleCancel}
          isLoading={isLoading}
          showSaveButton={false}
          initialData={isEditMode ? userData : undefined}
          isEditMode={isEditMode}
        />
      </div>
    </div>
  )
}
