import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import type { OrderSource } from '@/types/api/order-sources'
import { MoreHorizontal, Trash2 } from 'lucide-react'

import { useDeleteOrderSource } from '@/hooks/api/use-order-sources'

import { ConfirmModal } from '@/components/pos'
import { TableCell, TableRow as UITableRow, Button } from '@/components/ui'

import { OrderSourceStoresModal } from '../order-source-stores-modal'

interface TableRowProps {
  source: OrderSource
  index: number
}

export function TableRow({ source, index }: TableRowProps) {
  const navigate = useNavigate()
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [storesModalOpen, setStoresModalOpen] = useState(false)
  const { deleteOrderSource, isDeleting } = useDeleteOrderSource()

  const handleDelete = () => {
    deleteOrderSource(
      { id: source.id },
      {
        onSuccess: () => {
          setShowDeleteModal(false)
        }
      }
    )
  }

  const formatStoreCount = (storeCount: number | null) => {
    if (storeCount === null || storeCount === 0) {
      return '0 cửa hàng'
    }
    return `${storeCount} cửa hàng`
  }

  const handleStoresClick = (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent row click when clicking stores
    setStoresModalOpen(true)
  }

  const handleRowClick = () => {
    navigate({ to: `/setting/source/detail/${source.id}` })
  }

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent row click when clicking delete
    setShowDeleteModal(true)
  }

  return (
    <>
      <UITableRow className='hover:bg-muted/50 cursor-pointer' onClick={handleRowClick}>
        <TableCell className='text-center font-medium'>{index}</TableCell>

        <TableCell className='font-medium'>{source.source_id}</TableCell>

        <TableCell>{source.source_name}</TableCell>

        <TableCell>
          <div className='flex items-center gap-2'>
            <button
              className='text-muted-foreground hover:text-primary cursor-pointer text-sm underline-offset-4 hover:underline'
              onClick={handleStoresClick}
              disabled={!source.stores || source.stores === 0}
            >
              {formatStoreCount(source.stores)}
            </button>
            {source.stores && source.stores > 0 && (
              <Button variant='ghost' size='sm' className='h-6 w-6 p-0' onClick={handleStoresClick}>
                <MoreHorizontal className='text-muted-foreground h-4 w-4' />
              </Button>
            )}
          </div>
        </TableCell>

        <TableCell>
          <Button
            variant='ghost'
            size='sm'
            className='text-muted-foreground hover:text-destructive h-8 w-8 p-0'
            onClick={handleDeleteClick}
          >
            <Trash2 className='h-4 w-4' />
          </Button>
        </TableCell>
      </UITableRow>

      <ConfirmModal
        open={showDeleteModal}
        onOpenChange={setShowDeleteModal}
        title='Xác nhận xóa'
        content={`Bạn có chắc chắn muốn xóa nguồn đơn hàng "${source.source_name}"?`}
        confirmText='Xóa'
        cancelText='Hủy'
        onConfirm={handleDelete}
        isLoading={isDeleting}
      />

      {/* Stores Modal */}
      <OrderSourceStoresModal
        open={storesModalOpen}
        onOpenChange={setStoresModalOpen}
        orderSourceId={source.id}
        orderSourceName={source.source_name}
      />
    </>
  )
}
