# Sửa lỗi Logic Mapping Thời Gian trong Tạo Combo

## Vấn đề
Trong tính năng tạo combo tại `/sale/combo`, logic mapping cho "Khung thời gian bán" bị sai:
- **<PERSON><PERSON><PERSON> (time_sale_date_week)**: Logic mapping không đúng với chuẩn hệ thống
- **Giờ (time_sale_hour_day)**: Logic mapping cũng có vấn đề tương tự

## Nguyên nhân
File `POS/src/features/sale/combo/components/create-combo-dialog.tsx` sử dụng logic mapping sai:
- **Logic cũ (SAI)**: Sử dụng bit position trực tiếp `(1 << index)`
- **Logic đúng**: <PERSON><PERSON>i sử dụng mapping theo chuẩn hệ thống như trong `time-frame-config-modal.tsx`

## Chuẩn Mapping Đúng
Theo file tham khảo `POS/src/features/menu/items/items-in-store/list/components/modals/time-frame-config-modal.tsx`:

### Ngày (time_sale_date_week)
```
Thứ 2  = bit 4   (4)
Thứ 3  = bit 8   (8) 
Thứ 4  = bit 16  (16)
Thứ 5  = bit 32  (32)
Thứ 6  = bit 64  (64)
Thứ 7  = bit 128 (128)
Chủ nhật = bit 2  (2)
```

### Giờ (time_sale_hour_day)
```
0h = bit 0, 1h = bit 1, ..., 23h = bit 23
```

## Thay đổi đã thực hiện

### 1. Sửa function `convertBitmaskToArray`
**Trước:**
```javascript
const convertBitmaskToArray = (bitmask: number, length: number): boolean[] => {
  const result = new Array(length).fill(false)
  for (let i = 0; i < length; i++) {
    result[i] = (bitmask & (1 << i)) !== 0  // SAI!
  }
  return result
}
```

**Sau:**
```javascript
const convertBitmaskToArray = (bitmask: number, length: number): boolean[] => {
  const result = new Array(length).fill(false)
  // Mapping theo đúng bit flags như trong time-frame-config-modal.tsx
  const dayBitMapping = [4, 8, 16, 32, 64, 128, 2] // [T2, T3, T4, T5, T6, T7, CN]
  
  for (let i = 0; i < length && i < dayBitMapping.length; i++) {
    result[i] = (bitmask & dayBitMapping[i]) !== 0  // ĐÚNG!
  }
  return result
}
```

### 2. Sửa logic convert khi lưu dữ liệu
**Trước:**
```javascript
const timeSaleDateWeek = data.selectedDays.reduce((acc, isSelected, index) => {
  return isSelected ? acc | (1 << index) : acc  // SAI!
}, 0)
```

**Sau:**
```javascript
// Mapping ngày theo đúng bit flags như trong time-frame-config-modal.tsx
const dayBitMapping = [4, 8, 16, 32, 64, 128, 2] // [T2, T3, T4, T5, T6, T7, CN]
const timeSaleDateWeek = data.selectedDays.reduce((acc, isSelected, index) => {
  return isSelected && index < dayBitMapping.length ? acc | dayBitMapping[index] : acc  // ĐÚNG!
}, 0)
```

## Kết quả
- ✅ Logic mapping ngày đã đúng theo chuẩn hệ thống
- ✅ Logic mapping giờ vẫn giữ nguyên (đã đúng từ trước)
- ✅ Tương thích với các module khác trong hệ thống
- ✅ Đã test và verify logic hoạt động chính xác

## Files đã sửa
- `POS/src/features/sale/combo/components/create-combo-dialog.tsx`

## Files không cần sửa (đã đúng)
- `POS/src/utils/date-utils.ts` - Sử dụng logic đúng
- `POS/src/features/sale/combo/components/price-source-config-dialog.tsx` - Sử dụng dateUtils
- `POS/src/features/sale/combo/components/combo-form/hooks/use-combo-form.ts` - Sử dụng dateUtils
- `POS/src/features/sale/combo/components/export-combo-dialog.tsx` - Đã có mapping đúng

## Lưu ý
Logic mapping này phải nhất quán trong toàn bộ hệ thống. Các module khác đã sử dụng `dateUtils` từ `date-utils.ts` nên không bị ảnh hưởng.
