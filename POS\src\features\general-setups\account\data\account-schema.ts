import { z } from 'zod'

export const createUserSchema = z.object({
  username: z
    .string()
    .min(1, 'Tên người dùng là bắt buộc')
    .min(3, 'Tên người dùng phải có ít nhất 3 ký tự'),
  email: z
    .string()
    .min(1, '<PERSON><PERSON> là bắt buộc')
    .email('<PERSON><PERSON> không hợp lệ'),
  password: z
    .string()
    .min(1, 'Mật khẩu là bắt buộc')
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
  permissions: z
    .array(z.string())
    .min(1, '<PERSON><PERSON>i chọn ít nhất một quyền')
})

export const updateUserSchema = z.object({
  username: z
    .string()
    .min(3, 'Tên người dùng phải có ít nhất 3 ký tự')
    .optional(),
  email: z
    .string()
    .email('<PERSON><PERSON> không hợp lệ')
    .optional(),
  password: z
    .string()
    .min(6, '<PERSON><PERSON><PERSON> khẩu phải có ít nhất 6 ký tự')
    .optional(),
  permissions: z
    .array(z.string())
    .min(1, 'Phải chọn ít nhất một quyền')
    .optional(),
  status: z
    .enum(['active', 'inactive'])
    .optional()
})

export type CreateUserFormData = z.infer<typeof createUserSchema>
export type UpdateUserFormData = z.infer<typeof updateUserSchema>
