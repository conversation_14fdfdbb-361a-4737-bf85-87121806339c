import { formatCurrency } from '@/lib/utils'

import { Badge, Card, CardContent, CardHeader, CardTitle } from '@/components/ui'

import { useDashboardContext } from '../context'

export function StatisticsCards() {
  const { overviewData, isOverviewLoading, overviewError, dateRange } = useDashboardContext()

  const averagePerBill = overviewData?.total_sales ? overviewData.revenue_net / overviewData.total_sales : 0

  const averagePerCustomer = overviewData?.peo_count ? overviewData.revenue_net / overviewData.peo_count : 0

  const getPreviousPeriodText = () => {
    if (!overviewData?.previous_period) return ''

    const fromDate = new Date(dateRange.from)
    const toDate = new Date(dateRange.to)

    const isSameDay = fromDate.toDateString() === toDate.toDateString()

    if (isSameDay) {
      const prevDay = new Date(overviewData.previous_period.start_date)
      return `so với <PERSON> ${prevDay.getDate().toString().padStart(2, '0')}/${(prevDay.getMonth() + 1).toString().padStart(2, '0')}`
    } else {
      const prevStartDate = new Date(overviewData.previous_period.start_date)
      const prevEndDate = new Date(overviewData.previous_period.end_date)

      return `so với Từ ngày ${prevStartDate.getDate().toString().padStart(2, '0')}/${(prevStartDate.getMonth() + 1).toString().padStart(2, '0')} đến ngày ${prevEndDate.getDate().toString().padStart(2, '0')}/${(prevEndDate.getMonth() + 1).toString().padStart(2, '0')}`
    }
  }

  const formatPercentageChange = (percentage: number) => {
    const isPositive = percentage > 0
    const isNegative = percentage < 0

    return {
      isPositive,
      isNegative,
      text: `${isPositive ? 'Tăng' : 'Giảm'} ${Math.abs(percentage).toFixed(2)}%`,
      icon: isPositive ? '↑' : '↓',
      variant: (isPositive ? 'secondary' : 'destructive') as 'secondary' | 'destructive'
    }
  }

  const percentageChange = overviewData?.previous_period?.percentage
    ? formatPercentageChange(overviewData.previous_period.percentage)
    : null

  if (isOverviewLoading) {
    return (
      <div className='space-y-8'>
        <div className='animate-pulse'>
          <div className='h-32 rounded-lg bg-gray-200'></div>
        </div>
        <div className='grid grid-cols-1 gap-2 md:grid-cols-3'>
          {[1, 2, 3].map(i => (
            <div key={i} className='animate-pulse'>
              <div className='h-24 rounded-lg bg-gray-200'></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (overviewError) {
    return (
      <div className='py-8 text-center'>
        <p className='text-red-500'>Lỗi tải dữ liệu: {overviewError}</p>
      </div>
    )
  }
  return (
    <>
      {/* ===== Revenue ===== */}
      <div className='mb-8'>
        <Card className='gap-0'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-lg font-semibold'>Doanh thu (NET)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center gap-3'>
              <div className='text-3xl font-bold'>{formatCurrency(overviewData?.revenue_net || 0)}</div>
              {percentageChange && (
                <div className='flex items-center gap-2'>
                  <Badge
                    variant={percentageChange.variant}
                    className={`flex items-center gap-1 ${
                      percentageChange.isPositive
                        ? 'border-green-200 bg-green-100 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400'
                        : ''
                    }`}
                  >
                    <span>{percentageChange.icon}</span>
                    <span>{percentageChange.text}</span>
                  </Badge>
                  <span className='text-muted-foreground text-sm'>{getPreviousPeriodText()}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className='mb-8 grid grid-cols-1 gap-2 md:grid-cols-3'>
        <Card className='gap-2'>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Giảm giá và chi phí</CardTitle>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth='2'
              className='text-muted-foreground h-4 w-4'
            >
              <path d='M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6' />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{formatCurrency(overviewData?.discount_amount || 0)}</div>
          </CardContent>
        </Card>
        <Card className='gap-2'>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Số hóa đơn</CardTitle>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth='2'
              className='text-muted-foreground h-4 w-4'
            >
              <rect width='20' height='14' x='2' y='5' rx='2' />
              <path d='M2 10h20' />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{overviewData?.total_sales || 0}</div>
            <p className='text-muted-foreground text-xs'>Trung bình {formatCurrency(averagePerBill)} / hoá đơn</p>
          </CardContent>
        </Card>
        <Card className='gap-2'>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Số khách</CardTitle>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth='2'
              className='text-muted-foreground h-4 w-4'
            >
              <path d='M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2' />
              <circle cx='9' cy='7' r='4' />
              <path d='M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75' />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{overviewData?.peo_count || 0}</div>
            <p className='text-muted-foreground text-xs'>Trung bình {formatCurrency(averagePerCustomer)} / khách</p>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
