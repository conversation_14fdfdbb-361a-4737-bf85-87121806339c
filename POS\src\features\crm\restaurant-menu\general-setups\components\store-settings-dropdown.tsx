import { Settings, Store, Menu, Clock, DollarSign } from 'lucide-react'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Button
} from '@/components/ui'

import type { Store as StoreType } from '../types'

interface StoreSettingsDropdownProps {
  store: StoreType
  onStoreInfoClick?: (store: StoreType) => void
  onMenuClick?: (store: StoreType) => void
  onDeliveryTimeClick?: (store: StoreType) => void
  onShippingFeeClick?: (store: StoreType) => void
}

export function StoreSettingsDropdown({
  store,
  onStoreInfoClick,
  onMenuClick,
  onDeliveryTimeClick,
  onShippingFeeClick
}: StoreSettingsDropdownProps) {
  const handleStoreInfoClick = () => {
    onStoreInfoClick?.(store)
  }

  const handleMenuClick = () => {
    onMenuClick?.(store)
  }

  const handleDeliveryTimeClick = () => {
    onDeliveryTimeClick?.(store)
  }

  const handleShippingFeeClick = () => {
    onShippingFeeClick?.(store)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' size='sm' className='h-8 px-3 text-xs'>
          <Settings className='h-3 w-3 mr-1' />
          Cài đặt
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-56'>
        <DropdownMenuItem onClick={handleStoreInfoClick}>
          <Store className='mr-2 h-4 w-4' />
          Thông tin cửa hàng
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleMenuClick}>
          <Menu className='mr-2 h-4 w-4' />
          Thực đơn
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDeliveryTimeClick}>
          <Clock className='mr-2 h-4 w-4' />
          Thời gian bán giao hàng
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleShippingFeeClick}>
          <DollarSign className='mr-2 h-4 w-4' />
          Phí vận chuyển
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
