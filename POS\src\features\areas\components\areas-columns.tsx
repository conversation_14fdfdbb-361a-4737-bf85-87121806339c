import { useState } from 'react'

import { ColumnDef } from '@tanstack/react-table'

import { Plus } from 'lucide-react'

import type { Area } from '@/lib/api/pos/areas-api'

import { useToggleAreaStatus } from '@/hooks/api/use-areas'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'

import { StatusBadge } from '@/components/pos'

import { AddTablesModal } from './add-tables-modal'

// Helper function to get store name from localStorage
function getStoreName(storeUid: string): string {
  try {
    const posStoresData = localStorage.getItem('pos_stores_data')
    if (posStoresData) {
      const stores = JSON.parse(posStoresData)
      const store = stores.find((s: any) => s.id === storeUid)
      return store?.store_name || 'Không xác định'
    }
    return 'Không xác định'
  } catch (error) {
    console.error('Error parsing pos_stores_data:', error)
    return 'Không xác định'
  }
}

function ActionsCell({ area }: { area: Area }) {
  const { toggleAreaStatus, isToggling } = useToggleAreaStatus()

  const handleToggleStatus = () => {
    console.log('Toggling area status:', area.area_name, 'from', area.active === 1 ? 'Active' : 'Inactive')
    toggleAreaStatus(area)
  }

  return (
    <div className='flex items-center justify-center gap-2'>
      <button
        onClick={handleToggleStatus}
        disabled={isToggling}
        className='cursor-pointer disabled:cursor-not-allowed disabled:opacity-50'
      >
        <StatusBadge isActive={area.active === 1} activeText='Active' inactiveText='Deactive' />
      </button>
    </div>
  )
}

function AddTablesCell({ area, storeUid }: { area: Area; storeUid: string }) {
  const [modalOpen, setModalOpen] = useState(false)

  const handleAddTables = () => {
    console.log('handleAddTables clicked for area:', area.area_name)
    setModalOpen(true)
  }

  const formatTableCount = (tableIds: string[]) => {
    const count = tableIds?.length || 0
    return `${count} bàn trong khu vực`
  }

  return (
    <>
      <div className='flex items-center justify-center gap-2'>
        <button
          className='cursor-pointer text-sm text-blue-600 hover:text-blue-800 hover:underline'
          onClick={handleAddTables}
        >
          {formatTableCount(area.list_table_id)}
        </button>
        <Button variant='ghost' size='sm' className='h-6 w-6 p-0' onClick={handleAddTables}>
          <Plus className='h-4 w-4' />
        </Button>
      </div>

      <AddTablesModal open={modalOpen} onOpenChange={setModalOpen} area={area} storeUid={storeUid} />
    </>
  )
}

export const createAreasColumns = (storeUid: string): ColumnDef<Area>[] => [
  {
    id: 'select',
    header: ({ table }) => (
      <div className='flex justify-center'>
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
          onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
          aria-label='Select all'
          className='translate-y-[2px]'
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className='flex justify-center'>
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={value => row.toggleSelected(!!value)}
          aria-label='Select row'
          className='translate-y-[2px]'
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    id: 'index',
    header: () => <div className='text-center'>#</div>,
    cell: ({ row }) => <div className='w-[50px] text-center font-medium'>{row.index + 1}</div>,
    enableSorting: false,
    enableHiding: false,
    size: 60
  },
  {
    accessorKey: 'area_name',
    header: () => <div className='text-center'>Tên khu vực</div>,
    cell: ({ row }) => (
      <div className='text-center'>
        <span className='font-medium'>{row.getValue('area_name')}</span>
      </div>
    ),
    size: 200
  },
  {
    accessorKey: 'area_id',
    header: () => <div className='text-center'>Mã khu vực</div>,
    cell: ({ row }) => (
      <div className='text-center'>
        <span className='font-mono text-sm'>{row.getValue('area_id')}</span>
      </div>
    ),
    size: 150
  },
  {
    id: 'applied_stores',
    header: () => <div className='text-center'>Điểm áp dụng</div>,
    cell: ({ row }) => {
      const area = row.original
      const storeName = getStoreName(area.store_uid || '')
      return (
        <div className='text-center'>
          <span className='text-sm'>{storeName}</span>
        </div>
      )
    },
    enableSorting: false,
    size: 120
  },
  {
    id: 'add_tables',
    header: () => <div className='text-center'>Thêm bàn</div>,
    cell: ({ row }) => <AddTablesCell area={row.original} storeUid={storeUid} />,
    enableSorting: false,
    size: 120
  },
  {
    id: 'actions',
    header: () => <div className='text-center'>Thao tác</div>,
    cell: ({ row }) => <ActionsCell area={row.original} />,
    enableSorting: false,
    size: 100
  }
]
