import { useDashboardContext } from '../context/dashboard-context'

export function SourcesTest() {
  const { sourcesData, isSourcesLoading, sourcesError } = useDashboardContext()

  if (isSourcesLoading) {
    return <div>Loading sources...</div>
  }

  if (sourcesError) {
    return <div>Error: {sourcesError}</div>
  }

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Sources Data Test</h3>
      <div className="space-y-2">
        {sourcesData.map((source, index) => (
          <div key={index} className="border p-3 rounded">
            <div className="font-medium">{source.source_name}</div>
            <div className="text-sm text-gray-600">
              ID: {source.source_id} | Bills: {source.total_bill} | Revenue: {source.revenue_gross?.toLocaleString()}
            </div>
          </div>
        ))}
      </div>
      {sourcesData.length === 0 && (
        <div className="text-gray-500">No sources data available</div>
      )}
    </div>
  )
}
