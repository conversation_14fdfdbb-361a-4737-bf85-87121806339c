---
type: "agent_requested"
description: "Example description"
---
# Commit Style Guidelines

## Commit Message Format

All commit messages must follow the Conventional Commits specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Type

Must be one of the following:

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **perf**: A code change that improves performance
- **test**: Adding missing tests or correcting existing tests
- **build**: Changes that affect the build system or external dependencies
- **ci**: Changes to our CI configuration files and scripts
- **chore**: Other changes that don't modify src or test files
- **revert**: Reverts a previous commit

### Scope

The scope should be the name of the npm package affected (as perceived by the person reading the changelog generated from commit messages).

Examples:
- `feat(auth): add OAuth2 integration`
- `fix(api): resolve timeout issues in user endpoints`
- `docs(readme): update installation instructions`

### Description

- Use the imperative, present tense: "change" not "changed" nor "changes"
- Don't capitalize the first letter
- No dot (.) at the end
- Maximum 50 characters

### Body

- Use the imperative, present tense: "change" not "changed" nor "changes"
- Include motivation for the change and contrasts with previous behavior
- Wrap at 72 characters

### Footer

- Reference issues and pull requests
- Include breaking change information

## Examples

### Simple commit
```
feat: add user authentication
```

### Commit with scope
```
fix(api): resolve timeout in user login endpoint
```

### Commit with body
```
feat: add email notifications

Users can now receive email notifications for important events.
This includes order confirmations, shipping updates, and account changes.
```

### Commit with breaking change
```
feat!: remove deprecated API endpoints

BREAKING CHANGE: The /api/v1/users endpoint has been removed.
Use /api/v2/users instead.
```

### Commit with issue reference
```
fix: resolve memory leak in data processing

Closes #123
```

## Rules

1. **Always use lowercase** for type and description
2. **Keep the first line under 50 characters**
3. **Use present tense** ("add" not "added")
4. **Be specific** about what changed
5. **Reference issues** when applicable
6. **Include breaking changes** in footer
7. **No period** at the end of the description
8. **Use imperative mood** ("fix bug" not "fixes bug")

## Anti-patterns

❌ **Bad examples:**
```
Fixed bug
Update README.md
WIP: working on feature
misc changes
.
```

✅ **Good examples:**
```
fix: resolve null pointer exception in user service
docs: update API documentation for v2 endpoints
feat: implement real-time notifications
refactor: extract common validation logic
```

## Enforcement

- All commits must pass the conventional commit format check
- Use `git commit` without `--no-verify` to ensure hooks run
- Pre-commit hooks will validate commit message format
- Invalid commit messages will be rejected
