## Plan: Add remove (X) button for items in CreateGroupModal

### Context

- File: `src/features/menu/customization/customization-in-city/detail/components/create-group-modal.tsx`
- Current behavior: Modal lists `menuItems` in a table with columns Tên, Giá. No per-item removal.
- Goal: Add a cancel icon (X) button per row to remove that item from `menuItems`.

### Changes

1. UI updates in table

   - Add a third column with an empty header (right-aligned).
   - For each `menuItems` row, render a small icon button (X) on the right.
   - Use an icon consistent with project (e.g., `X` from `lucide-react` or existing `IconX`).
   - Button style: subtle/ghost, size `icon`, hover red, with `aria-label` and `title`.

2. Removal behavior

   - Implement `handleRemoveItem(id: string)` inside `CreateGroupModal`.
   - Logic: `groupManagement.setMenuItems(prev => prev.filter(i => i.id !== id))`.
   - Wire the icon button `onClick={() => handleRemoveItem(item.id)}`.

3. Empty-state and validations

   - Keep existing "Thêm món" row visible regardless of list length.
   - No changes to `useGroupManagement` validation; saving still requires at least one item.

### Acceptance Criteria

- A third, empty-header column is visible at the right of the items table.
- Each item row shows an X icon button at the right.
- Clicking the X removes the corresponding item from `menuItems` immediately.
- The "Thêm món" row remains available after removals.
- No runtime errors; group save still enforces at least one item.
