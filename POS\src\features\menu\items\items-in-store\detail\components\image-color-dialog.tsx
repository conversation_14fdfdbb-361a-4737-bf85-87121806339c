import { useState } from 'react'

import { Upload } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'

interface ImageColorDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImageSelect: (file: File) => void
  onColorSelect: (color: string) => void
  onSelfOrderToggle: (enabled: boolean) => void
  selectedColor?: string
  useSelfOrderImage?: boolean
  currentImageUrl?: string
}

const predefinedColors = [
  '#3B82F6',
  '#EF4444',
  '#10B981',
  '#F59E0B',
  '#8B5CF6',
  '#EC4899',
  '#F97316',
  '#84CC16',
  '#06B6D4',
  '#6366F1'
]

const additionalColors = [
  '#FF6B6B',
  '#4ECDC4',
  '#45B7D1',
  '#96CEB4',
  '#FFEAA7',
  '#DDA0DD',
  '#98D8C8',
  '#F7DC6F',
  '#BB8FCE',
  '#85C1E9'
]

export function ImageColorDialog({
  open,
  onOpenChange,
  onImageSelect,
  onColorSelect,
  onSelfOrderToggle,
  selectedColor = '#000000',
  useSelfOrderImage = false,
  currentImageUrl
}: ImageColorDialogProps) {
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  // Set initial imagePreview from currentImageUrl if available
  const initialImagePreview = currentImageUrl || imagePreview

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setSelectedFile(null)
      setImagePreview(null)
    }
    onOpenChange(open)
  }

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      const reader = new FileReader()
      reader.onload = e => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleDone = () => {
    if (selectedFile) {
      onImageSelect(selectedFile)
    } else if (selectedColor && selectedColor !== '#000000') {
      onColorSelect(selectedColor)
    }
    handleOpenChange(false)
  }

  const handleColorClick = (color: string) => {
    onColorSelect(color)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className='max-h-[90vh] w-[95vw] max-w-3xl overflow-y-auto lg:max-w-2xl'>
        <DialogHeader>
          <DialogTitle className='text-lg font-semibold'>Chọn ảnh hoặc màu</DialogTitle>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Image Selection Section */}
          <div className='space-y-4'>
            <div>
              <h3 className='text-base font-medium text-gray-900'>Chọn ảnh</h3>
              <p className='mt-1 text-sm text-gray-600'>
                Bạn có thể chọn ảnh có kích thước 540x785px để sử dụng trên thiết bị Self Order
              </p>
            </div>

            <div className='flex items-center space-x-2'>
              <Checkbox checked={useSelfOrderImage} onCheckedChange={onSelfOrderToggle} />
              <label className='text-sm font-medium'>Sử dụng ảnh cho thiết bị Self Order</label>
            </div>

            <div className='relative'>
              <div
                className='flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-4 transition-colors hover:bg-gray-100'
                style={{
                  width: '467px',
                  height: '420px',
                  maxWidth: '100%',
                  maxHeight: '380px'
                }}
                onClick={() => document.getElementById('dialog-image-upload')?.click()}
              >
                {initialImagePreview ? (
                  <img src={initialImagePreview} alt='Preview' className='h-full w-full rounded-lg object-cover' />
                ) : (
                  <div className='text-center'>
                    <Upload className='mx-auto mb-2 h-8 w-8 text-gray-400' />
                    <span className='text-sm text-gray-500'>Click để chọn ảnh</span>
                  </div>
                )}
              </div>
              <input
                type='file'
                accept='image/*'
                onChange={handleImageChange}
                className='hidden'
                id='dialog-image-upload'
              />
            </div>
          </div>

          {/* Color Selection Section */}
          <div className='space-y-4'>
            <h3 className='text-base font-medium text-gray-900'>Hoặc chọn màu dưới đây</h3>

            <div className='flex items-center space-x-4'>
              <div className='flex space-x-2'>
                {predefinedColors.map(color => (
                  <button
                    key={color}
                    className={`h-8 w-8 rounded-full border-2 transition-all ${
                      selectedColor === color ? 'scale-110 border-gray-900' : 'border-gray-300 hover:border-gray-400'
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorClick(color)}
                  />
                ))}
              </div>
              <Input
                type='text'
                value={selectedColor}
                onChange={e => handleColorClick(e.target.value)}
                className='w-24 text-center'
                placeholder='#000000'
              />
            </div>

            <div>
              <h4 className='mb-2 text-sm font-medium text-gray-700'>Khác</h4>
              <div className='flex space-x-2'>
                {additionalColors.map(color => (
                  <button
                    key={color}
                    className={`h-8 w-8 rounded-full border-2 transition-all ${
                      selectedColor === color ? 'scale-110 border-gray-900' : 'border-gray-300 hover:border-gray-400'
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorClick(color)}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className='flex justify-end space-x-2 pt-4'>
          <Button variant='outline' onClick={() => onOpenChange(false)}>
            Huỷ
          </Button>
          <Button onClick={handleDone}>Xong</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
