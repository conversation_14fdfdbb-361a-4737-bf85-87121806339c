import { useState, useEffect } from 'react'

import { useNavigate, useRouter, useCanGoBack } from '@tanstack/react-router'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { getErrorMessage } from '@/utils/error-utils'

import {
  useDeviceDetail,
  useUpdateDevice,
  useCreateDevice,
  usePrintersData,
  useDeletePrinter,
  useItemTypesData,
  useCombosData,
  useStoresData
} from '@/hooks/api'

import { DEFAULT_FORM_DATA } from '../constants/device-form-constants'
import { DeviceFormData } from '../types'
import { formatDeviceData, buildUpdatePayload, buildCreatePayload } from '../utils/device-form-helpers'

export const useDeviceForm = (id?: string, storeUid?: string) => {
  const navigate = useNavigate()
  const router = useRouter()
  const canGoBack = useCanGoBack()
  const { auth } = useAuthStore()
  const isEditMode = !!id

  const [formData, setFormData] = useState<DeviceFormData>(DEFAULT_FORM_DATA)
  const [allowEditIpServer, setAllowEditIpServer] = useState(false)

  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showPrinterModal, setShowPrinterModal] = useState(false)
  const [showDeletePrinterModal, setShowDeletePrinterModal] = useState(false)
  const [printerToDelete, setPrinterToDelete] = useState<any>(null)
  const [printerToEdit, setPrinterToEdit] = useState<any>(null)
  const [isPrinterEditMode, setIsPrinterEditMode] = useState(false)
  const [showOrderLogModal, setShowOrderLogModal] = useState(false)
  const [showGroupSelectionModal, setShowGroupSelectionModal] = useState(false)

  const [selectedGroups, setSelectedGroups] = useState<Set<string>>(new Set())
  const [selectedCombos, setSelectedCombos] = useState<Set<string>>(new Set())
  const [groupSearchTerm, setGroupSearchTerm] = useState('')
  const [selectedSectionOpen, setSelectedSectionOpen] = useState(true)
  const [remainingSectionOpen, setRemainingSectionOpen] = useState(true)

  const [openDeviceType, setOpenDeviceType] = useState(false)
  const [openDeviceStatus, setOpenDeviceStatus] = useState(false)
  const [openStoreLocation, setOpenStoreLocation] = useState(false)
  const [openTimezone, setOpenTimezone] = useState(false)
  const [openDeviceTypeLocal, setOpenDeviceTypeLocal] = useState(false)
  const [openTypeAllowConnectPos, setOpenTypeAllowConnectPos] = useState(false)
  const [openKdsNotification, setOpenKdsNotification] = useState(false)

  const { mutate: updateDevice, isPending: isUpdating } = useUpdateDevice()
  const { mutate: createDevice, isPending: isCreating } = useCreateDevice()
  const { mutate: deletePrinter, isPending: isDeletingPrinter } = useDeletePrinter()
  const { data: deviceData, isLoading: isLoadingDevice } = useDeviceDetail(id || '', isEditMode)
  const { data: storesData } = useStoresData()

  const { data: printersData, isLoading: isLoadingPrinters } = usePrintersData({
    params: { pos_device_code: deviceData?.device_code },
    enabled: isEditMode && !!deviceData?.device_code
  })

  const { data: itemTypesData } = useItemTypesData({
    enabled: isEditMode
  })

  const { data: combosData } = useCombosData({
    enabled: isEditMode
  })

  useEffect(() => {
    if (isEditMode && deviceData) {
      const extraData = (deviceData as any).extra_data || {}
      const formattedData = formatDeviceData(deviceData, extraData)
      setFormData(formattedData)

      if (Array.isArray(extraData.item_type_ignore)) {
        const groups = new Set<string>()
        const combos = new Set<string>()

        extraData.item_type_ignore.forEach((id: string) => {
          const isGroup = itemTypesData?.some(item => item.id === id)
          const isCombo = combosData?.some(combo => combo.id === id)

          if (isGroup) groups.add(id)
          if (isCombo) combos.add(id)
        })

        setSelectedGroups(groups)
        setSelectedCombos(combos)
      }
    }
  }, [isEditMode, deviceData, itemTypesData, combosData])

  const handleSave = async () => {
    try {
      if (isEditMode) {
        const updateData = buildUpdatePayload(
          formData,
          deviceData,
          selectedGroups,
          selectedCombos,
          itemTypesData,
          combosData
        )

        updateDevice(
          { id: id!, ...updateData },
          {
            onSuccess: () => {
              toast.success('Cập nhật thiết bị thành công!')
              navigate({ to: '/devices/list' })
            },
            onError: error => {
              const errorMessage = getErrorMessage(error)
              toast.error(errorMessage)
            }
          }
        )
      } else {
        if (!storeUid) {
          toast.error('Không tìm thấy thông tin cửa hàng')
          return
        }

        const createData = {
          ...buildCreatePayload(formData, storeUid, selectedGroups, selectedCombos),
          brandUid: auth?.brands?.[0]?.id || ''
        }

        createDevice(createData, {
          onSuccess: () => {
            toast.success('Tạo thiết bị thành công!')
            navigate({ to: '/devices/list' })
          },
          onError: error => {
            const errorMessage = getErrorMessage(error)
            toast.error(errorMessage)
          }
        })
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleDeletePrinter = (printer: any) => {
    if (isDeletingPrinter) return
    setPrinterToDelete(printer)
    setShowDeletePrinterModal(true)
  }

  const confirmDeletePrinter = () => {
    if (!printerToDelete || isDeletingPrinter) return

    deletePrinter(printerToDelete.id, {
      onSuccess: () => {
        toast.success(`Xóa máy in "${printerToDelete.printer_name}" thành công!`)
        setShowDeletePrinterModal(false)
        setPrinterToDelete(null)
      },
      onError: (error: any) => {
        const errorMessage = getErrorMessage(error)
        toast.error(errorMessage)
      }
    })
  }

  const handleBack = () => {
    if (canGoBack) {
      router.history.back()
    } else {
      navigate({ to: '/devices/list' })
    }
  }

  const handleGroupSelectionConfirm = () => {
    const selectedGroupIds = Array.from(selectedGroups)
    const selectedComboIds = Array.from(selectedCombos)

    const itemTypeIgnore = [...selectedGroupIds, ...selectedComboIds]
    setFormData(
      prev =>
        ({
          ...prev,
          enableComboGroup: itemTypeIgnore.length > 0,
          extra_data: {
            ...(prev as any).extra_data,
            item_type_ignore: itemTypeIgnore
          }
        }) as any
    )

    setShowGroupSelectionModal(false)
  }

  return {
    formData,
    setFormData,
    allowEditIpServer,
    setAllowEditIpServer,

    showDeleteModal,
    setShowDeleteModal,
    showPrinterModal,
    setShowPrinterModal,
    showDeletePrinterModal,
    setShowDeletePrinterModal,
    printerToDelete,
    setPrinterToDelete,
    printerToEdit,
    setPrinterToEdit,
    isPrinterEditMode,
    setIsPrinterEditMode,
    showOrderLogModal,
    setShowOrderLogModal,
    showGroupSelectionModal,
    setShowGroupSelectionModal,

    selectedGroups,
    setSelectedGroups,
    selectedCombos,
    setSelectedCombos,
    groupSearchTerm,
    setGroupSearchTerm,
    selectedSectionOpen,
    setSelectedSectionOpen,
    remainingSectionOpen,
    setRemainingSectionOpen,

    openDeviceType,
    setOpenDeviceType,
    openDeviceStatus,
    setOpenDeviceStatus,
    openStoreLocation,
    setOpenStoreLocation,
    openTimezone,
    setOpenTimezone,
    openDeviceTypeLocal,
    setOpenDeviceTypeLocal,
    openTypeAllowConnectPos,
    setOpenTypeAllowConnectPos,
    openKdsNotification,
    setOpenKdsNotification,

    deviceData,
    storesData,
    printersData,
    itemTypesData,
    combosData,

    isLoadingDevice,
    isLoadingPrinters,
    isUpdating,
    isCreating,
    isDeletingPrinter,

    handleSave,
    handleDeletePrinter,
    confirmDeletePrinter,
    handleBack,
    handleGroupSelectionConfirm,

    isEditMode
  }
}
