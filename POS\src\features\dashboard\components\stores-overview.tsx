import { ReportsStoresData } from '@/types/api'
import { Bar, <PERSON><PERSON>hart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'

// Transform API data to chart format
const transformStoresData = (storesData: ReportsStoresData[]) => {
  return storesData
    .map(store => ({
      name: store.store_name,
      total: store.revenue_net, // Use revenue_net for accurate net revenue
      sales: store.total_sales
    }))
    .sort((a, b) => b.total - a.total) // Sort by revenue descending
    .slice(0, 5) // Take top 5
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const value = payload[0].value
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('vi-VN').format(amount)
    }

    return (
      <div className='rounded-lg border bg-white p-3 shadow-lg'>
        <p className='font-semibold text-gray-900'>{label}</p>
        <p className='text-xs text-gray-600'>Doanh thu ròng: {formatCurrency(value)} đ</p>
      </div>
    )
  }

  return null
}

interface StoresOverviewProps {
  storesData?: ReportsStoresData[]
}

export function StoresOverview({ storesData }: StoresOverviewProps) {
  // Check if no data available
  if (!storesData || storesData.length === 0) {
    return (
      <div className='flex h-[350px] items-center justify-center'>
        <div className='text-muted-foreground text-sm'>Chưa có thông tin</div>
      </div>
    )
  }

  const chartData = transformStoresData(storesData)

  return (
    <ResponsiveContainer width='100%' height={350}>
      <BarChart data={chartData}>
        <XAxis
          dataKey='name'
          stroke='#888888'
          fontSize={10}
          tickLine={false}
          axisLine={false}
          angle={0}
          textAnchor='middle'
          height={60}
          interval={0}
          tick={{ fontSize: 10, textAnchor: 'middle' }}
          tickFormatter={value => {
            // Truncate long store names
            return value.length > 12 ? value.substring(0, 12) + '...' : value
          }}
        />
        <YAxis
          stroke='#888888'
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={value => `${(value / 1000000).toFixed(0)}M`}
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar dataKey='total' fill='currentColor' radius={[4, 4, 0, 0]} className='fill-primary' maxBarSize={30} />
      </BarChart>
    </ResponsiveContainer>
  )
}
