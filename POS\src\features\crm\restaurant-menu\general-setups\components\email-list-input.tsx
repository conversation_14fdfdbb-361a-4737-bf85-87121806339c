import { useState, KeyboardEvent } from 'react'
import { X } from 'lucide-react'

import { Input } from '@/components/ui/input'

interface EmailListInputProps {
  value: string[]
  onChange: (emails: string[]) => void
  placeholder?: string
  disabled?: boolean
}

export function EmailListInput({ 
  value = [], 
  onChange, 
  placeholder = "Nhập email và nhấn enter",
  disabled = false 
}: EmailListInputProps) {
  const [inputValue, setInputValue] = useState('')
  const [error, setError] = useState('')

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addEmail()
    }
  }

  const addEmail = () => {
    const email = inputValue.trim()
    
    if (!email) {
      return
    }

    if (!validateEmail(email)) {
      setError('<PERSON>ail không hợp lệ')
      return
    }

    if (value.includes(email)) {
      setError('Email đã tồn tại')
      return
    }

    onChange([...value, email])
    setInputValue('')
    setError('')
  }

  const removeEmail = (emailToRemove: string) => {
    onChange(value.filter(email => email !== emailToRemove))
  }

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue)
    if (error) {
      setError('')
    }
  }

  return (
    <div className="space-y-3">
      <div className="min-h-[60px] border border-input rounded-md p-3 bg-background">
        <div className="flex flex-wrap gap-2 mb-2">
          {value.map((email, index) => (
            <div
              key={index}
              className="inline-flex items-center gap-1 bg-blue-600 text-white px-3 py-1 rounded text-sm"
            >
              <span>{email}</span>
              <button
                type="button"
                onClick={() => removeEmail(email)}
                disabled={disabled}
                className="ml-1 hover:bg-blue-700 rounded-full p-0.5 transition-colors"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>

        <Input
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className={`border-none shadow-none p-0 h-auto focus-visible:ring-0 focus-visible:ring-offset-0 ${error ? 'text-destructive' : ''}`}
        />
      </div>

      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  )
}
