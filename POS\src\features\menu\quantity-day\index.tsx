import { useState, useEffect } from 'react'

import { useStoresData } from '@/hooks/api'
import { useItemsData } from '@/hooks/api/use-items'
import { useQuantityDaysForTable } from '@/hooks/api/use-quantity-days'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import {
  columns,
  QuantityDayTableSkeleton,
  QuantityDayTableWrapper,
  QuantityDayDialogs
} from './components'
import QuantityDayProvider from './context'

export default function QuantityDayPage() {
  const [selectedStoreUid, setSelectedStoreUid] = useState<string>('')
  const [selectedItemUid, setSelectedItemUid] = useState<string>('all')
  const { data: storesData } = useStoresData()
  const { data: itemsData } = useItemsData()

  useEffect(() => {
    if (storesData && storesData.length > 0 && !selectedStoreUid) {
      setSelectedStoreUid(storesData[0].id)
    }
  }, [storesData, selectedStoreUid])

  const selectedItemId =
    selectedItemUid !== 'all'
      ? itemsData?.find(item => item.id === selectedItemUid)?.item_id
      : undefined

  const {
    data: quantityDaysData,
    isLoading,
    error
  } = useQuantityDaysForTable({
    params: {
      store_uid: selectedStoreUid || undefined,
      list_item_id: selectedItemId
    },
    enabled: !!selectedStoreUid
  })

  if (error) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>
            Có lỗi xảy ra khi tải dữ liệu cấu hình số lượng
          </p>
          <button
            onClick={() => window.location.reload()}
            className='text-primary text-sm hover:underline'
          >
            Thử lại
          </button>
        </div>
      </div>
    )
  }

  return (
    <QuantityDayProvider>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          {isLoading && <QuantityDayTableSkeleton />}
          {!isLoading && (
            <QuantityDayTableWrapper
              columns={columns}
              data={quantityDaysData}
              storesData={storesData}
              itemsData={itemsData}
              onItemChange={setSelectedItemUid}
              onStoreChange={setSelectedStoreUid}
              selectedStoreUid={selectedStoreUid}
              selectedItemUid={selectedItemUid}
            />
          )}
        </div>
      </Main>
      <QuantityDayDialogs />
    </QuantityDayProvider>
  )
}
