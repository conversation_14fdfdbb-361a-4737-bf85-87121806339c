import { ColumnDef } from '@tanstack/react-table'
import { AccountingSaleData } from '@/hooks/use-accounting-sales-all'
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header'
import { formatCurrency } from './invoice-utils'

export type InvoiceData = AccountingSaleData & { storeName: string }

export function invoiceColumns(): ColumnDef<InvoiceData>[] {
  const columns: ColumnDef<InvoiceData>[] = [
    // # - Index column
    {
      id: 'index',
      header: '#',
      cell: ({ row }) => (
        <div className='w-[50px] text-center'>{row.index + 1}</div>
      ),
      enableSorting: false,
      enableHiding: false,
      size: 50,
    },
    // Mã hoá đơn - sticky column
    {
      accessorKey: 'tran_id',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Mã hoá đơn' />
      ),
      cell: ({ row }) => {
        const fullTranId = row.getValue('tran_id') as string
        const shortTranId = fullTranId ? `#${fullTranId.slice(-5)}` : ''
        return (
          <div className='cursor-help font-mono text-sm' title={fullTranId}>
            {shortTranId}
          </div>
        )
      },
      enablePinning: true,
      enableHiding: false,
    },
    // Số HĐ
    {
      accessorKey: 'tran_no',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Số HĐ' />
      ),
      cell: ({ row }) => (
        <div className='font-mono text-sm'>
          {row.getValue('tran_no') || '-'}
        </div>
      ),
    },
    // Mã ca
    {
      accessorKey: 'shift_id',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Mã ca' />
      ),
      cell: ({ row }) => {
        const fullShiftId = row.getValue('shift_id') as string
        const shortShiftId = fullShiftId ? `#${fullShiftId.slice(-5)}` : ''
        return (
          <div className='cursor-help font-mono text-sm' title={fullShiftId}>
            {shortShiftId || '-'}
          </div>
        )
      },
    },
    // Số khách
    {
      accessorKey: 'extra_data.peo_count',
      header: 'Số khách',
      cell: ({ row }) => {
        const peoCount = row.original.extra_data?.peo_count || 0
        return <div className='text-center'>{peoCount}</div>
      },
      enableSorting: false,
      enableHiding: false,
    },
    // Ghi chú
    {
      accessorKey: 'sale_note',
      header: 'Ghi chú',
      cell: ({ row }) => (
        <div
          className='max-w-[200px] truncate text-sm'
          title={row.getValue('sale_note')}
        >
          {row.getValue('sale_note') || '-'}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Nguồn
    {
      accessorKey: 'source_voucher',
      header: 'Nguồn',
      cell: ({ row }) => (
        <div className='text-sm'>{row.getValue('source_voucher') || '-'}</div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Khu vực
    {
      accessorKey: 'area_name',
      header: 'Khu vực',
      cell: ({ row }) => (
        <div className='text-sm'>{row.getValue('area_name') || '-'}</div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // PTTT (Phương thức thanh toán)
    {
      accessorKey: 'payment_method_name',
      header: 'PTTT',
      cell: ({ row }) => (
        <div className='text-sm'>
          {row.getValue('payment_method_name') || '-'}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Ngày chứng từ
    {
      accessorKey: 'tran_date',
      header: 'Ngày chứng từ',
      cell: ({ row }) => {
        const tranDate = row.getValue('tran_date') as number
        const formattedDate = tranDate
          ? new Date(tranDate).toLocaleDateString('vi-VN', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-'
        return <div className='text-sm'>{formattedDate}</div>
      },
      enableSorting: false,
      enableHiding: false,
    },
    // Giờ vào
    {
      accessorKey: 'start_hour',
      header: 'Giờ vào',
      cell: ({ row }) => {
        const startHour = row.getValue('start_hour') as number
        const startMinute = row.original.start_minute || 0
        const timeString =
          startHour !== undefined && startHour !== null
            ? `${String(startHour).padStart(2, '0')}:${String(startMinute).padStart(2, '0')}`
            : '-'
        return <div className='font-mono text-sm'>{timeString}</div>
      },
      enableSorting: false,
      enableHiding: false,
    },
    // Giờ ra
    {
      accessorKey: 'end_hour',
      header: 'Giờ ra',
      cell: ({ row }) => {
        const endHour = row.getValue('end_hour') as number
        const endMinute = row.original.end_minute || 0
        const timeString =
          endHour !== undefined && endHour !== null
            ? `${String(endHour).padStart(2, '0')}:${String(endMinute).padStart(2, '0')}`
            : '-'
        return <div className='font-mono text-sm'>{timeString}</div>
      },
      enableSorting: false,
      enableHiding: false,
    },
    // Thành tiền
    {
      accessorKey: 'amount_origin',
      header: 'Thành tiền',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('amount_origin'))}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Giảm giá
    {
      accessorKey: 'amount_discount_detail',
      header: 'Giảm giá',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('amount_discount_detail'))}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Phần trăm chiết khấu
    {
      id: 'discount_percentage',
      header: 'Phần trăm chiết khấu',
      cell: ({ row }) => {
        const amountOrigin = row.original.amount_origin || 0
        const discountAmount = row.original.amount_discount_detail || 0
        const percentage =
          amountOrigin > 0 ? (discountAmount / amountOrigin) * 100 : 0
        return (
          <div className='text-right font-medium'>{percentage.toFixed(2)}%</div>
        )
      },
      enableSorting: false,
      enableHiding: false,
    },
    // Chiết khấu
    {
      accessorKey: 'discount_extra_amount',
      header: 'Chiết khấu',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('discount_extra_amount'))}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Phí hỗ trợ marketing
    {
      accessorKey: 'partner_marketing_amount',
      header: 'Phí hỗ trợ marketing',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('partner_marketing_amount'))}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Phiếu GG (Voucher amount)
    {
      accessorKey: 'voucher_amount',
      header: 'Phiếu GG',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('voucher_amount'))}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Phí dịch vụ
    {
      accessorKey: 'service_charge_amount',
      header: 'Phí dịch vụ',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('service_charge_amount'))}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Thuế
    {
      accessorKey: 'vat_amount',
      header: 'Thuế',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('vat_amount'))}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Giảm giá VAT
    {
      accessorKey: 'discount_vat_amount',
      header: 'Giảm giá VAT',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('discount_vat_amount'))}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Phí ship
    {
      accessorKey: 'ship_fee_amount',
      header: 'Phí ship',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('ship_fee_amount'))}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Tổng tiền (không bao gồm VAT) - calculated field
    {
      id: 'total_amount_no_vat',
      header: 'Tổng tiền (không bao gồm VAT)',
      cell: ({ row }) => {
        const totalAmount = row.original.total_amount || 0
        const vatAmount = row.original.vat_amount || 0
        const totalNoVat = totalAmount - vatAmount
        return (
          <div className='text-right font-medium'>
            {formatCurrency(totalNoVat)}
          </div>
        )
      },
      enableSorting: false,
      enableHiding: false,
    },
    // Hoa hồng
    {
      accessorKey: 'commission_amount',
      header: 'Hoa hồng',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('commission_amount'))}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Tổng tiền (bao gồm hoa hồng) - calculated field
    {
      id: 'total_amount_with_commission',
      header: 'Tổng tiền (bao gồm hoa hồng)',
      cell: ({ row }) => {
        const totalAmount = row.original.total_amount || 0
        const commissionAmount = row.original.commission_amount || 0
        const totalWithCommission = totalAmount + commissionAmount
        return (
          <div className='text-right font-medium'>
            {formatCurrency(totalWithCommission)}
          </div>
        )
      },
      enableSorting: false,
      enableHiding: false,
    },
    // Nhân viên
    {
      accessorKey: 'employee_name',
      header: 'Nhân viên',
      cell: ({ row }) => (
        <div className='text-sm'>{row.getValue('employee_name') || '-'}</div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Tên khách
    {
      accessorKey: 'extra_data.customer_name',
      header: 'Tên khách',
      cell: ({ row }) => {
        const customerName = row.original.extra_data?.customer_name || ''
        return <div className='text-sm'>{customerName || '-'}</div>
      },
      enableSorting: false,
      enableHiding: false,
    },
    // Tên CTKM (Chương trình khuyến mãi)
    {
      accessorKey: 'voucher_name',
      header: 'Tên CTKM',
      cell: ({ row }) => (
        <div
          className='max-w-[200px] truncate text-sm'
          title={row.getValue('voucher_name')}
        >
          {row.getValue('voucher_name') || '-'}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Mã voucher
    {
      accessorKey: 'voucher_code',
      header: 'Mã voucher',
      cell: ({ row }) => (
        <div className='text-sm'>{row.getValue('voucher_code') || '-'}</div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Số điện thoại
    {
      accessorKey: 'extra_data.customer_phone',
      header: 'Số điện thoại',
      cell: ({ row }) => {
        const customerPhone = row.original.extra_data?.customer_phone || ''
        return <div className='font-mono text-sm'>{customerPhone || '-'}</div>
      },
      enableSorting: false,
      enableHiding: false,
    },
    // Tổng tiền - sticky last column
    {
      accessorKey: 'total_amount',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Tổng tiền' />
      ),
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {formatCurrency(row.getValue('total_amount'))}
        </div>
      ),
      enablePinning: true,
      enableHiding: false,
    },
  ]

  return columns
}
