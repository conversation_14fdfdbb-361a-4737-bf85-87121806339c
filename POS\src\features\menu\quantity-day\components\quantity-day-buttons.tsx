'use client'

import { Plus, Upload } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { useQuantityDay } from '../context'

export function QuantityDayButtons() {
  const { setOpen } = useQuantityDay()

  return (
    <div className='flex items-center space-x-2'>
      <Button size='sm' className='h-8' onClick={() => setOpen('create')}>
        <Plus className='mr-2 h-4 w-4' />
        Thêm mới
      </Button>
      <Button size='sm' className='h-8' variant='outline' onClick={() => setOpen('import-from-file')}>
        <Upload className='mr-2 h-4 w-4' />
        Thêm món từ file
      </Button>
    </div>
  )
}
