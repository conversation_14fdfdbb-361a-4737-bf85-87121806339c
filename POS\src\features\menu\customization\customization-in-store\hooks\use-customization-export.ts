import { useState, useRef, useEffect } from 'react'

import { ParsedCustomizationData } from '@/types/customizations'
import { toast } from 'sonner'

import { usePosStores } from '@/stores/posStore'

import { useExportCustomizations, useBulkImportCustomizations } from '@/hooks/api'

import { useExcelParser } from './use-excel-parser'

export function useCustomizationExport() {
  const { currentBrandStores } = usePosStores()
  const [exportStoreId, setExportStoreId] = useState<string>(
    currentBrandStores.length > 0 ? currentBrandStores[0].id : 'all'
  )
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [parsedData, setParsedData] = useState<ParsedCustomizationData[]>([])
  const [showParsedData, setShowParsedData] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { parseExcelFile } = useExcelParser()
  const exportCustomizationsMutation = useExportCustomizations()
  const bulkImportCustomizationsMutation = useBulkImportCustomizations()

  // Set default export store to first store when stores are loaded
  useEffect(() => {
    if (currentBrandStores.length > 0 && exportStoreId === 'all') {
      setExportStoreId(currentBrandStores[0].id)
    }
  }, [currentBrandStores, exportStoreId])

  const resetExportState = () => {
    setShowParsedData(false)
    setParsedData([])
    setSelectedFile(null)
  }

  const handleDownloadExportFile = async () => {
    try {
      // Prepare store UIDs for export based on selection
      const exportListStoreUid = exportStoreId === 'all' ? currentBrandStores.map(store => store.id) : [exportStoreId]

      // Get store name from current selection
      const selectedStore = currentBrandStores.find(store => store.id === exportStoreId)
      const storeName = selectedStore?.store_name || 'N/A'

      await exportCustomizationsMutation.mutateAsync({
        list_city_uid: exportListStoreUid,
        storeName: storeName
      })

      toast.success('File đã được tải xuống thành công!')
    } catch {
      toast.error('Có lỗi xảy ra khi tải file xuống')
    }
  }

  const handleUploadFile = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setSelectedFile(file)

    try {
      const parsedCustomizations = await parseExcelFile(file, [], false)
      setParsedData(parsedCustomizations)
      setShowParsedData(true)
      toast.success(`Đã phân tích ${parsedCustomizations.length} customization từ file!`)
    } catch {
      // Error handling is done in parseExcelFile
    }
  }

  const handleSaveImportedData = async () => {
    if (parsedData.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return false
    }

    try {
      await bulkImportCustomizationsMutation.mutateAsync({
        parsedData
      })
      toast.success(`Đã tạo thành công ${parsedData.length} customization!`)
      resetExportState()
      return true
    } catch {
      toast.error('Lỗi khi tạo customization. Vui lòng thử lại.')
      return false
    }
  }

  return {
    exportStoreId,
    setExportStoreId,
    selectedFile,
    parsedData,
    showParsedData,
    fileInputRef,
    stores: currentBrandStores,
    resetExportState,
    handleDownloadExportFile,
    handleUploadFile,
    handleFileChange,
    handleSaveImportedData,
    isExporting: exportCustomizationsMutation.isPending,
    isSaving: bulkImportCustomizationsMutation.isPending
  }
}
