import { Table } from '@tanstack/react-table'

import { X } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface MenuScheduleTableToolbarProps<TData> {
  table: Table<TData>
  selectedStatus?: string
  onStatusChange?: (status: string) => void
}

export function MenuScheduleTableToolbar<TData>({
  table,
  selectedStatus,
  onStatusChange
}: MenuScheduleTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 flex-col gap-2 sm:flex-row sm:items-center sm:space-x-2'>
        <Select value={selectedStatus} onValueChange={onStatusChange}>
          <SelectTrigger className='h-8 w-[200px]'>
            <SelectValue placeholder='Trạng thái' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả</SelectItem>
            <SelectItem value='PENDING'>Lịch sắp diễn ra</SelectItem>
            <SelectItem value='PROCESS'>Lịch đang diễn ra</SelectItem>
            <SelectItem value='DONE'>Lịch đã hoàn thành</SelectItem>
          </SelectContent>
        </Select>

        {isFiltered && (
          <Button variant='ghost' onClick={() => table.resetColumnFilters()} className='h-8 px-2 lg:px-3'>
            Đặt lại
            <X className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>
    </div>
  )
}
