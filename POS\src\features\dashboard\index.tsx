import { Head<PERSON>, <PERSON> } from '@/components/layout'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { ActionBar, ReportCharts, StatisticsCards } from './components'
import { DashboardProvider } from './context'

export default function Dashboard() {
  return (
    <DashboardProvider>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <ActionBar />
        <StatisticsCards />
        <ReportCharts />
      </Main>
    </DashboardProvider>
  )
}
