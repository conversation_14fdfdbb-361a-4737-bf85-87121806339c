import { useEffect, useMemo, useState } from 'react'

import type { OrderSource } from '@/types/api/order-sources'

import { useOrderSourcesForSort, useUpdateOrderSourcesSort } from '@/hooks/api/use-order-sources'

import { Skeleton } from '@/components/ui/skeleton'

import { PosModal } from '@/components/pos'

const DEFAULT_EXCLUDED_SOURCES = ['ZALO', 'FACEBOOK', 'CRM']
const SKELETON_ITEMS_COUNT = 8
const GRID_COLUMNS = 4
const DRAG_DATA_TYPE = 'text/plain'

const MODAL_CONFIG = {
  title: 'Sắp xếp nguồn hàng',
  confirmText: 'Lưu',
  maxWidth: 'sm:max-w-4xl',
  description: 'Thứ tự hiển thị các nguồn đơn sẽ được áp dụng tại thiết bị bán hàng'
}

const MESSAGES = {
  loadError: '<PERSON><PERSON> lỗi xảy ra khi tải danh sách nguồn đơn hàng',
  noSources: 'Không có nguồn đơn hàng nào để sắp xếp'
}

const STYLES = {
  description: 'text-muted-foreground text-sm',
  errorText: 'text-sm text-red-600',
  emptyState: 'py-8 text-center',
  grid: `grid grid-cols-${GRID_COLUMNS} gap-3`,
  draggableItem:
    'flex aspect-square cursor-move items-center justify-center bg-slate-300 p-2 transition-colors select-none hover:bg-slate-400',
  itemText: 'text-center text-sm font-medium',
  skeletonContainer: 'aspect-square bg-slate-300 p-2'
}

interface OrderSourcesSortModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

function LoadingSkeleton() {
  return (
    <div className={STYLES.grid}>
      {Array.from({ length: SKELETON_ITEMS_COUNT }).map((_, index) => (
        <div key={index} className={STYLES.skeletonContainer}>
          <Skeleton className='h-4 w-full' />
        </div>
      ))}
    </div>
  )
}

function ErrorState() {
  return (
    <div className={STYLES.emptyState}>
      <p className={STYLES.errorText}>{MESSAGES.loadError}</p>
    </div>
  )
}

function EmptyState() {
  return (
    <div className={STYLES.emptyState}>
      <p className={STYLES.description}>{MESSAGES.noSources}</p>
    </div>
  )
}

interface DraggableSourceItemProps {
  source: OrderSource
  index: number
  onDragStart: (e: React.DragEvent, index: number) => void
  onDragOver: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, index: number) => void
}

function DraggableSourceItem({ source, index, onDragStart, onDragOver, onDrop }: DraggableSourceItemProps) {
  return (
    <div
      key={source.id}
      draggable
      onDragStart={e => onDragStart(e, index)}
      onDragOver={onDragOver}
      onDrop={e => onDrop(e, index)}
      className={STYLES.draggableItem}
    >
      <div className={STYLES.itemText}>{source.source_name}</div>
    </div>
  )
}

function useFilteredOrderSources(orderSourcesResponse: any) {
  return useMemo(() => {
    if (!orderSourcesResponse?.data) return []

    const isValidSource = (source: OrderSource) =>
      source.deleted_at === null && !DEFAULT_EXCLUDED_SOURCES.includes(source.source_name)

    return orderSourcesResponse.data.filter(isValidSource).sort((a: OrderSource, b: OrderSource) => a.sort - b.sort)
  }, [orderSourcesResponse?.data])
}

function useDragAndDrop(initialSources: OrderSource[]) {
  const [sortedSources, setSortedSources] = useState<OrderSource[]>([])

  useEffect(() => {
    setSortedSources(initialSources)
  }, [initialSources])

  const handleDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.setData(DRAG_DATA_TYPE, index.toString())
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    const dragIndex = parseInt(e.dataTransfer.getData(DRAG_DATA_TYPE))

    if (dragIndex === dropIndex) return

    const newSources = [...sortedSources]
    const draggedItem = newSources[dragIndex]

    newSources.splice(dragIndex, 1)
    newSources.splice(dropIndex, 0, draggedItem)

    setSortedSources(newSources)
  }

  return {
    sortedSources,
    handleDragStart,
    handleDragOver,
    handleDrop
  }
}

export function OrderSourcesSortModal({ open, onOpenChange }: OrderSourcesSortModalProps) {
  const { data: orderSourcesResponse, isLoading, error } = useOrderSourcesForSort()
  const { updateSort, isUpdating } = useUpdateOrderSourcesSort()

  const filteredSources = useFilteredOrderSources(orderSourcesResponse)
  const { sortedSources, handleDragStart, handleDragOver, handleDrop } = useDragAndDrop(filteredSources)

  const isProcessing = isLoading || isUpdating
  const hasNoSources = sortedSources.length === 0

  const handleSave = () => {
    const sortData = sortedSources.map((source, index) => ({
      source_id: source.source_id,
      sort: index
    }))

    updateSort(sortData, {
      onSuccess: () => onOpenChange(false)
    })
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  const renderContent = () => {
    if (isLoading) return <LoadingSkeleton />
    if (error) return <ErrorState />
    if (hasNoSources) return <EmptyState />

    return (
      <div className={STYLES.grid}>
        {sortedSources.map((source, index) => (
          <DraggableSourceItem
            key={source.id}
            source={source}
            index={index}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          />
        ))}
      </div>
    )
  }

  return (
    <PosModal
      open={open}
      onOpenChange={onOpenChange}
      title={MODAL_CONFIG.title}
      onCancel={handleCancel}
      onConfirm={handleSave}
      confirmText={MODAL_CONFIG.confirmText}
      hideButtons={false}
      confirmDisabled={isProcessing}
      isLoading={isUpdating}
      disableCancelButton={true}
      maxWidth={MODAL_CONFIG.maxWidth}
    >
      <div className='mb-4'>
        <p className={STYLES.description}>{MODAL_CONFIG.description}</p>
      </div>

      <div>{renderContent()}</div>
    </PosModal>
  )
}
