import { useState } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import type { Store as ApiStore } from '@/types/auth'
import type { ItemClass } from '@/types/item-class'
import { Upload, X } from 'lucide-react'

import type { ItemType } from '@/lib/item-types-api'
import type { Unit } from '@/lib/units-api'

import { Checkbox } from '@/components/ui/checkbox'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'

import { Combobox } from '@/components/pos/combobox'

import { generateItemId } from '../../utils/utils'
import { ImageColorDialog } from './image-color-dialog'

interface Props {
  form: UseFormReturn<any>
  mode: 'create' | 'update'
  itemTypes: ItemType[]
  itemClasses: ItemClass[]
  units: Unit[]
  stores: ApiStore[]
  onImageChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  imagePreview?: string | null
  onImageRemove?: () => void
}

export function ItemBasicInfo({
  form,
  mode,
  itemTypes,
  itemClasses,
  units,
  stores,
  onImageChange,
  imagePreview,
  onImageRemove
}: Props) {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedColor, setSelectedColor] = useState(() => {
    const itemColor = form.getValues('item_color')
    return itemColor && itemColor.trim() !== '' ? itemColor : '#000000'
  })
  const [useSelfOrderImage, setUseSelfOrderImage] = useState(false)

  const handleImageSelect = (file: File) => {
    const event = { target: { files: [file] } } as unknown as React.ChangeEvent<HTMLInputElement>
    onImageChange(event)
    setSelectedColor('#000000')
    form.setValue('item_color', '')
  }

  const handleColorSelect = (color: string) => {
    setSelectedColor(color)
    form.setValue('item_color', color)
    if (onImageRemove) {
      onImageRemove()
    }
  }

  const handleSelfOrderToggle = (enabled: boolean) => {
    setUseSelfOrderImage(enabled)
  }
  return (
    <>
      <div className='space-y-6'>
        <h3 className='text-lg font-semibold text-gray-900'>Chi tiết</h3>
        <div className='space-y-4'>
          {/* Thông tin cơ bản và Ảnh */}
          <div className='grid grid-cols-1 gap-8 lg:grid-cols-12'>
            <div className='space-y-6 lg:col-span-9'>
              {/* Tên món */}
              <FormField
                control={form.control}
                name='item_name'
                render={({ field }) => (
                  <FormItem>
                    <div className='flex items-center gap-4'>
                      <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>
                        Tên <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl className='flex-1'>
                        <Input placeholder='Nhập tên món' className='w-full' {...field} />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Giá */}
              <FormField
                control={form.control}
                name='ots_price'
                render={({ field }) => (
                  <FormItem>
                    <div className='flex items-center gap-4'>
                      <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>
                        Giá <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl className='flex-1'>
                        <Input
                          placeholder='0'
                          className='w-full'
                          value={field.value ? new Intl.NumberFormat('vi-VN').format(field.value) : ''}
                          onChange={e => {
                            const value = e.target.value.replace(/[^\d]/g, '')
                            field.onChange(value ? Number(value) : 0)
                          }}
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Mã món */}
              <FormField
                control={form.control}
                name='item_id'
                render={({ field }) => (
                  <FormItem>
                    <div className='flex items-center gap-4'>
                      <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>Mã món</FormLabel>
                      <FormControl className='flex-1'>
                        <div className='flex items-center gap-3'>
                          <Input
                            placeholder='Nếu để trống, hệ thống sẽ tự động tạo một mã món'
                            className='w-full'
                            {...field}
                            readOnly={mode === 'update' || !form.watch('enable_custom_item_id')}
                          />
                          {mode === 'create' && (
                            <FormField
                              control={form.control}
                              name='enable_custom_item_id'
                              render={({ field: checkboxField }) => (
                                <FormControl>
                                  <Checkbox
                                    checked={checkboxField.value}
                                    onCheckedChange={checked => {
                                      checkboxField.onChange(checked)
                                      if (checked) {
                                        form.setValue('item_id', '', { shouldDirty: true, shouldValidate: true })
                                      } else {
                                        form.setValue('item_id', generateItemId(), {
                                          shouldDirty: true,
                                          shouldValidate: true
                                        })
                                      }
                                    }}
                                    className='border-2 border-blue-500 data-[state=checked]:border-blue-500 data-[state=checked]:bg-blue-500'
                                  />
                                </FormControl>
                              )}
                            />
                          )}
                        </div>
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Ảnh */}
            <div className='lg:col-span-3'>
              <div className='relative'>
                <div
                  className='flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-2 transition-colors hover:bg-gray-100'
                  style={{
                    height: '156px',
                    backgroundColor: selectedColor && selectedColor !== '#000000' ? selectedColor : undefined
                  }}
                  onClick={() => setDialogOpen(true)}
                >
                  {imagePreview ? (
                    <img src={imagePreview} alt='Preview' className='h-full w-full rounded-lg object-cover' />
                  ) : selectedColor && selectedColor !== '#000000' ? (
                    <div className='h-full w-full rounded-lg' style={{ backgroundColor: selectedColor }} />
                  ) : (
                    <>
                      <Upload className='mb-1 h-6 w-6 text-gray-400' />
                      <span className='text-center text-xs text-gray-500'>Chọn ảnh</span>
                    </>
                  )}
                </div>
                {(imagePreview || (selectedColor && selectedColor !== '#000000')) && (
                  <button
                    type='button'
                    onClick={() => {
                      if (imagePreview && onImageRemove) {
                        onImageRemove()
                      }
                      setSelectedColor('#000000')
                      form.setValue('item_color', '')
                    }}
                    className='absolute -top-2 -right-2 rounded-full bg-gray-600 p-1 text-white transition-colors hover:bg-gray-700'
                  >
                    <X className='h-3 w-3' />
                  </button>
                )}
                <input type='file' accept='image/*' onChange={onImageChange} className='hidden' id='image-upload' />
              </div>
            </div>
          </div>

          {/* Mã barcode */}
          <FormField
            control={form.control}
            name='item_id_barcode'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>Mã barcode</FormLabel>
                  <FormControl className='flex-1'>
                    <Input
                      placeholder='Nếu bạn sử dụng tính năng scan QR thì POS hay tạo mã barcode'
                      className='w-full'
                      maxLength={15}
                      {...field}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Món ăn kèm */}
          <FormField
            control={form.control}
            name='is_eat_with'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>Món ăn kèm</FormLabel>
                  <FormControl>
                    <Checkbox
                      checked={field.value === 1}
                      onCheckedChange={checked => field.onChange(checked ? 1 : 0)}
                    />
                  </FormControl>
                </div>
              </FormItem>
            )}
          />

          {/* Không cập nhật số lượng món ăn kèm */}
          <FormField
            control={form.control}
            name='no_update_quantity_toping'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>
                    Không cập nhật số lượng món ăn kèm
                  </FormLabel>
                  <FormControl>
                    <Checkbox
                      checked={field.value === 1}
                      onCheckedChange={checked => field.onChange(checked ? 1 : 0)}
                    />
                  </FormControl>
                </div>
              </FormItem>
            )}
          />

          {/* Nhóm */}
          <FormField
            control={form.control}
            name='item_type_uid'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>Nhóm</FormLabel>
                  <FormControl className='flex-1'>
                    <Combobox
                      options={itemTypes.map(itemType => ({
                        value: itemType.id,
                        label: itemType.item_type_name,
                        disabled: itemType.active === 0,
                        status: itemType.active === 0 ? 'Deactive' : undefined,
                        statusClassName: itemType.active === 0 ? 'bg-red-100 text-red-700' : undefined
                      }))}
                      value={field.value}
                      onValueChange={value => field.onChange(value ? value : undefined)}
                      placeholder='Uncategory'
                      searchPlaceholder='Tìm kiếm...'
                      emptyText='Không tìm thấy nhóm.'
                      className='flex-1 text-blue-500'
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Loại món */}
          <FormField
            control={form.control}
            name='item_class_uid'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>Loại món</FormLabel>
                  <FormControl className='flex-1'>
                    <Combobox
                      options={itemClasses.map(itemClass => ({
                        value: itemClass.id,
                        label: itemClass.item_class_name,
                        disabled: itemClass.active === 0,
                        status: itemClass.active === 0 ? 'Deactive' : undefined,
                        statusClassName: itemClass.active === 0 ? 'bg-red-100 text-red-700' : undefined
                      }))}
                      value={field.value}
                      onValueChange={value => field.onChange(value ? value : undefined)}
                      placeholder='None'
                      searchPlaceholder='Tìm kiếm loại món...'
                      emptyText='Không tìm thấy loại món.'
                      className='flex-1 text-blue-500'
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Mô tả */}
          <FormField
            control={form.control}
            name='description'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-start gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>Mô tả</FormLabel>
                  <FormControl className='flex-1'>
                    <Textarea
                      placeholder='Nếu để trống thì tên món sẽ tự động làm mô tả món'
                      className='min-h-[80px] w-full'
                      {...field}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Trạng thái */}
          <FormField
            control={form.control}
            name='apply_with_store'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>Trạng thái</FormLabel>
                  <FormControl className='flex-1'>
                    <Input
                      className='w-full'
                      value={Number(field.value) === 1 ? 'Sửa từ món gốc' : 'Món mới'}
                      readOnly
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Cửa hàng áp dụng */}
          <FormField
            control={form.control}
            name='store_uid'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>
                    Cửa hàng áp dụng <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl className='flex-1'>
                    <Combobox
                      options={stores.map(store => ({
                        value: store.id,
                        label: store.store_name,
                        disabled: store.active === 0,
                        status: store.active === 0 ? 'Deactive' : undefined,
                        statusClassName: store.active === 0 ? 'bg-red-100 text-red-700' : undefined
                      }))}
                      value={field.value}
                      onValueChange={value => field.onChange(value ? value : undefined)}
                      placeholder='Chọn cửa hàng'
                      searchPlaceholder='Tìm cửa hàng...'
                      emptyText='Không tìm thấy cửa hàng.'
                      className='flex-1 text-blue-500'
                      disabled={mode === 'update' || !!form.getValues('store_uid')}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* SKU */}
          <FormField
            control={form.control}
            name='item_id_mapping'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>SKU</FormLabel>
                  <FormControl className='flex-1'>
                    <Input placeholder='Nhập mã SKU' className='w-full' maxLength={50} {...field} />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Đơn vị tính */}
          <FormField
            control={form.control}
            name='unit_uid'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>
                    Đơn vị tính <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl className='flex-1'>
                    <Combobox
                      options={units.map(unit => ({
                        value: unit.id,
                        label: unit.unit_name || unit.id
                      }))}
                      value={field.value}
                      onValueChange={value => field.onChange(value ? value : undefined)}
                      placeholder='Món'
                      searchPlaceholder='Tìm kiếm đơn vị tính...'
                      emptyText='Không tìm thấy đơn vị tính.'
                      className='flex-1 text-blue-500'
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Đơn vị tính thứ 2 */}
          <FormField
            control={form.control}
            name='unit_secondary_uid'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>Đơn vị tính thứ 2</FormLabel>
                  <FormControl className='flex-1'>
                    <Combobox
                      options={units.map(unit => ({
                        value: unit.id,
                        label: unit.unit_name || unit.id
                      }))}
                      value={field.value}
                      onValueChange={value => field.onChange(value ? value : undefined)}
                      placeholder='Chọn đơn vị tính'
                      searchPlaceholder='Tìm kiếm đơn vị tính...'
                      emptyText='Không tìm thấy đơn vị tính.'
                      className='flex-1 text-blue-500'
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* VAT */}
          <FormField
            control={form.control}
            name='ots_tax'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>VAT món ăn</FormLabel>
                  <FormControl className='flex-1'>
                    <div className='flex items-center space-x-2'>
                      <Input
                        type='number'
                        placeholder='0'
                        className='w-full'
                        inputMode='decimal'
                        step='0.01'
                        value={
                          field.value === null || field.value === undefined
                            ? ''
                            : String((Number(field.value) || 0) * 100)
                        }
                        onChange={e => {
                          const value = e.target.value
                          const numeric = value === '' ? undefined : Number(value)
                          field.onChange(numeric === undefined || Number.isNaN(numeric) ? 0 : numeric / 100)
                        }}
                      />
                      <span>%</span>
                    </div>
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Thời gian chế biến */}
          <FormField
            control={form.control}
            name='time_cooking'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-4'>
                  <FormLabel className='w-72 flex-shrink-0 text-left text-sm font-medium'>
                    Thời gian chế biến (phút)
                  </FormLabel>
                  <FormControl className='flex-1'>
                    <Input
                      type='number'
                      placeholder='0'
                      className='w-full'
                      value={field.value ? Math.round(field.value / 60000) : ''}
                      onChange={e => {
                        const minutes = e.target.value
                        field.onChange(minutes ? Number(minutes) * 60000 : 0)
                      }}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <ImageColorDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onImageSelect={handleImageSelect}
        onColorSelect={handleColorSelect}
        onSelfOrderToggle={handleSelfOrderToggle}
        selectedColor={selectedColor}
        useSelfOrderImage={useSelfOrderImage}
        currentImageUrl={imagePreview || undefined}
      />
    </>
  )
}
