import { ReactNode } from 'react'

import { Link, useLocation, useRouter } from '@tanstack/react-router'

import { ChevronRight } from 'lucide-react'

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar
} from '@/components/ui/sidebar'

import { Badge } from '../ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { NavCollapsible, NavItem, NavLink, type NavGroup } from './types'

export function NavGroup({ title, items }: NavGroup) {
  const { state } = useSidebar()
  const href = useLocation({ select: location => location.href })
  return (
    <SidebarGroup>
      <SidebarGroupLabel>{title}</SidebarGroupLabel>
      <SidebarMenu>
        {items.map(item => {
          const key = `${item.title}-${item.url}`

          if (!item.items) return <SidebarMenuLink key={key} item={item} href={href} />

          if (state === 'collapsed') return <SidebarMenuCollapsedDropdown key={key} item={item} href={href} />

          return <SidebarMenuCollapsible key={key} item={item} href={href} />
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}

const NavBadge = ({ children }: { children: ReactNode }) => (
  <Badge className='rounded-full px-1 py-0 text-xs'>{children}</Badge>
)

const SidebarMenuLink = ({ item, href }: { item: NavLink; href: string }) => {
  const { setOpenMobile } = useSidebar()
  const router = useRouter()

  const handleClick = (e: React.MouseEvent) => {
    if (item.guard === 'crm') {
      const token = localStorage.getItem('crm_token')
      if (!token) {
        e.preventDefault()
        router.navigate({ to: '/crm/connect-crm' })
        return
      }
    }
    setOpenMobile(false)
  }
  return (
    <SidebarMenuItem>
      <SidebarMenuButton asChild isActive={checkIsActive(href, item)} tooltip={item.title}>
        <Link to={item.url} onClick={handleClick}>
          {item.icon && <item.icon />}
          <span className='truncate whitespace-nowrap' title={item.title}>
            {item.title}
          </span>
          {item.badge && <NavBadge>{item.badge}</NavBadge>}
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}

const SidebarMenuCollapsible = ({ item, href }: { item: NavCollapsible; href: string }) => {
  const { setOpenMobile } = useSidebar()
  const router = useRouter()
  return (
    <Collapsible asChild defaultOpen={checkIsActive(href, item, true)} className='group/collapsible'>
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton tooltip={item.title}>
            {item.icon && <item.icon />}
            <span className='truncate whitespace-nowrap' title={item.title}>
              {item.title}
            </span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
            <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent className='CollapsibleContent'>
          <SidebarMenuSub>
            {item.items.map(subItem => {
              if (subItem.items) {
                return (
                  <SidebarMenuSubItem key={subItem.title}>
                    <Collapsible
                      asChild
                      defaultOpen={checkIsActive(href, subItem, true)}
                      className='group/sub-collapsible'
                    >
                      <div>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuSubButton>
                            {subItem.icon && <subItem.icon />}
                            <span className='truncate whitespace-nowrap' title={subItem.title}>
                              {subItem.title}
                            </span>
                            {subItem.badge && <NavBadge>{subItem.badge}</NavBadge>}
                            <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/sub-collapsible:rotate-90' />
                          </SidebarMenuSubButton>
                        </CollapsibleTrigger>
                        <CollapsibleContent className='CollapsibleContent'>
                          <div className='mt-1 ml-4 space-y-1'>
                            {subItem.items.map(nestedItem => (
                              <SidebarMenuSubButton
                                key={nestedItem.title}
                                asChild
                                isActive={checkIsActive(href, nestedItem)}
                                className='text-sm'
                              >
                                <Link
                                  to={nestedItem.url}
                                  onClick={e => {
                                    if ((nestedItem as any).guard === 'crm') {
                                      const token = localStorage.getItem('crm_token')
                                      if (!token) {
                                        e.preventDefault()
                                        router.navigate({ to: '/crm/connect-crm' })
                                        return
                                      }
                                    }
                                    setOpenMobile(false)
                                  }}
                                >
                                  {nestedItem.icon && <nestedItem.icon />}
                                  <span className='truncate whitespace-nowrap' title={nestedItem.title}>
                                    {nestedItem.title}
                                  </span>
                                  {nestedItem.badge && <NavBadge>{nestedItem.badge}</NavBadge>}
                                </Link>
                              </SidebarMenuSubButton>
                            ))}
                          </div>
                        </CollapsibleContent>
                      </div>
                    </Collapsible>
                  </SidebarMenuSubItem>
                )
              }

              return (
                <SidebarMenuSubItem key={subItem.title}>
                  <SidebarMenuSubButton asChild isActive={checkIsActive(href, subItem)}>
                    <Link
                      to={subItem.url}
                      onClick={(e: any) => {
                        if (subItem.guard === 'crm') {
                          const token = localStorage.getItem('crm_token')
                          if (!token) {
                            e.preventDefault()
                            router.navigate({ to: '/crm/connect-crm' })
                            return
                          }
                        }
                        setOpenMobile(false)
                      }}
                    >
                      {subItem.icon && <subItem.icon />}
                      <span className='truncate whitespace-nowrap' title={subItem.title}>
                        {subItem.title}
                      </span>
                      {subItem.badge && <NavBadge>{subItem.badge}</NavBadge>}
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              )
            })}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  )
}

const SidebarMenuCollapsedDropdown = ({ item, href }: { item: NavCollapsible; href: string }) => {
  const router = useRouter()
  return (
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuButton tooltip={item.title} isActive={checkIsActive(href, item)}>
            {item.icon && <item.icon />}
            <span className='truncate whitespace-nowrap'>{item.title}</span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
            <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent side='right' align='start' sideOffset={4}>
          <DropdownMenuLabel>
            {item.title} {item.badge ? `(${item.badge})` : ''}
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {item.items.map(sub => {
            if (sub.items) {
              return (
                <div key={sub.title}>
                  <DropdownMenuLabel className='text-muted-foreground px-2 py-1 text-xs font-medium'>
                    {sub.title}
                  </DropdownMenuLabel>
                  {sub.items.map(nestedItem => (
                    <DropdownMenuItem key={`${nestedItem.title}-${nestedItem.url}`} asChild>
                      <Link
                        to={nestedItem.url}
                        className={`${checkIsActive(href, nestedItem) ? 'bg-secondary' : ''} ml-4`}
                      >
                        {nestedItem.icon && <nestedItem.icon />}
                        <span className='max-w-52 text-wrap'>{nestedItem.title}</span>
                        {nestedItem.badge && <span className='ml-auto text-xs'>{nestedItem.badge}</span>}
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </div>
              )
            }

            return (
              <DropdownMenuItem key={`${sub.title}-${sub.url}`} asChild>
                <Link
                  to={sub.url}
                  className={`${checkIsActive(href, sub) ? 'bg-secondary' : ''}`}
                  onClick={(e: any) => {
                    if (sub.guard === 'crm') {
                      const token = localStorage.getItem('crm_token')
                      if (!token) {
                        e.preventDefault()
                        router.navigate({ to: '/crm/connect-crm' })
                        return
                      }
                    }
                  }}
                >
                  {sub.icon && <sub.icon />}
                  <span className='max-w-52 text-wrap'>{sub.title}</span>
                  {sub.badge && <span className='ml-auto text-xs'>{sub.badge}</span>}
                </Link>
              </DropdownMenuItem>
            )
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  )
}

function checkIsActive(href: string, item: NavItem, mainNav = false) {
  return (
    href === item.url ||
    href.split('?')[0] === item.url ||
    !!item?.items?.filter(i => i.url === href).length ||
    (mainNav && href.split('/')[1] !== '' && href.split('/')[1] === item?.url?.split('/')[1])
  )
}
