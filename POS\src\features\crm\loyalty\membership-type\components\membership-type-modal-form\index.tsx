import { useEffect } from 'react'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { usePosCompanyData, useBrandsData } from '@/hooks'
import type { MembershipTypeItem, CreateMembershipTypeParams } from '@/types/api/crm'

import { useCreateMembershipTypeMutation, useUpdateMembershipTypeMutation } from '@/hooks/crm'

import {
  Button,
  Form,
  Switch,
  Checkbox,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle
} from '@/components/ui'

import { MembershipTypeSection } from './membership-type-section'
import { RankAchievementConditions } from './rank-achievement-conditions'
import { RankReviewCycleSection } from './rank-review-cycle-section'
import { defaultValues, membershipTypeFormSchema, type MembershipTypeFormData } from './schema'

interface MembershipTypeModalFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  membershipType?: MembershipTypeItem
}

export const MembershipTypeModalForm: React.FC<MembershipTypeModalFormProps> = ({
  open,
  onOpenChange,
  membershipType
}) => {
  const editMode = !!membershipType

  const form = useForm<MembershipTypeFormData>({
    resolver: zodResolver(membershipTypeFormSchema),
    mode: 'onChange',
    defaultValues
  })

  const {
    setValue,
    watch,
    formState: { isSubmitting, errors }
  } = form
  const isRankReviewEnabled = watch('isRankReviewEnabled')
  const isNoChange = watch('isNoChange')

  const hasValidationErrors = Object.keys(errors).length > 0

  const updateMembershipTypeMutation = useUpdateMembershipTypeMutation()
  const createMembershipTypeMutation = useCreateMembershipTypeMutation()
  const posCompanyData = usePosCompanyData()
  const brandsData = useBrandsData()

  useEffect(() => {
    if (editMode && membershipType) {
      setValue('typeId', membershipType.type_id)
      setValue('typeName', membershipType.type_name)
      setValue('pointRate', (membershipType?.point_rate || 0) * 100)
      setValue('isNoChange', membershipType?.is_no_change === 1)
      setValue('upgradeAmount', membershipType?.upgrade_amount?.toString() || '0')
      setValue('upgradeMinusPointAmount', membershipType?.upgrade_minus_point_amount?.toString() || '0')
      setValue('upgradeResetPointAmount', membershipType?.upgrade_reset_point_amount?.toString() || '0')
      setValue('downgradeAmount', membershipType?.downgrade_amount?.toString() || '')
      setValue('downgradeToLevel', membershipType?.downgrade_to_level || '')
      setValue('downgradeMinusPointAmount', membershipType?.downgrade_minus_point_amount || 0)
      setValue('downgradeResetPointAmount', membershipType?.downgrade_reset_point_amount || 0)
      setValue('downgradeMinusPoint', membershipType?.downgrade_minus_point || 0)
      setValue('downgradeResetPoint', membershipType?.downgrade_reset_point || 0)
      setValue('unchangeMinusPointAmount', membershipType?.unchange_minus_point_amount || 0)
      setValue('unchangeResetPointAmount', membershipType?.unchange_reset_point_amount || 0)
      setValue('isActive', membershipType.active === 1)

      const hasDowngradeFields = !!(
        membershipType?.downgrade_amount ||
        membershipType?.downgrade_to_level ||
        membershipType?.downgrade_minus_point_amount ||
        membershipType?.downgrade_reset_point_amount ||
        membershipType?.downgrade_minus_point ||
        membershipType?.downgrade_reset_point ||
        membershipType?.unchange_minus_point_amount ||
        membershipType?.unchange_reset_point_amount
      )

      if (hasDowngradeFields) {
        setValue('isRankReviewEnabled', true)
        setValue('rankChangeType', 'downgrade')
      }

      if (membershipType?.upgrade_minus_point_amount) {
        setValue('moneyHandlingType', 'deduct')
      } else if (membershipType?.upgrade_reset_point_amount) {
        setValue('moneyHandlingType', 'reset')
      } else {
        setValue('moneyHandlingType', 'maintain')
      }

      if (membershipType?.downgrade_minus_point_amount) {
        setValue('moneyChangeType', 'deduct')
      } else if (membershipType?.downgrade_reset_point_amount) {
        setValue('moneyChangeType', 'reset')
      } else {
        setValue('moneyChangeType', 'maintain')
      }

      if (membershipType?.downgrade_minus_point) {
        setValue('pointChangeType', 'deduct')
      } else if (membershipType?.downgrade_reset_point) {
        setValue('pointChangeType', 'reset')
      } else {
        setValue('pointChangeType', 'maintain')
      }

      if (membershipType?.unchange_minus_point_amount) {
        setValue('exceededMoneyChangeType', 'deduct')
      } else if (membershipType?.unchange_reset_point_amount) {
        setValue('exceededMoneyChangeType', 'reset')
      } else {
        setValue('exceededMoneyChangeType', 'maintain')
      }
    } else {
      form.reset(defaultValues)
    }
  }, [editMode, membershipType, open, setValue, form.reset])

  const onSubmit = async (data: MembershipTypeFormData) => {
    if (!posCompanyData?.company_id || !brandsData?.[0]?.brand_id) {
      console.error('Missing required fields')
      return
    }

    try {
      if (editMode && membershipType) {
        const updateData: MembershipTypeItem = {
          id: membershipType.id,
          type_id: data.typeId || '',
          company_id: posCompanyData.company_id,
          type_name: data.typeName || '',
          point_rate: (data.pointRate || 0) / 100,
          active: data.isActive ? 1 : 0,
          is_no_change: data.isNoChange ? 1 : 0,
          created_at: membershipType.created_at,
          updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19),
          upgrade_amount: 0
        }

        if (!data.isNoChange) {
          updateData.upgrade_amount = data.upgradeAmount || '0'
          if (data.moneyHandlingType === 'deduct') {
            updateData.upgrade_minus_point_amount = data.upgradeMinusPointAmount || '0'
          }
          if (data.moneyHandlingType === 'reset') {
            updateData.upgrade_reset_point_amount = data.upgradeResetPointAmount || '0'
          }
        }

        if (!data.isNoChange) {
          if (data.downgradeAmount) updateData.downgrade_amount = Number(data.downgradeAmount)
          if (data.downgradeToLevel) updateData.downgrade_to_level = data.downgradeToLevel

          if (data.moneyChangeType === 'deduct') {
            updateData.downgrade_minus_point_amount = Number(data.downgradeMinusPointAmount)
          }
          if (data.moneyChangeType === 'reset') {
            updateData.downgrade_reset_point_amount = Number(data.downgradeResetPointAmount)
          }

          if (data.pointChangeType === 'deduct') {
            updateData.downgrade_minus_point = data.downgradeMinusPoint
          }
          if (data.pointChangeType === 'reset') {
            updateData.downgrade_reset_point = data.downgradeResetPoint
          }

          if (data.exceededMoneyChangeType === 'deduct') {
            updateData.unchange_minus_point_amount = Number(data.unchangeMinusPointAmount)
          }
          if (data.exceededMoneyChangeType === 'reset') {
            updateData.unchange_reset_point_amount = Number(data.unchangeResetPointAmount)
          }
        }

        await updateMembershipTypeMutation.mutateAsync(updateData)
        onOpenChange(false)
        return
      }

      const createData: CreateMembershipTypeParams = {
        company_id: posCompanyData.company_id,
        type_id: data.typeId || '',
        type_name: data.typeName || '',
        point_rate: (data.pointRate || 0) / 100,
        active: data.isActive ? 1 : 0,
        is_no_change: data.isNoChange ? 1 : 0
      }

      if (!data.isNoChange) {
        createData.upgrade_amount = data.upgradeAmount || '0'
        if (data.moneyHandlingType === 'deduct') {
          createData.upgrade_minus_point_amount = data.upgradeMinusPointAmount || '0'
        }
        if (data.moneyHandlingType === 'reset') {
          createData.upgrade_reset_point_amount = data.upgradeResetPointAmount || '0'
        }
      }

      if (!data.isNoChange) {
        if (data.downgradeAmount) createData.downgrade_amount = Number(data.downgradeAmount)
        if (data.downgradeToLevel) createData.downgrade_to_level = data.downgradeToLevel

        if (data.moneyChangeType === 'deduct') {
          createData.downgrade_minus_point_amount = Number(data.downgradeMinusPointAmount)
        }
        if (data.moneyChangeType === 'reset') {
          createData.downgrade_reset_point_amount = Number(data.downgradeResetPointAmount)
        }

        if (data.pointChangeType === 'deduct') {
          createData.downgrade_minus_point = data.downgradeMinusPoint
        }
        if (data.pointChangeType === 'reset') {
          createData.downgrade_reset_point = data.downgradeResetPoint
        }

        if (data.exceededMoneyChangeType === 'deduct') {
          createData.unchange_minus_point_amount = Number(data.unchangeMinusPointAmount)
        }
        if (data.exceededMoneyChangeType === 'reset') {
          createData.unchange_reset_point_amount = Number(data.unchangeResetPointAmount)
        }
      }

      await createMembershipTypeMutation.mutateAsync(createData)
      onOpenChange(false)
    } catch (error) {
      console.error('Error updating membership type:', error)
    }
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className='h-full w-[800px] overflow-y-auto p-6' side='right'>
        <SheetHeader className='mb-6'>
          <SheetTitle>{editMode ? 'Sửa hạng thành viên' : 'Tạo hạng thành viên'}</SheetTitle>
        </SheetHeader>

        <Form {...form}>
          <form id='membership-type-form' onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
            <MembershipTypeSection form={form} editMode={editMode} />

            {!isNoChange && (
              <>
                <RankAchievementConditions form={form} />

                <FormField
                  control={form.control}
                  name='isRankReviewEnabled'
                  render={({ field }) => (
                    <FormItem className='space-y-3'>
                      <div className='flex items-center space-x-2'>
                        <FormControl>
                          <Checkbox id='review-rank' checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                        <FormLabel htmlFor='review-rank' className='cursor-pointer text-sm font-medium'>
                          XÉT LẠI HẠNG THEO CHU KỲ
                        </FormLabel>
                        {field.value && (
                          <span className='text-sm'>
                            <a href='/crm/settings#loyalty' className='text-blue-600 hover:underline'>
                              Cài đặt chu kỳ tại đây
                            </a>
                          </span>
                        )}
                      </div>
                    </FormItem>
                  )}
                />

                {isRankReviewEnabled && <RankReviewCycleSection form={form} />}
              </>
            )}

            <FormField
              control={form.control}
              name='isActive'
              render={({ field }) => (
                <FormItem className='flex items-center justify-between'>
                  <FormLabel>Trạng thái</FormLabel>
                  <FormControl>
                    <Switch id='status' checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className='flex justify-end pt-4'>
              <Button type='submit' disabled={isSubmitting || hasValidationErrors}>
                {isSubmitting ? (editMode ? 'Đang cập nhật...' : 'Đang tạo...') : editMode ? 'Cập nhật' : 'Tạo mới'}
              </Button>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  )
}
