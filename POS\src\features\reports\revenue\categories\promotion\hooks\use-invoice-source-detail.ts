import { useQuery } from '@tanstack/react-query'

import { api } from '@/lib/api'

interface InvoiceSourceDetailParams {
  company_uid: string
  brand_uid: string
  start_date: number
  end_date: number
  promotion_id: string
  list_store_uid?: string | string[]
  page?: number
  results_per_page?: number
}

interface InvoiceSourceDetailResponse {
  data: Array<{
    id: string
    tran_id: string
    tran_no: string
    table_name: string
    source_deli: string
    total_amount: number
    tran_date: number
    employee_name: string
    sale_detail: Array<{
      item_name: string
      quantity: number
      price: number
    }>
    sale_payment_method: Array<{
      payment_method_name: string
      trace_no: string
    }>
    extra_data: {
      tran_no_partner: string
    }
  }>
  track_id?: string
}

export const useInvoiceSourceDetail = (params: InvoiceSourceDetailParams, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['invoice-source-detail', params],
    queryFn: async (): Promise<InvoiceSourceDetailResponse> => {
      const searchParams = new URLSearchParams({
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        start_date: params.start_date.toString(),
        end_date: params.end_date.toString(),
        promotion_id: params.promotion_id,
        page: (params.page || 1).toString(),
        results_per_page: (params.results_per_page || 20).toString(),
        by_days: '1',
        is_sales: '1'
      })

      if (params.list_store_uid) {
        const storeUids = Array.isArray(params.list_store_uid) ? params.list_store_uid.join(',') : params.list_store_uid
        if (storeUids && storeUids.length > 0) {
          searchParams.append('list_store_uid', storeUids)
        }
      }

      const response = await api.get(`/v3/pos-cms/report/sale_by_promotion?${searchParams.toString()}`)
      return response.data as InvoiceSourceDetailResponse
    },
    enabled: enabled && !!params.promotion_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false
  })
}
