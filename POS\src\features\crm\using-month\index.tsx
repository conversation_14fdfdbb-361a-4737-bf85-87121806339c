import { useState, useEffect } from 'react'

import { useUsingMonth } from '@/hooks/crm'

import { Main } from '@/components/layout/main'

import { ActionBar, TableData } from './components'

export default function UsingMonthPage() {
  const [selectedMonth, setSelectedMonth] = useState(() => {
    const now = new Date()
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
  })
  const [searchParams, setSearchParams] = useState<{
    year: string
    month: string
    pos_parent: string
  } | null>(null)

  const usingMonthQuery = useUsingMonth(searchParams)

  const handleSearch = () => {
    const [year, month] = selectedMonth.split('-')
    const params = {
      year,
      month,
      pos_parent: 'BRAND-953H'
    }
    setSearchParams(params)
  }

  useEffect(() => {
    handleSearch()
  }, [])

  const handleExport = () => {
    console.log('Export data')
  }

  return (
    <Main>
      <div className='container mx-auto space-y-6 py-6'>
        <div className='flex items-center justify-between'>
          <h1 className='text-3xl font-bold tracking-tight'>Lưu lượng sử dụng</h1>
        </div>

        <ActionBar
          selectedMonth={selectedMonth}
          onMonthChange={setSelectedMonth}
          onSearch={handleSearch}
          onExport={handleExport}
          isLoading={usingMonthQuery.isLoading}
        />

        {usingMonthQuery.data && <TableData data={usingMonthQuery.data} searchParams={searchParams} />}
      </div>
    </Main>
  )
}
