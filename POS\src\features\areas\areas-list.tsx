import { useState, useMemo } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { Plus, Upload } from 'lucide-react'

import { useAreasData } from '@/hooks/api/use-areas'

import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { AreasDataTable, createAreasColumns, ImportAreasModal } from './'

interface Store {
  id: string
  store_name: string
  active: number
}

export function AreasList() {
  const navigate = useNavigate()

  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData) ? storesData.filter((store: Store) => store.active === 1) : []
      }
      return []
    } catch (error) {
      console.error('Error parsing pos_stores_data:', error)
      return []
    }
  }, [])

  const [selectedStoreId, setSelectedStoreId] = useState<string>(stores[0]?.id || '')
  const [isImportModalOpen, setIsImportModalOpen] = useState(false)

  const {
    data: areas = [],
    isLoading,
    error
  } = useAreasData({
    storeUid: selectedStoreId
  })

  const columns = useMemo(() => createAreasColumns(selectedStoreId), [selectedStoreId])

  const handleCreateNew = () => {
    navigate({ to: '/setting/area/detail' })
  }

  const handleImportFromFile = () => {
    if (!selectedStoreId) {
      return
    }
    setIsImportModalOpen(true)
  }

  const handleCloseImportModal = () => {
    setIsImportModalOpen(false)
  }

  const handleImportSuccess = () => {
    setIsImportModalOpen(false)
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4'></div>

        <div className='container mx-auto space-y-6 py-6'>
          <div className='flex items-center gap-4'>
            <h1 className='text-2xl font-bold tracking-tight'>Danh sách khu vực</h1>

            <div className='flex-1'>
              <Select value={selectedStoreId} onValueChange={setSelectedStoreId}>
                <SelectTrigger className='w-[300px]'>
                  <SelectValue placeholder='Chọn cửa hàng' />
                </SelectTrigger>
                <SelectContent>
                  {stores.map(store => (
                    <SelectItem key={store.id} value={store.id}>
                      {store.store_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className='flex gap-2'>
              <Button variant='outline' onClick={handleImportFromFile}>
                <Upload className='mr-2 h-4 w-4' />
                Thêm khu vực từ file
              </Button>
              <Button onClick={handleCreateNew}>
                <Plus className='mr-2 h-4 w-4' />
                Tạo khu vực mới
              </Button>
            </div>
          </div>

          {/* Areas Table */}
          {isLoading && (
            <div className='rounded-md border p-8 text-center'>
              <p className='text-muted-foreground'>Đang tải...</p>
            </div>
          )}
          {error && (
            <div className='rounded-md border p-8 text-center'>
              <p className='text-muted-foreground'>Có lỗi xảy ra khi tải dữ liệu</p>
            </div>
          )}
          {!isLoading && !error && <AreasDataTable columns={columns} data={areas} storeUid={selectedStoreId} />}

          {!selectedStoreId && stores.length > 0 && (
            <div className='bg-card rounded-lg border p-8 text-center'>
              <p className='text-muted-foreground'>Vui lòng chọn cửa hàng để xem danh sách khu vực</p>
            </div>
          )}

          {stores.length === 0 && (
            <div className='bg-card rounded-lg border p-8 text-center'>
              <p className='text-muted-foreground'>Không có cửa hàng nào khả dụng</p>
            </div>
          )}

          {/* Import Areas Modal */}
          <ImportAreasModal
            open={isImportModalOpen}
            onOpenChange={setIsImportModalOpen}
            onCancel={handleCloseImportModal}
            onSuccess={handleImportSuccess}
          />
        </div>
      </Main>
    </>
  )
}
