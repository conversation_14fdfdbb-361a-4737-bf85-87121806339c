import {
  IconBrowserCheck,
  IconHelp,
  IconNotification,
  IconPalette,
  IconSettings,
  IconTool,
  IconUserCog,
} from '@tabler/icons-react'
import { type NavItem } from '../types'

export const settingsNavItems: NavItem[] = [
  {
    title: 'Settings',
    icon: IconSettings,
    items: [
      {
        title: 'Profile',
        url: '/settings',
        icon: IconUserCog,
      },
      {
        title: 'Account',
        url: '/settings/account',
        icon: IconTool,
      },
      {
        title: 'Appearance',
        url: '/settings/appearance',
        icon: IconPalette,
      },
      {
        title: 'Notifications',
        url: '/settings/notifications',
        icon: IconNotification,
      },
      {
        title: 'Display',
        url: '/settings/display',
        icon: IconBrowserCheck,
      },
    ],
  },
  {
    title: 'Help Center',
    url: '/help-center',
    icon: IconHelp,
  },
]
