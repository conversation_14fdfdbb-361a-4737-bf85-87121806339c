import { useState } from 'react'

export function useItemConfigurationState() {
  const [showQuantityInputs, setShowQuantityInputs] = useState(false)
  const [isCustomizationDetailsOpen, setIsCustomizationDetailsOpen] = useState(false)
  const [isPriceSourceDialogOpen, setIsPriceSourceDialogOpen] = useState(false)
  const [isBuffetConfigModalOpen, setIsBuffetConfigModalOpen] = useState(false)
  const [confirmDeleteIndex, setConfirmDeleteIndex] = useState<number | null>(null)

  const toggleQuantityInputs = () => {
    setShowQuantityInputs(!showQuantityInputs)
  }

  const openPriceSourceDialog = () => {
    setIsPriceSourceDialogOpen(true)
  }

  const closePriceSourceDialog = () => {
    setIsPriceSourceDialogOpen(false)
  }

  const openBuffetConfigModal = () => {
    setIsBuffetConfigModalOpen(true)
  }

  const closeBuffetConfigModal = () => {
    setIsBuffetConfigModalOpen(false)
  }

  const handleRemovePriceSource = (index: number) => {
    setConfirmDeleteIndex(index)
  }

  const clearConfirmDelete = () => {
    setConfirmDeleteIndex(null)
  }

  return {
    showQuantityInputs,
    isCustomizationDetailsOpen,
    isPriceSourceDialogOpen,
    isBuffetConfigModalOpen,
    confirmDeleteIndex,

    setShowQuantityInputs,
    setIsCustomizationDetailsOpen,
    setIsPriceSourceDialogOpen,
    setIsBuffetConfigModalOpen,
    setConfirmDeleteIndex,

    toggleQuantityInputs,
    openPriceSourceDialog,
    closePriceSourceDialog,
    openBuffetConfigModal,
    closeBuffetConfigModal,
    handleRemovePriceSource,
    clearConfirmDelete
  }
}
