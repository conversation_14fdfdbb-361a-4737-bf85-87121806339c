// Store Menu Types
export interface Store {
  id: string
  name: string
  phone: string
  address: string
  image?: string
  lastSalesDataUpdate?: string
  partnerDriverPhone?: string
  active?: boolean
  onlineSales?: boolean
  deliverySales?: boolean
  onlineReservation?: boolean
  emailList?: string[]
  banner?: string
}

export interface StoreFilters {
  search: string
}

export interface StoreMenuPageProps {
  className?: string
}

// Menu Items Types
export interface MenuItem {
  id: string
  code: string
  name: string
  group: string
  type: string
  status: 'available' | 'unavailable'
  canTakeaway: boolean
  canDelivery: boolean
  order: number
  lastUpdated: string
  image?: string
}

export interface MenuGroup {
  id: string
  code: string
  name: string
  status: 'available' | 'unavailable'
  order: number
  maxItems: number
  requiredItems: number
  lastUpdated: string
}

export interface MenuCombo {
  id: string
  code: string
  name: string
  type: 'combo' | 'custom-combo'
  items: string[]
  price: number
  status: 'available' | 'unavailable'
  canTakeaway: boolean
  canDelivery: boolean
  lastUpdated: string
  image?: string
}

export interface ItemsFilters {
  search: string
  group: string
  type: string
  status: string
  canTakeaway: string
  canDelivery: string
  order: string
}

export type MenuItemsDataType = 'item-type' | 'items' | 'combo' | 'combo-special'

export interface MenuItemsPageProps {
  className?: string
}
