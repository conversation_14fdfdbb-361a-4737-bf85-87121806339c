import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { QUERY_KEYS } from '@/constants/query-keys'

import { itemsInCityApiService } from './items-in-city-api'
import type {
  CreateItemInCityRequest,
  UpdateItemInCityRequest,
  UpdateItemStatusRequest,
  DeleteItemInCityParams,
  DeleteMultipleItemsInCityParams
} from './items-in-city-types'

export const useCreateItemInCity = () => {
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn: (data: CreateItemInCityRequest) => itemsInCityApiService.createItemInCity(data),
    onSuccess: () => {
      itemsInCityApiService.clearCache()

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_CITY_LIST]
      })
      toast.success('Tạo món thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi tạo món')
    }
  })

  return { createItemAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useUpdateItemInCity = () => {
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn: (data: UpdateItemInCityRequest) => itemsInCityApiService.updateItemInCity(data),
    onSuccess: () => {
      itemsInCityApiService.clearCache()

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_CITY_LIST]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_CITY_DETAIL]
      })
      toast.success('Cập nhật món thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật món')
    }
  })

  return { updateItemAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useDeleteItemInCity = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const mutation = useMutation({
    mutationFn: (id: string) => {
      const params: DeleteItemInCityParams = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        id
      }
      return itemsInCityApiService.deleteItemInCity(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_CITY_LIST]
      })
      toast.success('Xóa món thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi xóa món')
    }
  })

  return { deleteItemAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useDeleteMultipleItemsInCity = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const mutation = useMutation({
    mutationFn: (itemUids: string[]) => {
      const params: DeleteMultipleItemsInCityParams = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        list_item_uid: itemUids
      }
      return itemsInCityApiService.deleteMultipleItemsInCity(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_CITY_LIST] })
      toast.success('Xóa món ăn thành công')
    },
    onError: (error: Error) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi xóa món ăn')
    }
  })

  return { deleteMultipleItemsAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useUpdateItemInCityStatus = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const mutation = useMutation({
    mutationFn: (data: { id: string; active: number }) => {
      const params: UpdateItemStatusRequest = {
        id: data.id,
        active: data.active,
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || ''
      }
      return itemsInCityApiService.updateItemStatus(params)
    },
    onSuccess: () => {
      itemsInCityApiService.clearCache()

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_CITY_LIST]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_CITY_DETAIL]
      })
      toast.success('Cập nhật trạng thái thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật trạng thái')
    }
  })

  return { updateStatusAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useDownloadItemsTemplate = () => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const mutation = useMutation({
    mutationFn: (params: Record<string, unknown>) => {
      const downloadParams = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        ...params
      }
      return itemsInCityApiService.downloadTemplate(downloadParams)
    },
    onSuccess: (blob: Blob) => {
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `items-template-${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      toast.success('Tải template thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi tải template')
    }
  })

  return { downloadTemplateAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

// Hook riêng cho import template
export const useDownloadImportTemplate = () => {
  const mutation = useMutation({
    mutationFn: (referenceData?: {
      itemTypes?: any[]
      itemClasses?: any[]
      units?: any[]
      cities?: any[]
    }) => {
      return itemsInCityApiService.downloadImportTemplate(referenceData)
    },
    onSuccess: () => {
      toast.success('Tải template thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi tải template')
    }
  })

  return { downloadImportTemplateAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useFetchItemsData = () => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const mutation = useMutation({
    mutationFn: (params: Record<string, unknown>) => {
      const fetchParams = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        ...params
      }
      return itemsInCityApiService.fetchItemsData(fetchParams)
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi tải dữ liệu')
    }
  })

  return { fetchItemsDataAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useImportItems = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const mutation = useMutation({
    mutationFn: (items: unknown[]) => {
      const params = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        items
      }
      return itemsInCityApiService.importItems(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_CITY_LIST]
      })
      toast.success('Import món thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi import món')
    }
  })

  return { importItemsAsync: mutation.mutateAsync, isPending: mutation.isPending }
}
