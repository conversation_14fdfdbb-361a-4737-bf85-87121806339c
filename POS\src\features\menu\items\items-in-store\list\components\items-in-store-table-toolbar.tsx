'use client'

import { useEffect, useState } from 'react'

import { Table } from '@tanstack/react-table'

import { IconFilter } from '@tabler/icons-react'

import { X, Trash2 } from 'lucide-react'

import { usePosStores } from '@/stores/posStore'

import { useItemTypesData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

// Removed Select imports as status will use Combobox

import { MultiSelect } from '@/components/multi-select'
import { Combobox } from '@/components/pos/combobox'

const daysOfWeekOptions = [
  { label: 'Thứ 2', value: '2' },
  { label: 'Thứ 3', value: '3' },
  { label: 'Thứ 4', value: '4' },
  { label: 'Thứ 5', value: '5' },
  { label: 'Thứ 6', value: '6' },
  { label: 'Thứ 7', value: '7' },
  { label: 'Chủ Nhật', value: '1' }
]

const statusOptions = [
  { label: 'Tất cả trạng thái', value: 'all' },
  { label: 'Active', value: '1' },
  { label: 'Deactive', value: '0' }
]

const applyWithStoreOptions = [
  { label: 'Cửa hàng và thành phố', value: '0' },
  { label: 'Cửa hàng', value: '-1' }
]

interface ItemsInStoreTableToolbarProps<TData> {
  table: Table<TData>
  selectedItemTypeUid?: string
  onItemTypeChange?: (itemTypeUid: string) => void
  selectedStoreUid?: string
  onStoreChange?: (storeUid: string) => void
  selectedDaysOfWeek?: string[]
  onDaysOfWeekChange?: (daysOfWeek: string[]) => void
  selectedStatus?: string
  onStatusChange?: (status: string) => void
  selectedApplyWithStore?: string
  onApplyWithStoreChange?: (applyWithStore: string) => void
  onDeleteSelected?: () => void
}

export function ItemsInStoreTableToolbar<TData>({
  table,
  selectedItemTypeUid = 'all',
  onItemTypeChange,
  selectedStoreUid = '',
  onStoreChange,
  selectedDaysOfWeek = [],
  onDaysOfWeekChange,
  selectedStatus = 'all',
  onStatusChange,
  selectedApplyWithStore = '-1',
  onApplyWithStoreChange,
  onDeleteSelected
}: ItemsInStoreTableToolbarProps<TData>) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const { currentBrandStores } = usePosStores()
  const storeOptions = currentBrandStores
    .filter(store => store.active === 1)
    .map(store => ({ label: store.store_name, value: store.id }))

  const { data: itemTypesData = [] } = useItemTypesData()
  const itemTypeOptions = itemTypesData
    .filter(itemType => itemType.active === 1)
    .map(itemType => ({ label: itemType.item_type_name, value: itemType.id }))

  useEffect(() => {
    const firstStoreId = storeOptions[0]?.value
    const currentExists = storeOptions.some(opt => opt.value === selectedStoreUid)
    if (firstStoreId && (!selectedStoreUid || !currentExists)) {
      onStoreChange && onStoreChange(firstStoreId)
    }
  }, [selectedStoreUid, storeOptions, onStoreChange])

  useEffect(() => {
    const firstApplyWithStoreId = applyWithStoreOptions[0]?.value
    const currentExists = applyWithStoreOptions.some(opt => opt.value === selectedApplyWithStore)
    if (firstApplyWithStoreId && (!selectedApplyWithStore || !currentExists)) {
      onApplyWithStoreChange && onApplyWithStoreChange(firstApplyWithStoreId)
    }
  }, [selectedApplyWithStore, applyWithStoreOptions, onApplyWithStoreChange])

  const isFiltered = table.getState().columnFilters.length > 0
  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length

  return (
    <div className='space-y-4'>
      {/* Main toolbar */}
      <div className='flex items-center justify-between'>
        <div className='flex flex-1 items-center space-x-2'>
          {selectedRowsCount > 0 && (
            <Button variant='destructive' size='sm' onClick={onDeleteSelected} className='h-9'>
              <Trash2 />
              Xóa món ({selectedRowsCount})
            </Button>
          )}

          <Input
            placeholder='Tìm kiếm món ăn...'
            value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
            onChange={event => table.getColumn('name')?.setFilterValue(event.target.value)}
            className='h-9 w-[150px] lg:w-[250px]'
          />

          <Combobox
            value={selectedStoreUid}
            onValueChange={val => onStoreChange && onStoreChange(val)}
            options={storeOptions}
            placeholder='Chọn cửa hàng'
            searchPlaceholder='Tìm cửa hàng...'
            emptyText='Không có dữ liệu'
            className='w-[260px]'
          />

          <Combobox
            value={selectedItemTypeUid}
            onValueChange={val => onItemTypeChange && onItemTypeChange(val)}
            options={[{ label: 'Tất cả nhóm món', value: 'all' }, ...itemTypeOptions]}
            placeholder='Chọn loại món'
            searchPlaceholder='Tìm nhóm món...'
            emptyText='Không có dữ liệu'
            className='w-[260px]'
          />

          <Button variant='outline' size='sm' onClick={() => setShowAdvanced(!showAdvanced)} className='h-9'>
            <IconFilter className='h-4 w-4' />
            Nâng cao
          </Button>

          {isFiltered && (
            <Button variant='ghost' onClick={() => table.resetColumnFilters()} className='h-10 px-2 lg:px-3'>
              Reset
              <X className='ml-2 h-4 w-4' />
            </Button>
          )}
        </div>
      </div>

      {/* Advanced filters */}
      {showAdvanced && (
        <div className='flex items-center space-x-2'>
          <MultiSelect
            options={daysOfWeekOptions}
            value={selectedDaysOfWeek}
            onValueChange={onDaysOfWeekChange || (() => {})}
            placeholder='Chọn ngày trong tuần'
            className='min-h-9 w-[300px]'
            maxCount={1}
          />

          <Combobox
            value={selectedApplyWithStore}
            onValueChange={val => onApplyWithStoreChange && onApplyWithStoreChange(val)}
            options={applyWithStoreOptions}
            placeholder='Chọn áp dụng tại'
            searchPlaceholder='Tìm áp dụng tại...'
            emptyText='Không có dữ liệu'
            className='w-[220px]'
          />

          <Combobox
            value={selectedStatus}
            onValueChange={val => onStatusChange && onStatusChange(val)}
            options={statusOptions}
            placeholder='Chọn trạng thái'
            searchPlaceholder='Tìm trạng thái...'
            emptyText='Không có dữ liệu'
            className='w-[180px]'
          />
        </div>
      )}
    </div>
  )
}
