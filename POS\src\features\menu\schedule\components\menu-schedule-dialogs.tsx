import { ConfirmDialog } from '@/components/confirm-dialog'

import { useMenuSchedule } from '../context'
import { useDeleteMenuSchedule } from '../hooks'
import { MenuScheduleMutate } from './menu-schedule-mutate'

export function MenuScheduleDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useMenuSchedule()
  const deleteMutation = useDeleteMenuSchedule()

  return (
    <>
      <MenuScheduleMutate
        key='menu-schedule-day-create'
        open={open === 'create'}
        onOpenChange={isOpen => {
          if (!isOpen) {
            setOpen(null)
          }
        }}
      />

      {currentRow && (
        <>
          <MenuScheduleMutate
            key={`menu-schedule-day-update-${currentRow}`}
            open={open === 'update'}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            currentRow={currentRow}
          />

          <ConfirmDialog
            key='quantity-day-delete'
            destructive
            open={open === 'delete'}
            onOpenChange={() => {
              setOpen('delete')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            handleConfirm={async () => {
              if (currentRow) {
                try {
                  await deleteMutation.mutateAsync({
                    time: currentRow.time,
                    store_uid: currentRow.store_uid,
                    city_uid: currentRow.city_uid,
                    brand_uid: currentRow.brand_uid,
                    company_uid: currentRow.company_uid
                  })
                  setOpen(null)
                  setTimeout(() => {
                    setCurrentRow(null)
                  }, 500)
                } catch (_error) {
                  // Error is handled by the mutation's onError callback
                }
              }
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá cấu hình này?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}
    </>
  )
}
