import { useNavigate } from '@tanstack/react-router'

import type { CreateRoleRequest, UpdateRoleRequest, Role } from '@/types/role'
import { toast } from 'sonner'

import { useCreateRole, useUpdateRole } from '@/hooks/api/use-roles'

interface UseRoleSaveProps {
  roleName: string
  description: string
  permissions: string[]
  isFormValid: boolean
  isEditMode?: boolean
  roleData?: Role
}

export const useRoleSave = ({
  roleName,
  description,
  permissions,
  isFormValid,
  isEditMode,
  roleData
}: UseRoleSaveProps) => {
  const navigate = useNavigate()
  const createRoleMutation = useCreateRole()
  const updateRoleMutation = useUpdateRole()

  const handleBack = () => {
    navigate({ to: '/employee/role' })
  }

  const handleSave = async () => {
    if (!isFormValid) {
      toast.error('Vui lòng điền đầy đủ thông tin bắt buộc')
      return
    }

    try {
      // Separate main access permissions from detailed permissions
      const mainAccessPermissions = permissions.filter(p =>
        ['POS_CMS', 'POS_CLIENT', 'POS_MANAGER'].includes(p)
      )

      // Get all possible detailed permissions
      const allDetailedPermissions = permissions.filter(
        p => !['POS_CMS', 'POS_CLIENT', 'POS_MANAGER'].includes(p)
      )

      // reject_permissions should contain UNCHECKED permissions only
      // Since permissions array contains CHECKED permissions, we need to find the difference
      const { getAllPermissions } = await import('../utils')
      const allPossiblePermissions = getAllPermissions()

      const allPossibleDetailedPermissions = allPossiblePermissions.filter(
        p => !['POS_CMS', 'POS_CLIENT', 'POS_MANAGER'].includes(p)
      )

      const rejectedPermissions = allPossibleDetailedPermissions.filter(
        p => !allDetailedPermissions.includes(p)
      )

      // Get company ID from localStorage
      const posCompanyData = localStorage.getItem('pos_company_data')
      const companyId = posCompanyData ? JSON.parse(posCompanyData).id : null

      if (isEditMode && roleData) {
        // Update existing role
        const updateData: UpdateRoleRequest = {
          allow_access: mainAccessPermissions,
          role_name: roleName,
          role_id: roleData.role_id,
          description: description,
          reject_permissions: rejectedPermissions,
          id: roleData.id,
          scope: roleData.scope || 'COMPANY',
          scope_value: roleData.scope_value || companyId
        }

        await updateRoleMutation.mutateAsync(updateData)
        toast.success('Cập nhật chức vụ thành công')
      } else {
        // Create new role
        const generateRoleId = (name: string) => {
          const randomString = Math.random().toString(36).substring(2, 6).toUpperCase()
          return `${name}_ROLE-${randomString}`
        }

        const createData: CreateRoleRequest = {
          allow_access: mainAccessPermissions,
          role_name: roleName,
          role_id: generateRoleId(roleName),
          description: description,
          reject_permissions: rejectedPermissions,
          scope: 'COMPANY',
          scope_value: companyId
        }

        await createRoleMutation.mutateAsync(createData)
        toast.success('Tạo chức vụ thành công')
      }

      navigate({ to: '/employee/role' })
    } catch {
      toast.error(isEditMode ? 'Có lỗi xảy ra khi cập nhật chức vụ' : 'Có lỗi xảy ra khi tạo chức vụ')
    }
  }

  return {
    handleBack,
    handleSave,
    createRoleMutation,
    updateRoleMutation,
    isLoading: createRoleMutation.isPending || updateRoleMutation.isPending
  }
}
