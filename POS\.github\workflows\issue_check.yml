name: Daily Issue Check

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

jobs:
  check_issues:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up GitHub CLI
        run: |
          sudo apt-get update
          sudo apt-get install -y gh

      - name: Set up jq
        run: sudo apt-get install -y jq
      
      - name: Run issue check script
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        run: |
          chmod +x ./scripts/gh-check.sh
          ./scripts/gh-check.sh 