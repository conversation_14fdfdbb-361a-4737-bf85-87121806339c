import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { useCreateOrderSource } from '@/hooks/api/use-order-sources'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { StoreSelectionModal } from './store-selection-modal'

export function CreateOrderSourceForm() {
  const navigate = useNavigate()
  const { createOrderSource, isCreating } = useCreateOrderSource()

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    autoGenerateCode: true,
    selectedStores: [] as string[]
  })

  const [showStoreModal, setShowStoreModal] = useState(false)

  const handleBack = () => {
    navigate({ to: '/setting/source' })
  }

  const isFormValid = formData.name.trim() !== '' && formData.selectedStores.length > 0

  const handleSave = async () => {
    if (!isFormValid) return

    const createData = {
      source_name: formData.name,
      source_id: formData.autoGenerateCode ? undefined : formData.code,
      stores: formData.selectedStores
    }

    createOrderSource(createData, {
      onSuccess: () => {
        navigate({ to: '/setting/source' })
      }
    })
  }

  const handleStoreSelection = () => {
    setShowStoreModal(true)
  }

  const handleStoreSelectionChange = (storeIds: string[]) => {
    setFormData({ ...formData, selectedStores: storeIds })
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header */}
      <div className='border-b bg-white px-6 py-4'>
        <div className='mx-auto flex max-w-4xl items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='p-2'>
            <X className='h-4 w-4' />
          </Button>
          <div className='flex-1 text-center'>
            <h1 className='text-xl font-semibold text-gray-900'>Tạo nguồn mới</h1>
          </div>
          <div>
            <Button onClick={handleSave} disabled={!isFormValid || isCreating}>
              {isCreating ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className='mx-auto max-w-4xl'>
        <div className='space-y-6'>
          {/* Section Title */}
          <div>
            <h2 className='text-lg font-medium text-gray-900'>Thông tin chi tiết</h2>
          </div>

          {/* Form Fields */}
          <div className='space-y-6'>
            {/* Tên nguồn */}
            <div className='flex items-center gap-4'>
              <Label htmlFor='source-name' className='min-w-[200px] text-sm font-medium'>
                Tên nguồn *
              </Label>
              <Input
                id='source-name'
                value={formData.name}
                onChange={e => setFormData({ ...formData, name: e.target.value })}
                placeholder='Nhập tên nguồn đơn hàng'
                className='flex-1'
              />
            </div>

            {/* Cửa hàng áp dụng */}
            <div className='flex items-center gap-4'>
              <Label className='min-w-[200px] text-sm font-medium'>Cửa hàng áp dụng *</Label>
              <Button
                type='button'
                variant='outline'
                onClick={handleStoreSelection}
                className='flex-1 justify-start'
              >
                {formData.selectedStores.length > 0
                  ? `${formData.selectedStores.length} điểm`
                  : '0 điểm'}
              </Button>
            </div>

            {/* Mã nguồn */}
            <div className='flex items-center gap-4'>
              <Label htmlFor='source-code' className='min-w-[200px] text-sm font-medium'>
                Mã nguồn
              </Label>
              <div className='flex flex-1 items-center gap-3'>
                <Input
                  id='source-code'
                  value={formData.code}
                  onChange={e => setFormData({ ...formData, code: e.target.value })}
                  placeholder='Nếu để trống, hệ thống sẽ tự động tạo một mã nguồn'
                  disabled={formData.autoGenerateCode}
                  className='flex-1'
                />
                <Checkbox
                  id='auto-generate-code'
                  checked={formData.autoGenerateCode}
                  onCheckedChange={checked =>
                    setFormData({
                      ...formData,
                      autoGenerateCode: checked as boolean,
                      code: checked ? '' : formData.code
                    })
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Store Selection Modal */}
      <StoreSelectionModal
        open={showStoreModal}
        onOpenChange={setShowStoreModal}
        selectedStoreIds={formData.selectedStores}
        onStoreSelectionChange={handleStoreSelectionChange}
      />
    </div>
  )
}
