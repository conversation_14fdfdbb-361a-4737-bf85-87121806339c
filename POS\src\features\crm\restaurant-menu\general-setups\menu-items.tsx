import { useNavigate, useLocation, useSearch } from '@tanstack/react-router'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'

import { MenuItemsDataTable, ActionBar } from './components'
import { DanhSachNhomFilters } from './item-type'
import { DanhSachMonFilters } from './items'
import { DanhSachComboFilters } from './combo'
import { DanhSachComboTuyChinhFilters } from './combo-special'
import { useMenuItems } from './hooks'

import type { MenuItemsPageProps, MenuItemsDataType } from './types'

const MenuItemsPage = ({ className }: MenuItemsPageProps) => {
  const navigate = useNavigate()
  const location = useLocation()
  const search = useSearch({ strict: false }) as { pos?: string }

  const getActiveTabFromUrl = (): MenuItemsDataType => {
    const pathname = location.pathname

    if (pathname.includes('/item-type')) return 'item-type'
    if (pathname.includes('/combo-special')) return 'combo-special'
    if (pathname.includes('/combo')) return 'combo'
    if (pathname.includes('/items')) return 'items'
    return 'items'
  }

  const activeTab = getActiveTabFromUrl()

  const posId = search.pos

  const { items, groups, combos, filters, updateFilters, isLoading } = useMenuItems(posId)

  const handleTabChange = (value: string) => {
    const tabValue = value as MenuItemsDataType

    // Preserve pos query parameter when switching tabs
    const searchParams = posId ? { pos: String(posId) } : {}

    switch (tabValue) {
      case 'item-type':
        navigate({ to: '/crm/general-setups/item-type', search: searchParams })
        break
      case 'items':
        navigate({ to: '/crm/general-setups/items', search: searchParams })
        break
      case 'combo':
        navigate({ to: '/crm/general-setups/combo', search: searchParams })
        break
      case 'combo-special':
        navigate({ to: '/crm/general-setups/combo-special', search: searchParams })
        break
    }
  }

  const handleSyncMenu = () => {
  }

  const handleDeleteMenu = () => {
  }

  const getDataForTab = () => {
    switch (activeTab) {
      case 'item-type':
        return groups
      case 'items':
        return items
      case 'combo':
      case 'combo-special':
        return combos
      default:
        return items
    }
  }

  if (isLoading) {
    return (
      <div className={`container mx-auto p-6 ${className}`}>
        <div className='flex items-center justify-center h-64'>
          <div className='text-lg'>Đang tải...</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`container mx-auto p-6 ${className}`}>
      <div className='mb-6'>
        <h1 className='text-2xl font-bold mb-2'>Quản lý thực đơn</h1>
      </div>

      <div className='mb-6'>
        <Tabs value={activeTab} onValueChange={handleTabChange} className='w-full'>
          {/* Desktop Layout */}
          <div className='hidden lg:flex items-start justify-between gap-4'>
            <TabsList className='flex w-auto'>
              <TabsTrigger
                value='item-type'
                className='whitespace-nowrap px-4 py-2 text-sm flex-1 text-center'
              >
                Danh sách nhóm
              </TabsTrigger>
              <TabsTrigger
                value='items'
                className='whitespace-nowrap px-4 py-2 text-sm flex-1 text-center'
              >
                Danh sách món
              </TabsTrigger>
              <TabsTrigger
                value='combo'
                className='whitespace-nowrap px-4 py-2 text-sm flex-1 text-center'
              >
                Danh sách combo
              </TabsTrigger>
              <TabsTrigger
                value='combo-special'
                className='whitespace-nowrap px-4 py-2 text-sm flex-1 text-center'
              >
                Danh sách combo tùy chỉnh
              </TabsTrigger>
            </TabsList>

            <ActionBar
              onSyncMenu={handleSyncMenu}
              onDeleteMenu={handleDeleteMenu}
              variant='desktop'
            />
          </div>

          {/* Mobile/Tablet Layout */}
          <div className='lg:hidden space-y-4'>
            <div className='flex flex-col md:flex-row md:items-start md:justify-between gap-4'>
              <TabsList className='flex flex-wrap md:flex-nowrap w-full md:w-auto'>
                <TabsTrigger
                  value='item-type'
                  className='text-xs sm:text-sm px-2 py-2 flex-1 text-center min-w-0'
                >
                  Danh sách nhóm
                </TabsTrigger>
                <TabsTrigger
                  value='items'
                  className='text-xs sm:text-sm px-2 py-2 flex-1 text-center min-w-0'
                >
                  Danh sách món
                </TabsTrigger>
                <TabsTrigger
                  value='combo'
                  className='text-xs sm:text-sm px-2 py-2 flex-1 text-center min-w-0'
                >
                  Danh sách combo
                </TabsTrigger>
                <TabsTrigger
                  value='combo-special'
                  className='text-xs sm:text-sm px-2 py-2 flex-1 text-center min-w-0'
                >
                  Combo tùy chỉnh
                </TabsTrigger>
              </TabsList>

              <ActionBar
                onSyncMenu={handleSyncMenu}
                onDeleteMenu={handleDeleteMenu}
                variant='mobile'
              />
            </div>
          </div>

          {/* Tab Contents */}
          <TabsContent value='item-type' className='mt-6'>
            <DanhSachNhomFilters
              filters={filters}
              onFiltersChange={updateFilters}
            />
            <MenuItemsDataTable
              data={getDataForTab()}
              type='item-type'
            />
          </TabsContent>

          <TabsContent value='items' className='mt-6'>
            <DanhSachMonFilters
              filters={filters}
              onFiltersChange={updateFilters}
            />
            <MenuItemsDataTable
              data={getDataForTab()}
              type='items'
            />
          </TabsContent>

          <TabsContent value='combo' className='mt-6'>
            <DanhSachComboFilters
              filters={filters}
              onFiltersChange={updateFilters}
            />
            <MenuItemsDataTable
              data={getDataForTab()}
              type='combo'
            />
          </TabsContent>

          <TabsContent value='combo-special' className='mt-6'>
            <DanhSachComboTuyChinhFilters
              filters={filters}
              onFiltersChange={updateFilters}
            />
            <MenuItemsDataTable
              data={getDataForTab()}
              type='combo-special'
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default MenuItemsPage
