import type {
  MenuItem,
  UpdateItemSchedulePayload,
  ItemScheduleData
} from '../types/menu-schedule-api'

export const convertMenuItemToUpdatePayload = (
  menuItem: MenuItem,
  scheduleData: {
    company_uid: string
    brand_uid: string
    store_uid: string
    city_uid: string
    time: number
    end_time?: number | null
    schedule_id?: string
  },
  userEmail: string
): UpdateItemSchedulePayload => {
  const now = Date.now()

  const originalData = menuItem.originalItem
  const changed_data: ItemScheduleData = {
    id: originalData?.id || menuItem.id,
    sort: menuItem.display_order || originalData?.sort || 1000,
    is_fc: originalData?.is_fc || 0,
    point: originalData?.point || 0,
    active: originalData?.active || 1,
    is_kit: originalData?.is_kit || 0,
    is_sub: originalData?.is_sub || 0,
    ta_tax: originalData?.ta_tax || 0,
    deleted: originalData?.deleted || false,
    is_fabi: originalData?.is_fabi || 1,
    is_gift: originalData?.is_gift || 0,
    item_id: menuItem.code,
    ots_tax: menuItem.vat_rate || originalData?.ots_tax || 0,
    user_id: originalData?.user_id || '',
    city_uid: scheduleData.city_uid,
    revision: originalData?.revision || now,
    ta_price: menuItem.ots_price || originalData?.ta_price || 0,
    unit_uid: menuItem.unit_uid || originalData?.unit_uid || '',
    brand_uid: scheduleData.brand_uid,
    is_parent: originalData?.is_parent || 0,
    item_name: menuItem.name,
    ots_price: menuItem.ots_price || originalData?.ots_price || 0,
    store_uid: scheduleData.store_uid,
    cost_price: originalData?.cost_price || 0,
    created_at: originalData?.created_at || now,
    created_by: originalData?.created_by || userEmail,
    deleted_at: originalData?.deleted_at || null,
    deleted_by: originalData?.deleted_by || null,
    extra_data: {
      is_buffet_item: originalData?.extra_data?.is_buffet_item || 0,
      up_size_buffet: originalData?.extra_data?.up_size_buffet || [],
      is_item_service: originalData?.extra_data?.is_item_service || 0,
      is_virtual_item: originalData?.extra_data?.is_virtual_item || 0,
      price_by_source: originalData?.extra_data?.price_by_source || [],
      enable_edit_price: originalData?.extra_data?.enable_edit_price || 0,
      exclude_items_buffet: originalData?.extra_data?.exclude_items_buffet || [],
      no_update_quantity_toping: originalData?.extra_data?.no_update_quantity_toping || 0
    },
    image_path: originalData?.image_path || '',
    is_foreign: originalData?.is_foreign || 0,
    is_service: menuItem.is_service ? 1 : 0,
    item_color: originalData?.item_color || '',
    list_order: originalData?.list_order || 0,
    source_uid: originalData?.source_uid || null,
    updated_at: originalData?.updated_at || now,
    updated_by: userEmail,
    company_uid: scheduleData.company_uid,
    description: originalData?.description || '',
    expire_date: originalData?.expire_date || 0,
    is_eat_with: originalData?.is_eat_with || 0,
    is_material: originalData?.is_material || 0,
    show_on_web: originalData?.show_on_web || 0,
    price_change: originalData?.price_change || 0,
    time_cooking: menuItem.cooking_time || originalData?.time_cooking || 0,
    item_type_uid: menuItem.item_type_uid || originalData?.item_type_uid || '',
    process_index: originalData?.process_index || 0,
    effective_date: originalData?.effective_date || 0,
    is_print_label: originalData?.is_print_label || 0,
    item_class_uid: menuItem.item_class_uid || originalData?.item_class_uid || null,
    quantity_limit: originalData?.quantity_limit || 0,
    allow_take_away: originalData?.allow_take_away || 1,
    item_id_barcode: originalData?.item_id_barcode || '',
    item_id_mapping: originalData?.item_id_mapping || '',
    apply_with_store: originalData?.apply_with_store || 1,
    currency_type_id: originalData?.currency_type_id || '',
    image_path_thumb: originalData?.image_path_thumb || null,
    item_id_eat_with: originalData?.item_id_eat_with || '',
    quantity_default: originalData?.quantity_default || 0,
    quantity_per_day: originalData?.quantity_per_day || 0,
    customization_uid: menuItem.customization_uid || originalData?.customization_uid || null,
    is_allow_discount: originalData?.is_allow_discount || 0,
    show_price_on_web: originalData?.show_price_on_web || 0,
    time_sale_hour_day: menuItem.time_sale_hour_day || originalData?.time_sale_hour_day || 0,
    unit_secondary_uid: menuItem.unit_secondary_uid || originalData?.unit_secondary_uid || null,
    time_sale_date_week: menuItem.time_sale_date_week || originalData?.time_sale_date_week || 0
  }

  return {
    company_uid: scheduleData.company_uid,
    brand_uid: scheduleData.brand_uid,
    type: 'item',
    id: scheduleData.schedule_id || menuItem.id,
    item_id: menuItem.code,
    item_old_uid: null,
    item_new_uid: null,
    action: menuItem.action,
    partner: '',
    time: scheduleData.time,
    end_time: scheduleData.end_time || null,
    user_info: {},
    changed_data,
    data_old: originalData
      ? {
          ...originalData,
          image_path_thumb: originalData.image_path_thumb || '',
          extra_data: {
            ...originalData.extra_data,
            up_size_buffet: originalData.extra_data?.up_size_buffet || [],
            price_by_source: originalData.extra_data?.price_by_source || [],
            exclude_items_buffet: originalData.extra_data?.exclude_items_buffet || []
          }
        }
      : {},
    error: '',
    status: 'PENDING',
    store_uid: scheduleData.store_uid,
    city_uid: scheduleData.city_uid,
    created_by: originalData?.created_by || userEmail,
    updated_by: userEmail,
    deleted_by: originalData?.deleted_by || null,
    created_at: originalData?.created_at || now,
    updated_at: originalData?.updated_at || now,
    deleted_at: originalData?.deleted_at || null,
    deleted: originalData?.deleted || false,
    item_uid: originalData?.id || menuItem.id
  }
}
