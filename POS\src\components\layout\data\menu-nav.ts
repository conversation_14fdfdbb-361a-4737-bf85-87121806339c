import { IconPackages } from '@tabler/icons-react'
import { type NavItem } from '../types'

export const menuNavItems: NavItem[] = [
  {
    title: 'Thực Đơn',
    icon: IconPackages,
    items: [
      {
        title: 'Món ăn',
        items: [
          {
            title: 'Món ăn tại thành phố',
            url: '/menu/items/items-in-city',
          },
          {
            title: 'Món ăn tại cửa hàng',
            url: '/menu/items/items-in-store',
          },
        ],
      },
      {
        title: 'Nhóm món',
        items: [
          {
            title: 'Nhóm món toàn thương hiệu',
            url: '/menu/categories/categories-in-brand',
          },
          {
            title: 'Nhóm món tại cửa hàng',
            url: '/menu/categories/categories-in-store',
          },
        ],
      },
      {
        title: 'Loại món',
        url: '/menu/item-class',
      },
      {
        title: 'Customization',
        items: [
          {
            title: 'Customization tại thành phố',
            url: '/menu/customization/customization-in-city',
          },
          {
            title: 'Customization tại cửa hàng',
            url: '/menu/customization/customization-in-store',
          },
        ],
      },
      {
        title: 'Lập lịch thực đơn',
        url: '/menu/schedule',
      },
      {
        title: 'Món đã xoá',
        items: [
          {
            title: 'Món đã xoá tại thành phố',
            url: '/menu/item-removed/item-removed-in-city',
          },
          {
            title: 'Món đã xoá tại cửa hàng',
            url: '/menu/item-removed/item-removed-in-store',
          },
        ],
      },
      {
        title: 'Khai báo số lượng món',
        url: '/menu/quantity-day',
      },
    ],
  },
]
