import { Button, Input, Label, Checkbox } from '@/components/ui'

interface PointAccumulationSectionProps {
  pointRate: string
  setPointRate: (value: string) => void
  isNoChange: boolean
  setIsNoChange: (value: boolean) => void
}

export function PointAccumulationSection({
  pointRate,
  setPointRate,
  isNoChange,
  setIsNoChange
}: PointAccumulationSectionProps) {
  return (
    <div className='space-y-4'>
      <h3 className='text-lg font-semibold text-gray-800'>TÍCH ĐIỂM</h3>
      <div className='space-y-4'>
        <div className='space-y-2'>
          <Label htmlFor='point-rate' className='text-sm font-medium'>
            Tích điểm <span className='text-red-500'>*</span>
          </Label>
          <div className='flex items-center gap-3'>
            <Input
              id='point-rate'
              type='number'
              value={pointRate}
              onChange={e => {
                const value = e.target.value
                if (value === '' || /^\d*\.?\d*$/.test(value)) {
                  setPointRate(value)
                }
              }}
              placeholder='0.01'
              className='w-32'
              min='0'
              step='0.01'
            />
            <span className='rounded bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>% giá trị hóa đơn</span>
            <Button
              variant='outline'
              size='sm'
              className='border-orange-200 bg-orange-50 text-orange-600 hover:bg-orange-100'
            >
              Tạo voucher đổi điểm
            </Button>
          </div>
          <div className='text-sm'>
            <a href='#' className='text-blue-600 hover:underline'>
              Cài đặt làm tròn điểm tích lũy tại đây
            </a>
          </div>
        </div>

        <div className='space-y-3'>
          <div className='flex items-center space-x-2'>
            <Checkbox
              id='no-change'
              checked={isNoChange}
              onCheckedChange={checked => setIsNoChange(checked === true)}
            />
            <Label htmlFor='no-change' className='cursor-pointer text-sm font-medium'>
              Hạng không đổi
            </Label>
          </div>
          <div className='rounded bg-gray-50 p-3 text-sm text-gray-600'>
            Hạng không đổi sẽ không ảnh hưởng bởi các điều kiện hạng thành viên thông thường, không xét lại theo chu kỳ
          </div>
        </div>
      </div>
    </div>
  )
}
