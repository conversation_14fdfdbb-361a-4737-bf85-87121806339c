import { Search } from 'lucide-react'

import { Button, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

import { DateRangePicker } from '@/features/devices/detail/components/date-range-picker'

import type { SystemLogFilters } from '../data'

interface SystemLogFiltersProps {
  filters: SystemLogFilters
  onFilterChange: (key: keyof SystemLogFilters, value: any) => void
  onDateRangeChange: (from: Date | null, to: Date | null) => void
  onSearch: () => void
  availableRequestPaths: Array<{ value: string; label: string }>
  availableUsers: Array<{ value: string; label: string }>
  isLoading: boolean
}

export function SystemLogFilters({
  filters,
  onFilterChange,
  onDateRangeChange,
  onSearch,
  availableRequestPaths,
  availableUsers,
  isLoading
}: SystemLogFiltersProps) {
  const handleStartDateChange = (date: Date | undefined) => {
    onDateRangeChange(date || null, filters.dateRange.to)
  }

  const handleEndDateChange = (date: Date | undefined) => {
    onDateRangeChange(filters.dateRange.from, date || null)
  }

  return (
    <div className='space-y-4 rounded-lg border bg-white p-4'>
      {/* <div className='grid grid-cols-1 gap-4 md:grid-cols-4'> */}
      <div className='flex items-center justify-start gap-3'>
        {/* Date Range */}
        <div className='space-y-2'>
          <label className='text-sm font-medium'>Thời gian</label>
          <DateRangePicker
            startDate={filters.dateRange.from || undefined}
            endDate={filters.dateRange.to || undefined}
            onStartDateChange={handleStartDateChange}
            onEndDateChange={handleEndDateChange}
            placeholder='Chọn khoảng thời gian'
            disabled={isLoading}
          />
        </div>

        {/* Request Path Filter */}
        <div className='space-y-2'>
          <label className='text-sm font-medium'>Thao tác</label>
          <Select value={filters.request_path} onValueChange={value => onFilterChange('request_path', value)}>
            <SelectTrigger>
              <SelectValue placeholder='Chọn đường dẫn API' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả</SelectItem>
              {availableRequestPaths.map(path => (
                <SelectItem key={path.value} value={path.value}>
                  {path.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* User Filter */}
        <div className='space-y-2'>
          <label className='text-sm font-medium'>User</label>
          <Select value={filters.user_name} onValueChange={value => onFilterChange('user_name', value)}>
            <SelectTrigger>
              <SelectValue placeholder='Chọn user' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả</SelectItem>
              {availableUsers.map(user => (
                <SelectItem key={user.value} value={user.value}>
                  {user.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Search Button */}
        <div className='space-y-2'>
          <label className='text-sm font-medium'>.</label>
          <Button onClick={onSearch} disabled={isLoading} className='w-full'>
            <Search className='mr-2 h-4 w-4' />
            {isLoading ? 'Đang tìm...' : 'Lọc'}
          </Button>
        </div>
      </div>
    </div>
  )
}
