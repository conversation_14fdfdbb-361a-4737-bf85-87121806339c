import { useState, useEffect } from 'react'
import type { MenuItem, MenuGroup, MenuCombo, ItemsFilters } from '../types'

// Mock data
const mockMenuItems: MenuItem[] = [
  {
    id: 'ITEM-KDXI',
    code: 'ITEM-KDXI',
    name: 'sdfsd',
    group: 'Uncategory',
    type: '<PERSON>ón thường',
    status: 'available',
    canTakeaway: true,
    canDelivery: true,
    order: 1000,
    lastUpdated: '23-07-2025 16:19:12'
  },
  {
    id: 'ITEM-FF6U',
    code: 'ITEM-FF6U',
    name: 'okok',
    group: 'Uncategory',
    type: '<PERSON><PERSON> thường',
    status: 'available',
    canTakeaway: true,
    canDelivery: true,
    order: 1337,
    lastUpdated: '29-07-2025 10:50:03'
  }
]

const mockMenuGroups: MenuGroup[] = [
  {
    id: '1',
    code: '1234',
    name: 'man<PERSON><PERSON>',
    status: 'available',
    order: 1000,
    maxItems: 0,
    requiredItems: 0,
    lastUpdated: '30-07-2025 00:00:00'
  },
  {
    id: '2',
    code: 'CHADA',
    name: 'chà da da',
    status: 'available',
    order: 1,
    maxItems: 0,
    requiredItems: 0,
    lastUpdated: '08-08-2025 00:00:00'
  },
  {
    id: '3',
    code: 'EATCHICK',
    name: '123anga',
    status: 'available',
    order: 2,
    maxItems: 0,
    requiredItems: 0,
    lastUpdated: '06-08-2025 00:00:00'
  },
  {
    id: '4',
    code: '1',
    name: '131231234',
    status: 'unavailable',
    order: 1,
    maxItems: 0,
    requiredItems: 0,
    lastUpdated: '28-07-2025 00:00:00'
  }
]

const mockMenuCombos: MenuCombo[] = [
  {
    id: '1',
    code: 'COMBO-SPXU',
    name: 'ok',
    image: 'https://via.placeholder.com/48x48/4A5568/FFFFFF?text=IMG',
    type: 'combo',
    items: [],
    price: 50000,
    status: 'available',
    canTakeaway: true,
    canDelivery: true,
    lastUpdated: '12-08-2025 13:35:27'
  },
  {
    id: '2',
    code: 'BUN',
    name: 'bún bò',
    type: 'combo',
    items: [],
    price: 35000,
    status: 'available',
    canTakeaway: true,
    canDelivery: true,
    lastUpdated: '12-08-2025 08:57:02'
  },
  {
    id: '3',
    code: 'COMBO-SK26',
    name: 'Combo test 1',
    type: 'combo',
    items: [],
    price: 75000,
    status: 'available',
    canTakeaway: true,
    canDelivery: true,
    lastUpdated: '12-08-2025 08:47:23'
  },
  {
    id: '4',
    code: 'COMBO-INQV',
    name: 'Matcha',
    type: 'custom-combo',
    items: [],
    price: 45000,
    status: 'available',
    canTakeaway: true,
    canDelivery: true,
    lastUpdated: '12-08-2025 11:02:21'
  }
]

export function useMenuItems(posId: string | null | undefined) {
  const [items, setItems] = useState<MenuItem[]>([])
  const [groups, setGroups] = useState<MenuGroup[]>([])
  const [combos, setCombos] = useState<MenuCombo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filters, setFilters] = useState<ItemsFilters>({
    search: '',
    group: '',
    type: '',
    status: '',
    canTakeaway: '',
    canDelivery: '',
    order: ''
  })

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        setItems(mockMenuItems)
        setGroups(mockMenuGroups)
        setCombos(mockMenuCombos)
      } catch (error) {
        console.error('Error fetching menu items:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [posId])

  const updateFilters = (newFilters: Partial<ItemsFilters>) => {
    setFilters((prev: ItemsFilters) => ({ ...prev, ...newFilters }))
  }

  return {
    items,
    groups,
    combos,
    filters,
    updateFilters,
    isLoading
  }
}
