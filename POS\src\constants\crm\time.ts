export const TIME_BOUNDARIES = {
  START_OF_DAY: { hours: 0, minutes: 0, seconds: 0, ms: 0 },
  END_OF_DAY: { hours: 23, minutes: 59, seconds: 59, ms: 999 }
} as const

export const DEFAULT_DATE_RANGE = {
  FROM: new Date(2025, 6, 29), // 29/07/2025
  TO: new Date(2025, 7, 13) // 13/08/2025
} as const

export const setTimeToStartOfDay = (date: Date): Date => {
  const newDate = new Date(date)
  const { hours, minutes, seconds, ms } = TIME_BOUNDARIES.START_OF_DAY
  newDate.setHours(hours, minutes, seconds, ms)
  return newDate
}

export const setTimeToEndOfDay = (date: Date): Date => {
  const newDate = new Date(date)
  const { hours, minutes, seconds, ms } = TIME_BOUNDARIES.END_OF_DAY
  newDate.setHours(hours, minutes, seconds, ms)
  return newDate
}
