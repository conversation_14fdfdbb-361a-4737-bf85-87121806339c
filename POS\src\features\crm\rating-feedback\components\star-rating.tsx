import { Star } from 'lucide-react'

interface StarRatingProps {
  rating: number
  maxRating?: number
  size?: 'sm' | 'md' | 'lg'
  showValue?: boolean
  className?: string
}

export function StarRating({ 
  rating, 
  maxRating = 5, 
  size = 'md', 
  showValue = false,
  className = '' 
}: StarRatingProps) {
  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4', 
    lg: 'h-5 w-5'
  }

  const stars = Array.from({ length: maxRating }, (_, index) => {
    const starNumber = index + 1
    const isFilled = starNumber <= rating
    
    return (
      <Star
        key={index}
        className={`${sizeClasses[size]} ${
          isFilled 
            ? 'fill-yellow-400 text-yellow-400' 
            : 'fill-gray-200 text-gray-200'
        }`}
      />
    )
  })

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {stars}
      {showValue && (
        <span className="text-sm text-gray-600 ml-1">
          ({rating})
        </span>
      )}
    </div>
  )
}
