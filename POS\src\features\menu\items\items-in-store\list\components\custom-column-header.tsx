import { ArrowDownIcon, ArrowUpIcon, CaretSortIcon } from '@radix-ui/react-icons'

import { Column } from '@tanstack/react-table'

import { cn } from '@/lib/utils'

import { Button } from '@/components/ui/button'

interface CustomColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>
  title: string
  defaultSort?: 'asc' | 'desc'
}

export function CustomColumnHeader<TData, TValue>({
  column,
  title,
  className,
  defaultSort = 'desc'
}: CustomColumnHeaderProps<TData, TValue>) {
  if (!column.getCanSort()) {
    return <div className={cn(className)}>{title}</div>
  }

  const handleSort = () => {
    const currentSort = column.getIsSorted()

    if (!currentSort) {
      column.toggleSorting(defaultSort === 'desc')
    } else if (currentSort === 'desc') {
      column.toggleSorting(false)
    } else {
      column.toggleSorting(true)
    }
  }

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <Button variant='ghost' size='sm' className='hover:bg-accent -ml-3 h-8' onClick={handleSort}>
        <span>{title}</span>
        {column.getIsSorted() === 'desc' ? (
          <ArrowDownIcon className='ml-2 h-4 w-4' />
        ) : column.getIsSorted() === 'asc' ? (
          <ArrowUpIcon className='ml-2 h-4 w-4' />
        ) : (
          <CaretSortIcon className='ml-2 h-4 w-4' />
        )}
      </Button>
    </div>
  )
}
