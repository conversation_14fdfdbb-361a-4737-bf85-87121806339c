import { X } from 'lucide-react'

import { ConfirmModal } from '@/components/pos'
import { Button } from '@/components/ui'

import { useDeviceForm } from '../../hooks/use-device-form'
import { PrinterModal, OrderLogModal, CancelDeviceModal, GroupSelectionModal } from '../modals'
import { DeviceBasicInfo, DeviceConfiguration } from './'

function DeviceForm({ id, storeUid }: { id?: string; storeUid?: string }) {
  const {
    // Form data
    formData,
    setFormData,
    allowEditIpServer,
    setAllowEditIpServer,

    // Modal states
    showDeleteModal,
    setShowDeleteModal,
    showPrinterModal,
    setShowPrinterModal,
    showDeletePrinterModal,
    setShowDeletePrinterModal,
    printerToDelete,

    printerToEdit,
    setPrinterToEdit,
    isPrinterEditMode,
    setIsPrinterEditMode,
    showOrderLogModal,
    setShowOrderLogModal,
    showGroupSelectionModal,
    setShowGroupSelectionModal,

    // Groups/combos
    selectedGroups,
    setSelectedGroups,
    selectedCombos,
    setSelectedCombos,

    // Dropdown states
    openDeviceType,
    setOpenDeviceType,

    openDeviceTypeLocal,
    setOpenDeviceTypeLocal,

    openKdsNotification,
    setOpenKdsNotification,

    // Data
    deviceData,
    printersData,

    // Loading states
    isLoadingDevice,
    isUpdating,
    isCreating,
    isDeletingPrinter,

    // Actions
    handleSave,
    handleDeletePrinter,
    confirmDeletePrinter,
    handleBack,
    handleGroupSelectionConfirm,

    // Computed
    isEditMode
  } = useDeviceForm(id, storeUid)

  const handleSavePrinter = () => {
    // Printer save logic handled in PrinterModal
  }

  if (isLoadingDevice) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='mb-8'>
          <div className='mb-4 flex items-center justify-between'>
            <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
              <X className='h-4 w-4' />
              <span className='ml-2'>Quay lại</span>
            </Button>
          </div>
        </div>
        <div className='flex items-center justify-center py-8'>
          <div className='text-center'>
            <div className='text-lg font-medium text-gray-900'>Đang tải...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <h2 className='text-3xl font-medium text-gray-900'>
            {isEditMode ? 'Chi tiết thiết bị' : 'Tạo thiết bị mới'}
          </h2>

          <div className='flex items-center space-x-4'>
            <Button variant='default' type='button' onClick={handleSave} disabled={isUpdating || isCreating}>
              {isUpdating || isCreating ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </div>

      <div className='mx-auto max-w-4xl'>
        <form onSubmit={e => e.preventDefault()} className='space-y-8'>
          <DeviceBasicInfo
            formData={formData}
            setFormData={setFormData}
            isEditMode={isEditMode}
            openDeviceType={openDeviceType}
            setOpenDeviceType={setOpenDeviceType}
          />

          <DeviceConfiguration
            formData={formData}
            setFormData={setFormData}
            allowEditIpServer={allowEditIpServer}
            setAllowEditIpServer={setAllowEditIpServer}
            deviceData={deviceData}
            isEditMode={isEditMode}
            openDeviceTypeLocal={openDeviceTypeLocal}
            setOpenDeviceTypeLocal={setOpenDeviceTypeLocal}
            openKdsNotification={openKdsNotification}
            setOpenKdsNotification={setOpenKdsNotification}
            selectedGroups={selectedGroups}
            selectedCombos={selectedCombos}
            onOpenGroupSelection={() => setShowGroupSelectionModal(true)}
          />

          {/* Printer List Section */}
          {isEditMode && (
            <div className='space-y-6'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-semibold text-gray-900'>Danh sách máy in</h3>
                <Button
                  type='button'
                  variant='default'
                  onClick={() => setShowPrinterModal(true)}
                  className='flex items-center'
                >
                  <span>Tạo máy in mới</span>
                </Button>
              </div>

              <div className='rounded-lg border border-gray-200 bg-white'>
                <div className='grid grid-cols-4 gap-4 border-b border-gray-200 bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700'>
                  <div>#</div>
                  <div>Tên máy in</div>
                  <div>Kiểu kết nối</div>
                  <div>Loại máy in</div>
                </div>
                <div className='divide-y divide-gray-200'>
                  {printersData && printersData.data && printersData.data.length > 0 ? (
                    printersData.data.map((printer: any, index: number) => (
                      <div
                        key={printer.id}
                        className='grid cursor-pointer grid-cols-4 gap-4 px-4 py-3 text-sm hover:bg-gray-50'
                        onClick={() => {
                          setPrinterToEdit(printer)
                          setIsPrinterEditMode(true)
                          setShowPrinterModal(true)
                        }}
                      >
                        <div className='text-gray-700'>{index + 1}</div>
                        <div className='text-gray-700'>{printer.printer_name}</div>
                        <div className='text-gray-700'>
                          {printer.type_printer == 'Wifi' ? 'LAN' : printer.type_printer.toUpperCase()}
                        </div>
                        <div className='flex items-center justify-between'>
                          <span className='text-gray-700'>
                            {printer.type === 'PRINT_HS'
                              ? printer.print_order === true
                                ? 'In order'
                                : 'In hoá đơn'
                              : printer.type === 'PRINT_LABLE'
                                ? 'In tem'
                                : printer.type}
                          </span>
                          <Button
                            variant='ghost'
                            size='sm'
                            className='h-6 w-6 p-0 text-gray-400 hover:text-red-600'
                            onClick={e => {
                              e.stopPropagation()
                              handleDeletePrinter(printer)
                            }}
                            disabled={isDeletingPrinter}
                          >
                            ✕
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className='px-4 py-8 text-center text-sm text-gray-500'>Chưa có máy in nào được cấu hình</div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Order Log and Cancel Device sections */}
          {isEditMode && (
            <>
              <div className='mt-8'>
                <h3 className='mb-4 text-lg font-semibold text-gray-900'>Nhật ký order của thiết bị</h3>
                <div className='w-full text-center'>
                  <Button type='button' variant='outline' onClick={() => setShowOrderLogModal(true)}>
                    Xem nhật ký order
                  </Button>
                </div>
              </div>

              <div className='mt-8'>
                <h3 className='mb-2 text-lg font-semibold text-gray-900'>Hủy thiết bị</h3>
                <p className='mb-4 text-sm text-gray-600'>Thiết bị đã hủy sẽ không thể sử dụng lại.</p>
                <Button type='button' variant='destructive' onClick={() => setShowDeleteModal(true)}>
                  Hủy thiết bị
                </Button>
              </div>
            </>
          )}
        </form>
      </div>

      {/* Modals */}
      <CancelDeviceModal
        open={showDeleteModal}
        onOpenChange={setShowDeleteModal}
        storeName={deviceData?.storeName}
        storeId={(deviceData as any)?.store_uid || deviceData?.storeId}
      />

      <ConfirmModal
        open={showDeletePrinterModal}
        onOpenChange={setShowDeletePrinterModal}
        content={`Bạn có muốn xóa máy in ${printerToDelete?.printer_name || 'In hoá đơn'}?`}
        confirmText='Xóa'
        onConfirm={confirmDeletePrinter}
        isLoading={isDeletingPrinter}
      />

      <PrinterModal
        open={showPrinterModal}
        onOpenChange={setShowPrinterModal}
        onSave={handleSavePrinter}
        deviceCode={(deviceData as any)?.device_code}
        printerData={printerToEdit}
        isEditMode={isPrinterEditMode}
      />

      <OrderLogModal
        open={showOrderLogModal}
        onOpenChange={setShowOrderLogModal}
        deviceCode={deviceData?.device_code}
      />

      <GroupSelectionModal
        open={showGroupSelectionModal}
        onOpenChange={setShowGroupSelectionModal}
        selectedGroups={selectedGroups}
        setSelectedGroups={setSelectedGroups}
        selectedCombos={selectedCombos}
        setSelectedCombos={setSelectedCombos}
        onConfirm={handleGroupSelectionConfirm}
        storeUid={(deviceData as any)?.store_uid || deviceData?.storeId}
        deviceData={deviceData}
      />
    </div>
  )
}

export default DeviceForm
