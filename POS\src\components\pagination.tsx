/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from 'react'

import { ChevronLeftIcon, ChevronRightIcon, DoubleArrowLeftIcon, DoubleArrowRightIcon } from '@radix-ui/react-icons'

import { useNavigate, useSearch } from '@tanstack/react-router'

import { cn } from '@/lib/utils'

import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface PaginationProps {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
  onPageChange?: (page: number) => void
  onPageSizeChange?: (pageSize: number) => void
  showPageSizeSelector?: boolean
  showItemCount?: boolean
  showFirstLastButtons?: boolean
  pageSizeOptions?: number[]
  className?: string
  disabled?: boolean
}

export function Pagination({
  currentPage,
  totalPages,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange,
  showPageSizeSelector = true,
  showItemCount = true,
  showFirstLastButtons = true,
  pageSizeOptions = [10, 20, 50, 100],
  className,
  disabled = false
}: PaginationProps) {
  const navigate = useNavigate()
  const search = useSearch({ strict: false })

  const handlePageChange = React.useCallback(
    (page: number) => {
      if (onPageChange) {
        onPageChange(page)
      } else {
        navigate({
          search: {
            ...search,
            page: page
          } as any
        })
      }
    },
    [onPageChange, navigate, search]
  )

  const handlePageSizeChange = React.useCallback(
    (newPageSize: number) => {
      if (onPageSizeChange) {
        onPageSizeChange(newPageSize)
      } else {
        navigate({
          search: {
            ...search,
            page: 1,
            pageSize: newPageSize
          } as any
        })
      }
    },
    [onPageSizeChange, navigate, search]
  )

  const canPreviousPage = currentPage > 1
  const canNextPage = currentPage < totalPages

  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1
  const endItem = Math.min(currentPage * pageSize, totalItems)

  return (
    <div
      className={cn('flex items-center justify-between overflow-clip px-2 py-2', className)}
      style={{ overflowClipMargin: 1 }}
    >
      {showItemCount && (
        <div className='text-muted-foreground hidden flex-1 text-sm sm:block'>
          Hiển thị {startItem} - {endItem} / {totalItems}
        </div>
      )}

      <div className='flex items-center sm:space-x-6 lg:space-x-8'>
        {showPageSizeSelector && (
          <div className='flex items-center space-x-2'>
            <p className='hidden text-sm font-medium sm:block'>Số hàng mỗi trang</p>
            <Select
              value={`${pageSize}`}
              onValueChange={value => handlePageSizeChange(Number(value))}
              disabled={disabled}
            >
              <SelectTrigger className='h-8 w-[70px]'>
                <SelectValue placeholder={pageSize} />
              </SelectTrigger>
              <SelectContent side='top'>
                {pageSizeOptions.map(size => (
                  <SelectItem key={size} value={`${size}`}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div className='flex w-[100px] items-center justify-center text-sm font-medium'>
          Trang {currentPage} / {totalPages}
        </div>

        <div className='flex items-center space-x-2'>
          {showFirstLastButtons && (
            <Button
              variant='outline'
              className='hidden h-8 w-8 p-0 lg:flex'
              onClick={() => handlePageChange(1)}
              disabled={!canPreviousPage || disabled}
            >
              <span className='sr-only'>Đi đến trang đầu</span>
              <DoubleArrowLeftIcon className='h-4 w-4' />
            </Button>
          )}

          <Button
            variant='outline'
            className='h-8 w-8 p-0'
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={!canPreviousPage || disabled}
          >
            <span className='sr-only'>Trang trước</span>
            <ChevronLeftIcon className='h-4 w-4' />
          </Button>

          <Button
            variant='outline'
            className='h-8 w-8 p-0'
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={!canNextPage || disabled}
          >
            <span className='sr-only'>Trang sau</span>
            <ChevronRightIcon className='h-4 w-4' />
          </Button>

          {showFirstLastButtons && (
            <Button
              variant='outline'
              className='hidden h-8 w-8 p-0 lg:flex'
              onClick={() => handlePageChange(totalPages)}
              disabled={!canNextPage || disabled}
            >
              <span className='sr-only'>Đi đến trang cuối</span>
              <DoubleArrowRightIcon className='h-4 w-4' />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export function usePagination() {
  const search = useSearch({ strict: false }) as any
  const navigate = useNavigate()

  const currentPage = React.useMemo(() => {
    const page = search?.page
    return typeof page === 'number' && page > 0 ? page : 1
  }, [search?.page])

  const pageSize = React.useMemo(() => {
    const size = search?.pageSize
    return typeof size === 'number' && size > 0 ? size : 10
  }, [search?.pageSize])

  const setPage = React.useCallback(
    (page: number) => {
      navigate({
        search: {
          ...search,
          page
        } as any
      })
    },
    [navigate, search]
  )

  const setPageSize = React.useCallback(
    (newPageSize: number) => {
      navigate({
        search: {
          ...search,
          page: 1,
          pageSize: newPageSize
        } as any
      })
    },
    [navigate, search]
  )

  return {
    currentPage,
    pageSize,
    setPage,
    setPageSize
  }
}
