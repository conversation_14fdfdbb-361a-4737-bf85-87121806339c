import { useCallback } from 'react'

import { IconUpload, IconX } from '@tabler/icons-react'

import { Button } from '@/components/ui'

import { createFileUploader } from '@/utils/file-upload'

interface ImageUploadSectionProps {
  label: string
  value?: string
  onChange: (url: string) => void
  disabled?: boolean
  className?: string
}

const EmptyImagePlaceholder = ({ 
  label, 
  onUpload, 
  disabled 
}: { 
  label: string
  onUpload: () => void
  disabled?: boolean
}) => (
  <div className='flex flex-col items-center justify-center space-y-2'>
    <IconUpload className='h-8 w-8 text-gray-400' />
    <p className='text-sm text-gray-500'>{label}</p>
    <Button
      type='button'
      variant='outline'
      size='sm'
      disabled={disabled}
      onClick={onUpload}
    >
      <IconUpload className='mr-2 h-4 w-4' />
      T<PERSON>i lên
    </Button>
  </div>
)

const ImagePreview = ({ 
  src, 
  alt, 
  onUpload, 
  onRemove, 
  disabled,
  className = 'mx-auto h-32 w-full rounded-lg object-cover'
}: { 
  src: string
  alt: string
  onUpload: () => void
  onRemove: () => void
  disabled?: boolean
  className?: string
}) => (
  <div className='relative'>
    <img
      src={src}
      alt={alt}
      className={className}
    />
    <div className='mt-4 flex justify-center space-x-2'>
      <Button
        type='button'
        variant='outline'
        size='sm'
        disabled={disabled}
        onClick={onUpload}
      >
        <IconUpload className='mr-2 h-4 w-4' />
        Thay đổi
      </Button>
      <Button
        type='button'
        variant='outline'
        size='sm'
        disabled={disabled}
        onClick={onRemove}
      >
        <IconX className='mr-2 h-4 w-4' />
        Xóa
      </Button>
    </div>
  </div>
)

export const ImageUploadSection = ({
  label,
  value,
  onChange,
  disabled = false,
  className
}: ImageUploadSectionProps) => {
  const handleUpload = useCallback(async () => {
    const uploader = createFileUploader({
      onSuccess: (_, url) => onChange(url),
      onError: (error) => console.error('Upload error:', error)
    })
    
    await uploader()
  }, [onChange])

  const handleRemove = useCallback(() => {
    onChange('')
  }, [onChange])

  return (
    <div className={`rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-8 text-center ${className}`}>
      {value ? (
        <ImagePreview
          src={value}
          alt={label}
          onUpload={handleUpload}
          onRemove={handleRemove}
          disabled={disabled}
        />
      ) : (
        <EmptyImagePlaceholder
          label={label}
          onUpload={handleUpload}
          disabled={disabled}
        />
      )}
    </div>
  )
}
