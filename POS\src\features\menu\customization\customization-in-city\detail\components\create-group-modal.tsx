import { IconX } from '@tabler/icons-react'

import { PosModal } from '@/components/pos'
import { Input, Label, Table, TableBody, TableCell, TableHead, TableHeader, TableRow, Button } from '@/components/ui'

import { useFormContext } from '../form-context'
import { AddMenuItemModal } from './add-menu-item-modal'

interface CreateGroupModalProps {
  items: {
    id: string
    item_id: string
    item_name: string
    ots_price: number
  }[]
}

export function CreateGroupModal({ items }: CreateGroupModalProps) {
  const { customizationForm, groupManagement, modalState, handlers, menuItemSelection } = useFormContext()

  const selectedMenuItemsList = menuItemSelection.getSelectedMenuItemsList(items)
  const remainingMenuItemsList = menuItemSelection.getRemainingMenuItemsList(items)

  const { isEditing, groupName, setGroupName, minRequired, setMinRequired, maxAllowed, setMaxAllowed, menuItems } =
    groupManagement

  const { customizationName } = customizationForm
  const handleRemoveItem = (id: string) => groupManagement.setMenuItems(prev => prev.filter(i => i.id !== id))
  return (
    <>
      <PosModal
        title={`${isEditing ? 'Sửa' : 'Tạo'} nhóm cho customization ${customizationName || ''}`}
        open={modalState.createGroupModalOpen}
        onOpenChange={modalState.setCreateGroupModalOpen}
        onCancel={handlers.handleCloseModal}
        onConfirm={() => handlers.handleSaveGroup(items)}
        confirmText='Lưu'
        cancelText='Hủy'
        maxWidth='sm:max-w-2xl'
      >
        <div className='space-y-4'>
          <div>
            <Input placeholder='Tên nhóm' value={groupName} onChange={e => setGroupName(e.target.value)} />
          </div>

          <div className='space-y-4'>
            <div className='flex items-center gap-4'>
              <Label htmlFor='min-required' className='min-w-[80px] text-sm font-medium'>
                Yêu cầu
              </Label>
              <Input
                id='min-required'
                type='number'
                value={minRequired}
                onChange={e => setMinRequired(e.target.value)}
                min='0'
                className='flex-1'
              />
            </div>
            <div className='flex items-center gap-4'>
              <Label htmlFor='max-allowed' className='min-w-[80px] text-sm font-medium'>
                Tối đa
              </Label>
              <Input
                id='max-allowed'
                type='number'
                value={maxAllowed}
                onChange={e => setMaxAllowed(e.target.value)}
                min='0'
                className='flex-1'
              />
            </div>
          </div>

          <div className='space-y-3'>
            <h4 className='text-sm font-medium'>Danh sách món</h4>

            <div
              className='scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 max-h-[50vh] overflow-y-auto rounded-md border'
              style={{ scrollBehavior: 'smooth' }}
            >
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tên</TableHead>
                    <TableHead>Giá</TableHead>
                    <TableHead className='w-10 text-right'></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {menuItems.map(item => (
                    <TableRow key={item.id} className={item.active === 0 ? 'opacity-50 blur-[0.5px] filter' : ''}>
                      <TableCell>{item.name}</TableCell>
                      <TableCell>{item.price.toLocaleString('vi-VN')} đ</TableCell>
                      <TableCell className='text-right'>
                        <Button
                          type='button'
                          variant='ghost'
                          size='icon'
                          aria-label='Xóa món khỏi nhóm'
                          title='Xóa'
                          onClick={() => handleRemoveItem(item.id)}
                          className='text-red-600 hover:text-red-700'
                        >
                          <IconX size={16} />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                  <TableRow
                    className='cursor-pointer hover:bg-gray-50'
                    onClick={() => handlers.handleAddMenuItem(customizationForm.selectedCityId)}
                  >
                    <TableCell colSpan={3} className='py-4 text-center'>
                      <span className='font-medium text-blue-600'>Thêm món</span>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </PosModal>

      <AddMenuItemModal
        open={modalState.addItemModalOpen}
        onOpenChange={modalState.setAddItemModalOpen}
        onCancel={handlers.handleCloseAddItemModal}
        onConfirm={() => handlers.handleConfirmMenuItems(items)}
        selectedMenuItemsList={selectedMenuItemsList}
        remainingMenuItemsList={remainingMenuItemsList}
      />
    </>
  )
}
