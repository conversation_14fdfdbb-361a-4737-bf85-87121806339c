import { UseFormReturn } from 'react-hook-form'

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem
} from '@/components/ui'

import type { MembershipTypeFormData } from './schema'

interface RankAchievementConditionsProps {
  form: UseFormReturn<MembershipTypeFormData>
}

export function RankAchievementConditions({ form }: RankAchievementConditionsProps) {
  return (
    <div className='space-y-6'>
      <h3 className='text-lg font-semibold text-gray-900'>ĐIỀU KIỆN ĐẠT HẠNG</h3>

      <div className='space-y-4'>
        {/* Tiền tích lũy vượt mức */}
        <FormField
          control={form.control}
          name='upgradeAmount'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tiền tích lũy vượt mức *</FormLabel>
              <FormControl>
                <div className='flex items-center space-x-2'>
                  <Input placeholder='0' {...field} />
                  <span className='text-sm text-gray-500'>VNĐ</span>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Xử lý tiền tích lũy */}
        <FormField
          control={form.control}
          name='moneyHandlingType'
          render={({ field }) => (
            <FormItem className='space-y-3'>
              <FormControl>
                <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className='space-y-2'>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='maintain' id='maintain' />
                    <label htmlFor='maintain' className='cursor-pointer text-sm text-gray-700'>
                      Duy trì tiền tích lũy
                    </label>
                  </div>

                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='deduct' id='deduct' />
                    <FormField
                      control={form.control}
                      name='upgradeMinusPointAmount'
                      render={({ field: deductField }) => (
                        <FormItem>
                          <FormControl>
                            <div className='flex items-center'>
                              <div className='rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                                Trừ tiền tích lũy
                              </div>
                              <Input
                                placeholder='0'
                                className='w-24 rounded-none border-r-0 border-l-0'
                                {...deductField}
                              />
                              <div className='rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                                VNĐ
                              </div>
                            </div>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='reset' id='reset' />
                    <FormField
                      control={form.control}
                      name='upgradeResetPointAmount'
                      render={({ field: resetField }) => (
                        <FormItem>
                          <FormControl>
                            <div className='flex items-center'>
                              <div className='rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                                Reset tiền tích lũy về
                              </div>
                              <Input
                                placeholder='0'
                                className='w-24 rounded-none border-r-0 border-l-0'
                                {...resetField}
                              />
                              <div className='rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                                VNĐ
                              </div>
                            </div>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </RadioGroup>
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
