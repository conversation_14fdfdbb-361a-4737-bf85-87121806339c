'use client'

import * as React from 'react'

import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { ConfirmDialog } from '@/components/confirm-dialog'

// removed default DataTablePagination in favor of inline pagination UI

import { useDeleteMultipleItemsInCity } from '../../hooks'
import { ItemsInCityPagination } from './items-in-city-pagination'
import { ItemsInCityTableToolbar } from './items-in-city-table-toolbar'

interface ItemsInCityDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  onCustomizationClick?: (itemsInCity: TData) => void
  onCopyClick?: (itemsInCity: TData) => void
  onToggleStatus?: (itemsInCity: TData) => void
  onRowClick?: (itemsInCity: TData) => void
  onDeleteClick?: (itemsInCity: TData) => void
  customizations?: Array<{ id: string; name: string }>
  selectedItemTypeUid?: string
  onItemTypeChange?: (itemTypeUid: string) => void
  selectedItemClassUid?: string
  onItemClassChange?: (itemClassUid: string) => void
  selectedCityUid?: string
  onCityChange?: (cityUid: string) => void
  selectedDaysOfWeek?: string[]
  onDaysOfWeekChange?: (daysOfWeek: string[]) => void
  selectedStatus?: string
  onStatusChange?: (status: string) => void
  hasNextPageOverride?: boolean
  currentPage?: number
  onPageChange?: (page: number) => void
}

export function ItemsInCityDataTable<TData, TValue>({
  columns,
  data,
  onCustomizationClick,
  onCopyClick,
  onToggleStatus,
  onRowClick,
  onDeleteClick,
  customizations,
  selectedItemTypeUid,
  onItemTypeChange,
  selectedCityUid,
  onCityChange,
  selectedDaysOfWeek,
  onDaysOfWeekChange,
  selectedStatus,
  onStatusChange,
  hasNextPageOverride,
  currentPage,
  onPageChange
}: ItemsInCityDataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = React.useState(false)

  const { deleteMultipleItemsAsync } = useDeleteMultipleItemsInCity()

  const handleDeleteSelected = () => {
    setIsDeleteConfirmOpen(true)
  }

  const handleConfirmDelete = async () => {
    try {
      const selectedRows = table.getFilteredSelectedRowModel().rows
      const itemUids = selectedRows.map(row => (row.original as any).id)

      await deleteMultipleItemsAsync(itemUids)

      setIsDeleteConfirmOpen(false)
      table.resetRowSelection()
    } catch (error) {
      // Error is handled by the mutation
    }
  }

  const handleRowClick = (item: TData, event: React.MouseEvent) => {
    const target = event.target as HTMLElement
    if (
      target.closest('input[type="checkbox"]') ||
      target.closest('button') ||
      target.closest('[role="button"]') ||
      target.closest('.badge') ||
      target.tagName === 'BUTTON'
    ) {
      return
    }

    onRowClick?.(item)
  }

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    // Disable client-side pagination; handle via server and parent state
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    meta: {
      onCustomizationClick,
      onCopyClick,
      onToggleStatus,
      onDeleteClick,
      customizations
    }
  })

  return (
    <div className='space-y-4'>
      <ItemsInCityTableToolbar
        table={table}
        selectedItemTypeUid={selectedItemTypeUid}
        onItemTypeChange={onItemTypeChange}
        selectedCityUid={selectedCityUid}
        onCityChange={onCityChange}
        selectedDaysOfWeek={selectedDaysOfWeek}
        onDaysOfWeekChange={onDaysOfWeekChange}
        selectedStatus={selectedStatus}
        onStatusChange={onStatusChange}
        onDeleteSelected={handleDeleteSelected}
      />
      <ScrollArea className='rounded-md border'>
        <Table className='relative'>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className='hover:bg-muted/50 cursor-pointer'
                  onClick={event => handleRowClick(row.original, event)}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  Không có dữ liệu.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <ScrollBar orientation='horizontal' />
      </ScrollArea>
      <ItemsInCityPagination
        currentPage={currentPage ?? 1}
        onPageChange={page => onPageChange && onPageChange(page)}
        hasNextPage={Boolean(hasNextPageOverride)}
      />

      <ConfirmDialog
        open={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        title={`Bạn có chắc muốn xóa ${table.getFilteredSelectedRowModel().rows.length} món đã chọn`}
        desc='Hành động này không thể hoàn tác.'
        confirmText='Xóa'
        cancelBtnText='Hủy'
        className='top-[30%] translate-y-[-50%]'
        handleConfirm={handleConfirmDelete}
        destructive={true}
      />
    </div>
  )
}
