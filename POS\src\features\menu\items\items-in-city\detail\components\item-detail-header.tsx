import { useNavigate } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { Button } from '@/components/ui/button'

import { ItemInCity } from '../../hooks'

interface ItemDetailHeaderProps {
  isUpdate: boolean
  currentRow?: ItemInCity
  isLoading: boolean
  isCopy: boolean
  onSave: () => void
  onSaveAndSync: () => void
  onDeactive?: () => void
  onActive?: () => void
  isDeactivating?: boolean
  isActivating?: boolean
}

export function ItemDetailHeader({
  isUpdate,
  currentRow,
  isLoading,
  onSave,
  onSaveAndSync,
  onDeactive,
  onActive,
  isDeactivating = false,
  isActivating = false
}: ItemDetailHeaderProps) {
  const navigate = useNavigate()

  const handleClose = () => {
    navigate({ to: '/menu/items/items-in-city' })
  }

  const getTitle = () => {
    if (isUpdate) return 'Chi tiết món'
    return 'Tạo món'
  }

  return (
    <div className='mb-8'>
      <div className='mb-4 flex items-center justify-between'>
        <div>
          <Button variant='ghost' size='sm' onClick={handleClose} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
        </div>
        <h2 className='mb-2 text-3xl font-medium'>{getTitle()}</h2>
        <div className='flex gap-2'>
          {isUpdate && currentRow?.active && onDeactive && (
            <Button
              type='button'
              variant='outline'
              className='border-red-500 text-red-500 hover:bg-red-50'
              disabled={isDeactivating}
              onClick={onDeactive}
            >
              {isDeactivating ? 'Đang deactive...' : 'Deactive'}
            </Button>
          )}
          {isUpdate && !currentRow?.active && onActive && (
            <Button
              type='button'
              variant='outline'
              className='border-green-500 text-green-500 hover:bg-green-50'
              disabled={isActivating}
              onClick={onActive}
            >
              {isActivating ? 'Đang active...' : 'Active'}
            </Button>
          )}

          <Button type='button' disabled={isLoading} onClick={onSaveAndSync}>
            {isLoading ? 'Đang lưu và đồng bộ...' : 'Lưu và đồng bộ'}
          </Button>
          <Button type='button' disabled={isLoading} onClick={onSave}>
            {isLoading ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>
      </div>
    </div>
  )
}
