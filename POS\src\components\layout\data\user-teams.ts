import { useMemo } from 'react'

import { AudioWaveform, Command, GalleryVerticalEnd, Store } from 'lucide-react'

import { usePosStore } from '@/stores/posStore'

import { usePosData } from '@/hooks/use-pos-data'

export const userData = {
  name: 'satnaing',
  email: '<EMAIL>',
  avatar: '/avatars/shadcn.jpg'
}

export const fallbackTeamsData = [
  {
    name: 'POS TTM',
    logo: Command,
    plan: 'Work Hard. Have Fun. Make History'
  },
  {
    name: 'Acme Inc',
    logo: GalleryVerticalEnd,
    plan: 'Enterprise'
  },
  {
    name: 'Acme Corp.',
    logo: AudioWaveform,
    plan: 'Startup'
  }
]

export const useBrandsData = () => {
  const { isAuthenticated, brands, getActiveBrands } = usePosStore()
  const activeBrands = getActiveBrands()

  return useMemo(() => {
    if (!isAuthenticated || brands.length === 0) {
      const fallbackData = fallbackTeamsData.map((team, index) => ({
        id: `fallback-${index}`,
        name: team.name,
        logo: team.logo,
        plan: team.plan,
        brandId: `fallback-${index}`,
        currency: 'VND',
        active: true
      }))
      return fallbackData
    }

    const brandsData = activeBrands.map((brand, index) => ({
      id: brand.id,
      name: brand.brand_name,
      logo: getBrandIcon(index),
      plan: `${brand.currency} • ${brand.brand_id}`,
      brandId: brand.brand_id,
      currency: brand.currency,
      active: brand.active === 1
    }))

    return brandsData
  }, [brands, activeBrands, isAuthenticated])
}

const getBrandIcon = (index: number) => {
  const icons = [Store, Command, GalleryVerticalEnd, AudioWaveform]
  return icons[index % icons.length]
}

export const usePosUserData = () => {
  const { user, isAuthenticated } = usePosData()

  return useMemo(() => {
    if (!isAuthenticated || !user) {
      return userData
    }

    return {
      name: user.full_name || user.email,
      email: user.email,
      avatar: '/avatars/shadcn.jpg'
    }
  }, [user, isAuthenticated])
}

export const teamsData = fallbackTeamsData
