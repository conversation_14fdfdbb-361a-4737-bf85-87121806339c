import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import { itemTypesApi, type GetItemTypesParams } from '@/lib/item-types-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseItemTypesInStoreDataOptions extends Partial<GetItemTypesParams> {
  enabled?: boolean
  store_uid?: string
}

export function useItemTypesInStoreData(params: UseItemTypesInStoreDataOptions = {}) {
  const { enabled = true, store_uid, ...apiParams } = params
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const finalParams: GetItemTypesParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    skip_limit: true,
    active: 1,
    ...(store_uid && {
      store_uid,
      apply_with_store: 1
    }),
    ...apiParams
  }

  return useQuery({
    queryKey: [QUERY_KEYS.ITEM_TYPES, 'store', finalParams],
    queryFn: async () => {
      const response = await itemTypesApi.getItemTypes(finalParams)
      return response.data || []
    },
    enabled: enabled && !!(company?.id && selectedBrand?.id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}
