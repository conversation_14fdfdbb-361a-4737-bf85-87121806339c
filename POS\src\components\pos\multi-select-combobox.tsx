import * as React from 'react'

import { Check, ChevronsUpDown } from 'lucide-react'

import { cn } from '@/lib/utils'

import { Checkbox } from '@/components/ui/checkbox'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

export interface MultiSelectOption {
  value: string
  label: string
}

export interface MultiSelectComboboxProps {
  options: MultiSelectOption[]
  value: string[]
  onValueChange: (value: string[]) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyText?: string
  className?: string
  disabled?: boolean
  selectAllLabel?: string
  allSelectedText?: string
  selectedCountText?: (count: number) => string
}

export function MultiSelectCombobox({
  options,
  value,
  onValueChange,
  placeholder = 'Select options...',
  searchPlaceholder = 'Search...',
  emptyText = 'No results found.',
  className,
  disabled = false,
  selectAllLabel = 'Select all',
  allSelectedText = 'All',
  selectedCountText = (count: number) => `${count} selected`
}: MultiSelectComboboxProps) {
  const [open, setOpen] = React.useState(false)

  const isAllSelected = options.length > 0 && value.length === options.length
  const isIndeterminate = value.length > 0 && value.length < options.length

  const handleSelectAll = () => {
    if (isAllSelected) {
      onValueChange([])
    } else {
      onValueChange(options.map(option => option.value))
    }
  }

  const handleSelectItem = (optionValue: string) => {
    const newValue = value.includes(optionValue) ? value.filter(v => v !== optionValue) : [...value, optionValue]
    onValueChange(newValue)
  }

  const getDisplayText = () => {
    if (value.length === 0) return placeholder
    if (value.length === 1) {
      const selectedOption = options.find(option => option.value === value[0])
      return selectedOption?.label || placeholder
    }
    if (value.length === options.length) return allSelectedText
    return selectedCountText(value.length)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <button
          type='button'
          role='combobox'
          aria-expanded={open}
          aria-label={getDisplayText()}
          className={cn(
            "border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex h-9 items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
            className || 'w-fit'
          )}
          disabled={disabled}
        >
          <span className={cn(value.length === 0 && 'text-muted-foreground', 'truncate')}>{getDisplayText()}</span>
          <ChevronsUpDown className='size-4 opacity-50' />
        </button>
      </PopoverTrigger>
      <PopoverContent
        className='p-0'
        sideOffset={4}
        align='start'
        style={{ width: 'var(--radix-popover-trigger-width)' }}
      >
        <Command>
          <CommandInput placeholder={searchPlaceholder} />
          <CommandList>
            <CommandEmpty>{emptyText}</CommandEmpty>
            <CommandGroup>
              <CommandItem onSelect={handleSelectAll} className='flex cursor-pointer items-center gap-2'>
                <Checkbox
                  checked={isAllSelected ? true : isIndeterminate ? 'indeterminate' : false}
                  className='pointer-events-none data-[state=checked]:text-white data-[state=indeterminate]:text-white data-[state=unchecked]:text-black'
                  classNameIcon='!stroke-white'
                />
                <span className='font-medium'>{selectAllLabel}</span>
              </CommandItem>

              {options.map(option => {
                const isSelected = value.includes(option.value)
                return (
                  <CommandItem
                    key={option.value}
                    onSelect={() => handleSelectItem(option.value)}
                    className='flex cursor-pointer items-center gap-2'
                  >
                    <Checkbox
                      checked={isSelected}
                      className='pointer-events-none data-[state=checked]:text-white data-[state=indeterminate]:text-white data-[state=unchecked]:text-black'
                      classNameIcon='!stroke-white'
                    />
                    <span>{option.label}</span>
                    {isSelected && <Check className='text-primary ml-auto h-4 w-4' />}
                  </CommandItem>
                )
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
