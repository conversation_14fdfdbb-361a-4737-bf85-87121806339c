import { IconMessages, IconUsers, IconDevices, IconReceiptDollar } from '@tabler/icons-react'

import { type NavItem } from '../types'

export const businessNavItems: NavItem[] = [
  {
    title: 'Chương Trình',
    icon: IconReceiptDollar,
    items: [
      {
        title: '<PERSON><PERSON><PERSON>ến mãi',
        url: '/sale/promotion'
      },
      {
        title: 'Combo',
        url: '/sale/combo'
      },
      {
        title: 'Gi<PERSON>m giá',
        url: '/sale/discount/regular'
      },
      {
        title: 'Gi<PERSON>m giá theo hội viên',
        url: '/sale/discount/membership',
        disabled: false
      },
      {
        title: 'Chiết khấu thanh toán',
        url: '/sale/discount-payment',
        disabled: false
      },
      {
        title: 'Phí dịch vụ',
        url: '/sale/service-charge',
        disabled: false
      }
    ]
  },
  {
    title: '<PERSON><PERSON><PERSON> Bán Hàng',
    icon: IconMessages,
    items: [
      {
        title: '<PERSON><PERSON><PERSON> bán hàng',
        url: '/sale-channel/channel'
      },
      {
        title: '<PERSON><PERSON><PERSON><PERSON> gi<PERSON> theo kênh',
        url: '/sale-channel/discount'
      }
    ]
  },
  {
    title: '<PERSON><PERSON><PERSON><PERSON> Bị',
    icon: IconDevices,
    items: [
      {
        title: 'Quản lý thiết bị',
        url: '/devices/list'
      },
      {
        title: 'Loại thiết bị',
        url: '/devices/types'
      }
    ]
  },
  {
    title: 'Nhân Viên',
    icon: IconUsers,
    items: [
      {
        title: 'Danh sách nhân viên',
        url: '/employee/list'
      },
      {
        title: 'Danh sách chức vụ',
        url: '/employee/role'
      }
    ]
  }
]
