import {
  DownloadIcon,
  UploadIcon,
  GearIcon,
  MixerVerticalIcon,
  UpdateIcon,
  CalendarIcon,
  ChevronDownIcon
} from '@radix-ui/react-icons'

import { useNavigate } from '@tanstack/react-router'

import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

import { useItemsInCity } from '../../context'

export function ItemsInCityButtons() {
  const { setOpen } = useItemsInCity()
  const navigate = useNavigate()

  const handleCreateItem = () => {
    navigate({ to: '/menu/items/items-in-city/detail' })
  }

  const handleExportMenu = () => {
    setOpen('export-dialog')
  }

  const handleImportFromFile = () => {
    setOpen('import')
  }

  const handleConfigPriceBySource = () => {
    // TODO: Implement config price by source functionality
  }

  const handleSortMenu = () => {
    // TODO: Implement sort menu functionality
  }

  const handleCopyMenu = () => {
    // TODO: Implement copy menu functionality
  }

  const handleConfigTimeFrame = () => {
    // TODO: Implement config time frame functionality
  }

  return (
    <>
      <div className='flex items-center space-x-2'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='outline' size='sm'>
              Tiện ích
              <ChevronDownIcon className='ml-2 h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='w-56'>
            <DropdownMenuItem onClick={handleExportMenu}>
              <DownloadIcon className='mr-2 h-4 w-4' />
              Xuất, sửa thực đơn
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleImportFromFile}>
              <UploadIcon className='mr-2 h-4 w-4' />
              Thêm món từ file
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleConfigPriceBySource}>
              <GearIcon className='mr-2 h-4 w-4' />
              Cấu hình giá theo nguồn
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleSortMenu}>
              <MixerVerticalIcon className='mr-2 h-4 w-4' />
              Sắp xếp thực đơn
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleCopyMenu}>
              <UpdateIcon className='mr-2 h-4 w-4' />
              Sao chép thực đơn
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleConfigTimeFrame}>
              <CalendarIcon className='mr-2 h-4 w-4' />
              Cấu hình khung thời gian
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button variant='default' size='sm' onClick={handleCreateItem}>
          Tạo món
        </Button>
      </div>
    </>
  )
}
