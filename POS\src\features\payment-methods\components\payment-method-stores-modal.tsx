import { useQuery } from '@tanstack/react-query'

import { paymentMethodsApi } from '@/lib/payment-methods-api'

import { QUERY_KEYS } from '@/constants/query-keys'

import { usePosData } from '@/hooks/use-pos-data'

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Skeleton } from '@/components/ui/skeleton'
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table'

interface PaymentMethodStoresModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  paymentMethodId: string
  paymentMethodName: string
  storeUids: string[]
}

interface StoreDetail {
  id: string
  store_name: string
  city_uid: string
  address: string
}

export function PaymentMethodStoresModal({
  open,
  onOpenChange,
  paymentMethodId,
  paymentMethodName: _paymentMethodName,
  storeUids
}: PaymentMethodStoresModalProps) {
  const { getCityById } = usePosData()

  const { data, isLoading, error } = useQuery({
    queryKey: [QUERY_KEYS.PAYMENT_METHODS_DETAIL, paymentMethodId],
    queryFn: async () => {
      // Call API with list_payment_method_uid parameter
      const response = await paymentMethodsApi.getPaymentMethodWithStores(storeUids)
      return response
    },
    enabled: open && storeUids.length > 0,
    staleTime: 5 * 60 * 1000 // 5 minutes
  })

  const stores: StoreDetail[] = data?.stores || []

  const getCityName = (cityUid: string) => {
    const city = getCityById(cityUid)
    return city?.city_name || cityUid
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle>Danh sách cửa hàng áp dụng</DialogTitle>
        </DialogHeader>
        <div className='space-y-4'>
          {isLoading ? (
            <div className='space-y-3'>
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className='flex items-center space-x-4'>
                  <Skeleton className='h-4 w-48' />
                  <Skeleton className='h-4 w-32' />
                </div>
              ))}
            </div>
          ) : error ? (
            <p className='text-sm text-red-600'>Có lỗi xảy ra khi tải danh sách cửa hàng</p>
          ) : stores.length === 0 ? (
            <p className='text-muted-foreground py-8 text-center text-sm'>
              Không có cửa hàng nào áp dụng phương thức thanh toán này
            </p>
          ) : (
            <div className='rounded-md border'>
              <Table>
                <TableBody>
                  {stores.map(store => (
                    <TableRow key={store.id}>
                      <TableCell className='font-medium'>{store.store_name}</TableCell>
                      <TableCell className='font-mono text-sm'>
                        {getCityName(store.city_uid)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
