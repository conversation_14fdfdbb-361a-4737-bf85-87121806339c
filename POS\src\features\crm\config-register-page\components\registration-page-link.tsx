import React from 'react'

import { Eye, Clipboard } from 'lucide-react'
import { toast } from 'sonner'

import { Button, Input } from '@/components/ui'

import { useConfigRegister } from '../context'

const RegistrationPageLinkSection: React.FC = () => {
  const { brandId } = useConfigRegister()
  const origin = typeof window !== 'undefined' ? window.location.origin : ''
  const registerUrl = `${origin}/membership?pos_parent=${brandId}`

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(registerUrl)
      toast.success('Đã sao chép đường dẫn')
    } catch (_e) {
      toast.error('Không thể sao chép, vui lòng thử lại')
    }
  }

  const handlePreview = () => {
    window.open(registerUrl, '_blank', 'noopener,noreferrer')
  }

  return (
    <>
      <div className='flex items-center gap-2'>
        <Input readOnly value={registerUrl} className='font-mono' />
        <Button type='button' variant='outline' size='icon' onClick={handleCopy} title='Sao chép'>
          <Clipboard className='h-4 w-4' />
        </Button>
      </div>
      <div className='flex items-center gap-2'>
        <Button type='button' onClick={handlePreview}>
          <Eye className='mr-2 h-4 w-4' /> Xem trước
        </Button>
      </div>
    </>
  )
}

export default RegistrationPageLinkSection
