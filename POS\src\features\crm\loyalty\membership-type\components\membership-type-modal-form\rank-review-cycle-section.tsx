import { UseFormReturn } from 'react-hook-form'

import {
  Input,
  Label,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'

import { useMembershipTypeContext } from '../../context'
import type { MembershipTypeFormData } from './schema'

interface RankReviewCycleSectionProps {
  form: UseFormReturn<MembershipTypeFormData>
}

export function RankReviewCycleSection({ form }: RankReviewCycleSectionProps) {
  const { register, watch, setValue } = form
  const { data: membershipTypes } = useMembershipTypeContext()

  const rankChangeType = watch('rankChangeType')
  const moneyChangeType = watch('moneyChangeType')

  return (
    <div className='space-y-6'>
      <div className='space-y-4'>
        {/* Định mức chi tiêu xét lại hạng */}
        <div className='space-y-2'>
          <Label htmlFor='downgradeAmount'><PERSON><PERSON><PERSON> mức chi tiêu xét lại hạng *</Label>
          <div className='flex items-center space-x-2'>
            <Input
              {...register('downgradeAmount')}
              type='text'
              id='downgradeAmount'
              placeholder='0'
              className='flex-1'
            />
            <span className='text-sm text-gray-500'>VNĐ</span>
          </div>
        </div>

        {/* Nếu không đạt định mức chi tiêu */}
        <div className='space-y-3'>
          <h5 className='text-sm font-medium text-gray-700'>Nếu không đạt định mức chi tiêu:</h5>

          {/* Thay đổi hạng */}
          <div className='space-y-2'>
            <Label>Thay đổi hạng:</Label>
            <RadioGroup
              value={rankChangeType}
              onValueChange={value => setValue('rankChangeType', value as 'maintain' | 'downgrade')}
              className='flex items-center space-x-4'
            >
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='maintain' id='rank-maintain' />
                <Label htmlFor='rank-maintain' className='cursor-pointer'>
                  Duy trì hạng
                </Label>
              </div>

              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='downgrade' id='rank-downgrade' />
                <Label htmlFor='rank-downgrade' className='cursor-pointer'>
                  Hạ hạng về
                </Label>
                <Select value={watch('downgradeToLevel')} onValueChange={value => setValue('downgradeToLevel', value)}>
                  <SelectTrigger className='ml-2 w-32'>
                    <SelectValue placeholder='Chọn hạng mới' />
                  </SelectTrigger>
                  <SelectContent>
                    {membershipTypes?.map((type: any) => (
                      <SelectItem key={type.id} value={type.type_id || ''}>
                        {type.type_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </RadioGroup>
          </div>

          {/* Thay đổi tiền tích lũy */}
          <div className='space-y-2'>
            <Label>Thay đổi tiền tích lũy:</Label>
            <RadioGroup
              value={moneyChangeType}
              onValueChange={value => setValue('moneyChangeType', value as 'maintain' | 'deduct' | 'reset')}
              className='space-y-2'
            >
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='maintain' id='money-maintain' />
                <Label htmlFor='money-maintain' className='cursor-pointer'>
                  Duy trì tiền tích lũy
                </Label>
              </div>

              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='deduct' id='money-deduct' />
                <div className='flex items-center'>
                  <div className='rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    Trừ tiền tích lũy
                  </div>
                  <Input
                    {...register('downgradeMinusPointAmount', {
                      setValueAs: value => (value === '' ? undefined : Number(value))
                    })}
                    type='number'
                    placeholder='0'
                    className='w-24 rounded-none border border-r-0 border-l-0'
                  />
                  <div className='rounded-r-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    VNĐ
                  </div>
                </div>
              </div>

              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='reset' id='money-reset' />
                <div className='flex items-center'>
                  <div className='rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    Reset tiền tích lũy về
                  </div>
                  <Input
                    {...register('downgradeResetPointAmount', {
                      setValueAs: value => (value === '' ? undefined : Number(value))
                    })}
                    type='number'
                    placeholder='0'
                    className='w-24 rounded-none border border-r-0 border-l-0'
                  />
                  <div className='rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    VNĐ
                  </div>
                </div>
              </div>
            </RadioGroup>
          </div>

          {/* Thay đổi điểm tích lũy */}
          <div className='space-y-2'>
            <Label>Thay đổi điểm tích lũy:</Label>
            <RadioGroup
              value={watch('pointChangeType')}
              onValueChange={value => setValue('pointChangeType', value as 'maintain' | 'deduct' | 'reset')}
              className='space-y-2'
            >
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='maintain' id='points-maintain' />
                <Label htmlFor='points-maintain' className='cursor-pointer'>
                  Duy trì điểm tích lũy hiện tại
                </Label>
              </div>

              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='deduct' id='points-deduct' />
                <div className='flex items-center'>
                  <div className='rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    Trừ điểm tích lũy
                  </div>
                  <Input
                    {...register('downgradeMinusPoint', {
                      setValueAs: value => (value === '' ? undefined : Number(value))
                    })}
                    type='number'
                    placeholder='0'
                    className='w-24 rounded-none border border-r-0 border-l-0'
                  />
                  <div className='rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    Điểm
                  </div>
                </div>
              </div>

              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='reset' id='points-reset' />
                <div className='flex items-center'>
                  <div className='rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    Reset điểm tích lũy về
                  </div>
                  <Input
                    {...register('downgradeResetPoint', {
                      setValueAs: value => (value === '' ? undefined : Number(value))
                    })}
                    type='number'
                    placeholder='0'
                    className='w-24 rounded-none border border-r-0 border-l-0'
                  />
                  <div className='rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    Điểm
                  </div>
                </div>
              </div>
            </RadioGroup>
          </div>
        </div>

        {/* Nếu vượt quá định mức chi tiêu */}
        <div className='space-y-3'>
          <h5 className='text-sm font-medium text-gray-700'>Nếu vượt quá định mức chi tiêu:</h5>

          {/* Thay đổi tiền tích lũy */}
          <div className='space-y-2'>
            <RadioGroup
              value={watch('exceededMoneyChangeType')}
              onValueChange={value => setValue('exceededMoneyChangeType', value as 'maintain' | 'deduct' | 'reset')}
              className='space-y-2'
            >
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='maintain' id='exceeded-money-maintain' />
                <Label htmlFor='exceeded-money-maintain' className='cursor-pointer'>
                  Duy trì tiền tích lũy
                </Label>
              </div>

              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='deduct' id='exceeded-money-deduct' />
                <div className='flex items-center'>
                  <div className='rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    Trừ tiền tích lũy
                  </div>
                  <Input
                    {...register('unchangeMinusPointAmount', {
                      setValueAs: value => (value === '' ? undefined : Number(value))
                    })}
                    type='number'
                    placeholder='0'
                    className='w-24 rounded-none border border-r-0 border-l-0'
                  />
                  <div className='rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    VNĐ
                  </div>
                </div>
              </div>

              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='reset' id='exceeded-money-reset' />
                <div className='flex items-center'>
                  <div className='rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    Reset tiền tích lũy về
                  </div>
                  <Input
                    {...register('unchangeResetPointAmount', {
                      setValueAs: value => (value === '' ? undefined : Number(value))
                    })}
                    type='number'
                    placeholder='0'
                    className='w-24 rounded-none border border-r-0 border-l-0'
                  />
                  <div className='rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    VNĐ
                  </div>
                </div>
              </div>
            </RadioGroup>
          </div>
        </div>
      </div>
    </div>
  )
}
