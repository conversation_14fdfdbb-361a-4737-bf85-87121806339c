import { Link } from '@tanstack/react-router'

import { But<PERSON> } from '@/components/ui'

import { AccountTable } from './components/account-table'
import { LocalUser } from './data'
import { useAccountManagement } from './hooks'

export default function AccountPage() {
  const { users, isLoading, error, toggleUserStatus } = useAccountManagement()

  const handleEditUser = (user: LocalUser) => {
    // TODO: Navigate to edit page
    console.log('Edit user:', user)
  }

  const handleToggleStatus = async (userId: string) => {
    await toggleUserStatus(userId)
  }

  if (error) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='py-8 text-center'>
          <p className='text-red-600'>{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-6'>
        <Link to='/general-setups/create-user'>
          <Button>T<PERSON><PERSON> tà<PERSON></Button>
        </Link>
      </div>

      <AccountTable
        users={users}
        isLoading={isLoading}
        onEditUser={handleEditUser}
        onToggleStatus={handleToggleStatus}
      />
    </div>
  )
}
