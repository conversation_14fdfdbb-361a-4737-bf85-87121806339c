import { useEffect } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { HelpCircle } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { DatePicker } from '@/components/ui/date/date-picker'
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'

const timeFrameConfigSchema = z
  .object({
    amount: z.coerce.number().min(0, 'Số tiền phải lớn hơn hoặc bằng 0'),
    startDate: z.date({ required_error: 'Vui lòng chọn ngày bắt đầu' }),
    endDate: z.date({ required_error: '<PERSON><PERSON> lòng chọn ngày kết thúc' }),
    selectedDays: z.array(z.number()).min(1, '<PERSON>ui lòng chọn ít nhất 1 ngày'),
    selectedHours: z.array(z.number()).min(1, 'Vui lòng chọn ít nhất 1 giờ')
  })
  .refine(
    data => {
      if (data.startDate && data.endDate) {
        return data.endDate >= data.startDate
      }
      return true
    },
    {
      message: 'Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu',
      path: ['endDate']
    }
  )

type TimeFrameConfigFormValues = z.infer<typeof timeFrameConfigSchema>

const daysOfWeek = [
  { value: 1, label: 'Thứ 2' },
  { value: 2, label: 'Thứ 3' },
  { value: 4, label: 'Thứ 4' },
  { value: 8, label: 'Thứ 5' },
  { value: 16, label: 'Thứ 6' },
  { value: 32, label: 'Thứ 7' },
  { value: 64, label: 'Chủ nhật' }
]

const hoursOfDay = Array.from({ length: 24 }, (_, i) => ({ value: i, label: `${i}h` }))

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm?: (data: TimeFrameConfigFormValues) => void
  sourceName: string
  data?: any
}

export function TimeFrameConfigDialog({ open, onOpenChange, onConfirm, sourceName, data }: Props) {
  const form = useForm<TimeFrameConfigFormValues>({
    resolver: zodResolver(timeFrameConfigSchema),
    defaultValues: {
      amount: 0,
      startDate: new Date(),
      endDate: new Date(),
      selectedDays: [],
      selectedHours: []
    }
  })

  useEffect(() => {
    if (open && data) {
      form.setValue('amount', Number(data.amount ?? 0))

      const start = data.startDate ? new Date(data.startDate) : new Date()
      const end = data.endDate ? new Date(data.endDate) : new Date()
      form.setValue('startDate', start)
      form.setValue('endDate', end)

      if (Array.isArray(data.selectedDays)) {
        form.setValue('selectedDays', data.selectedDays)
      }
      if (Array.isArray(data.selectedHours)) {
        form.setValue('selectedHours', data.selectedHours)
      }
    }
  }, [open, data, form])

  const onSubmit = (data: TimeFrameConfigFormValues) => {
    onConfirm?.(data)
    onOpenChange(false)
    form.reset()
  }

  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange(newOpen)
    if (!newOpen) {
      form.reset()
    }
  }

  const toggleDay = (dayValue: number) => {
    const currentDays = form.getValues('selectedDays')
    const newDays = currentDays.includes(dayValue)
      ? currentDays.filter(d => d !== dayValue)
      : [...currentDays, dayValue]
    form.setValue('selectedDays', newDays)
  }

  const toggleHour = (hourValue: number) => {
    const currentHours = form.getValues('selectedHours')
    const newHours = currentHours.includes(hourValue)
      ? currentHours.filter(h => h !== hourValue)
      : [...currentHours, hourValue]
    form.setValue('selectedHours', newHours)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-2xl lg:max-w-2xl'>
        <DialogHeader>
          <DialogTitle>Cấu hình giá theo khung thời gian nguồn {sourceName}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            {/* Số tiền */}
            <div className='grid grid-cols-[120px_1fr] items-center gap-4'>
              <FormLabel className='text-sm font-medium text-gray-600'>Số tiền</FormLabel>
              <FormField
                control={form.control}
                name='amount'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type='text'
                        placeholder='0'
                        value={field.value}
                        onChange={e => {
                          field.onChange(e.target.value)
                        }}
                        onKeyDown={e => {
                          if (
                            !/[0-9]/.test(e.key) &&
                            !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)
                          ) {
                            e.preventDefault()
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Ngày áp dụng */}
            <div className='space-y-3'>
              <FormLabel className='text-sm font-medium text-gray-600'>Ngày áp dụng</FormLabel>
              <div className='grid grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='startDate'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-xs text-gray-500'>Ngày bắt đầu</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          onDateChange={field.onChange}
                          placeholder='Chọn ngày bắt đầu'
                          className='border-blue-200 bg-blue-50'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='endDate'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-xs text-gray-500'>Ngày kết thúc</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          onDateChange={field.onChange}
                          placeholder='Chọn ngày kết thúc'
                          className='border-blue-200 bg-blue-50'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Khung thời gian áp dụng */}
            <div className='space-y-4'>
              <FormLabel className='text-sm font-medium text-gray-600'>Khung thời gian áp dụng</FormLabel>

              {/* Chọn ngày */}
              <div className='space-y-2'>
                <div className='flex items-center gap-2'>
                  <FormLabel className='text-xs text-gray-500'>Chọn ngày</FormLabel>
                  <HelpCircle className='h-3 w-3 text-gray-400' />
                </div>
                <div className='grid grid-cols-7 gap-2'>
                  {daysOfWeek.map(day => {
                    const isSelected = form.watch('selectedDays').includes(day.value)
                    return (
                      <Button
                        key={day.value}
                        type='button'
                        variant={isSelected ? 'default' : 'outline'}
                        size='sm'
                        onClick={() => toggleDay(day.value)}
                        className={`w-full ${
                          isSelected
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'border-blue-200 text-gray-700 hover:bg-blue-50'
                        }`}
                      >
                        {day.label}
                      </Button>
                    )
                  })}
                </div>
                <FormMessage />
              </div>

              {/* Chọn giờ */}
              <div className='space-y-2'>
                <div className='flex items-center gap-2'>
                  <FormLabel className='text-xs text-gray-500'>Chọn giờ</FormLabel>
                  <HelpCircle className='h-3 w-3 text-gray-400' />
                </div>
                <div className='grid grid-cols-6 gap-2'>
                  {hoursOfDay.map(hour => {
                    const isSelected = form.watch('selectedHours').includes(hour.value)
                    return (
                      <Button
                        key={hour.value}
                        type='button'
                        variant={isSelected ? 'default' : 'outline'}
                        size='sm'
                        onClick={() => toggleHour(hour.value)}
                        className={`w-full ${
                          isSelected
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'border-blue-200 text-gray-700 hover:bg-blue-50'
                        }`}
                      >
                        {hour.label}
                      </Button>
                    )
                  })}
                </div>
                <FormMessage />
              </div>
            </div>

            <DialogFooter className='pt-4'>
              <DialogClose asChild>
                <Button type='button' variant='outline'>
                  Hủy
                </Button>
              </DialogClose>
              <Button type='button' onClick={form.handleSubmit(onSubmit)}>
                Xác nhận
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
