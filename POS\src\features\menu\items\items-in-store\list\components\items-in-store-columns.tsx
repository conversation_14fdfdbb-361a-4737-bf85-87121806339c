'use client'

import type { ColumnDef } from '@tanstack/react-table'

import { Icon<PERSON><PERSON>, IconTrash } from '@tabler/icons-react'

import { Settings } from 'lucide-react'

import { DataTableColumnHeader } from '@/components/data-table'
import { StatusBadge } from '@/components/pos'
import { Badge, Button, Checkbox, Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui'

import { ItemsInStore } from '../../data'
import { CustomColumnHeader } from './custom-column-header'

interface ColumnsProps {
  onBuffetConfigClick: (item: ItemsInStore) => void
}

const columnsDefinition = ({ onBuffetConfigClick }: ColumnsProps): ColumnDef<ItemsInStore>[] => [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label='Select row'
        onClick={e => e.stopPropagation()}
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => <div className='w-[50px]'>{row.index + 1}</div>,
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    accessorKey: 'code',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Mã món' />,
    cell: ({ row }) => <div className='text-sm font-medium'>{row.getValue('code')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Tên món' />,
    cell: ({ row }) => <div className='max-w-[200px] truncate text-sm font-medium'>{row.getValue('name')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'price',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Giá' />,
    cell: ({ row }) => {
      const price = row.getValue('price') as number
      return (
        <div className='text-sm font-medium'>
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
          }).format(price)}
        </div>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'vatPercent',
    header: ({ column }) => <DataTableColumnHeader column={column} title='VAT (%)' />,
    cell: ({ row }) => {
      const vatPercent = row.getValue('vatPercent') as number
      return <div className='text-right text-sm'>{vatPercent * 100}</div>
    },
    enableSorting: false,
    enableHiding: true
  },

  {
    accessorKey: 'itemType',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Nhóm món' />,
    cell: ({ row }) => (
      <Badge variant='outline' className='text-xs'>
        {row.getValue('itemType')}
      </Badge>
    ),
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'itemClass',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Loại món' />,
    cell: ({ row }) =>
      row.getValue('itemClass') && (
        <Badge variant='outline' className='text-center text-xs'>
          {row.getValue('itemClass')}
        </Badge>
      ),

    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'unit',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Đơn vị tính' />,
    cell: ({ row }) => <div className='text-sm'>{row.getValue('unit')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'sideItems',
    header: ({ column }) => <CustomColumnHeader column={column} title='Món ăn kèm' defaultSort='desc' />,
    cell: ({ row }) => {
      const sideItems = row.getValue('sideItems') as string
      if (!sideItems) return <div>Món chính</div>

      const displayText = sideItems === 'Món ăn kèm' ? 'Món ăn kèm' : sideItems

      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className='max-w-[120px] cursor-help truncate text-sm'>{displayText}</div>
            </TooltipTrigger>
            <TooltipContent>
              <p className='max-w-[300px]'>{displayText}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    },
    enableSorting: true,
    enableHiding: true
  },
  {
    accessorKey: 'city',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Thành phố' />,
    cell: ({ row }) => <div className='text-sm'>{row.getValue('city')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'applyWithStore',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Áp dụng tại' />,
    cell: ({ row }) => <div className='text-sm'>{row.getValue('applyWithStore') ? 'Cửa hàng' : ''}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Trạng thái' />,
    cell: ({ row }) => (
      <div className='text-sm'>{row.getValue('applyWithStore') === 1 ? 'Sửa từ món gốc' : 'Món mới'}</div>
    ),
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'buffetConfig',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Cấu hình buffet' />,
    cell: ({ row }) => {
      const item = row.original
      const isBuffetConfigured = item.extra_data?.is_buffet_item === 1

      if (isBuffetConfigured) {
        return (
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium'>Đã cấu hình</span>
            <Button
              variant={'outline'}
              size='sm'
              onClick={() => onBuffetConfigClick(item)}
              className='h-6 px-2 text-xs'
            >
              <Settings className='h-3 w-3' />
            </Button>
          </div>
        )
      }

      return (
        <Button variant={'outline'} size='sm' onClick={() => onBuffetConfigClick(item)} className='h-7 px-2 text-xs'>
          <Settings className='mr-1 h-3 w-3' />
          Cấu hình
        </Button>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'customization',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Customization' />,
    cell: ({ row, table }) => {
      const item = row.original
      const meta = table.options.meta as {
        onCustomizationClick?: (menuItem: ItemsInStore) => void
        customizations?: Array<{ id: string; name: string }>
      }

      const currentCustomizationUid = item.customization_uid
      const currentCustomization = meta?.customizations?.find(c => c.id === currentCustomizationUid)
      if (currentCustomization) {
        return (
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium'>{currentCustomization.name}</span>
            <Button
              variant={'outline'}
              size='sm'
              onClick={() => meta?.onCustomizationClick?.(item)}
              className='h-6 px-2 text-xs'
            >
              <Settings className='h-3 w-3' />
            </Button>
          </div>
        )
      }

      return (
        <Button
          variant={'outline'}
          size='sm'
          onClick={() => meta?.onCustomizationClick?.(item)}
          className='h-7 px-2 text-xs'
        >
          <Settings className='mr-1 h-3 w-3' />
          Cấu hình
        </Button>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    id: 'copy',
    header: 'Sao chép tạo món mới',
    cell: ({ row, table }) => {
      const item = row.original
      const meta = table.options.meta as {
        onCopyClick?: (menuItem: ItemsInStore) => void
      }

      return (
        <Button
          variant='ghost'
          size='sm'
          className='ml-14 h-8 w-8'
          onClick={e => {
            e.stopPropagation()
            meta?.onCopyClick?.(item)
          }}
        >
          <IconCopy className='h-4 w-4' />
          <span className='sr-only'>Sao chép thiết bị {item.item_name}</span>
        </Button>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'isActive',
    header: ({ column }) => <CustomColumnHeader column={column} title='Thao tác' defaultSort='desc' />,
    enableSorting: true,
    cell: ({ row, table }) => {
      const item = row.original
      const isActive = row.getValue('isActive') as boolean
      const meta = table.options.meta as {
        onToggleStatus?: (menuItem: ItemsInStore) => void
      }

      return (
        <div
          onClick={e => {
            e.stopPropagation()
            meta?.onToggleStatus?.(item)
          }}
          className='cursor-pointer'
        >
          <StatusBadge isActive={isActive} activeText='Active' inactiveText='Deactive' />
        </div>
      )
    },
    enableHiding: true
  },
  {
    id: 'actions',
    cell: ({ row, table }) => {
      const item = row.original
      const meta = table.options.meta as {
        onDeleteClick?: (item: ItemsInStore) => void
      }

      return (
        <div className='flex items-center justify-center'>
          <Button
            variant='ghost'
            size='sm'
            onClick={e => {
              e.stopPropagation()
              meta?.onDeleteClick?.(item)
            }}
            className='h-8 w-8 p-0 text-red-600 hover:text-red-700'
          >
            <IconTrash className='h-4 w-4' />
            <span className='sr-only'>Xóa món {item.item_name}</span>
          </Button>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
    size: 80
  }
]

export const columns = columnsDefinition({ onBuffetConfigClick: () => {} })
export const createColumns = columnsDefinition
