import ExcelJS from 'exceljs'

interface PromotionRevenueExportData {
  promotion_id: string
  promotion_name: string
  total_bill?: number
  revenue_net?: number
  total_amount?: number
  discount_amount?: number
  list_data: Array<{
    date: string
    total_bill?: number
    revenue_net?: number
    total_amount?: number
    discount_amount?: number
  }>
}

interface PromotionExportOptions {
  filename?: string
  dateRange: { from: Date; to: Date }
  selectedStoreNames: string[]
}

export async function exportPromotionRevenueToExcel(
  data: PromotionRevenueExportData[],
  options: PromotionExportOptions
) {
  const { filename = 'top-promotions.xlsx' } = options

  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('Báo cáo doanh thu khuyến mãi')

  const allDates = new Set<string>()
  data.forEach(promotion => {
    if (promotion.list_data && Array.isArray(promotion.list_data)) {
      promotion.list_data.forEach(item => {
        if (item.date) {
          allDates.add(item.date)
        }
      })
    }
  })

  const sortedDates = Array.from(allDates).sort()

  const topHeaders = ['', 'Tổng']
  const subHeaders = ['CTKM', 'Tổng hoá đơn', 'Giảm giá', 'Doanh thu (net)']

  sortedDates.forEach((date, _) => {
    const formattedDate = new Date(date).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })

    topHeaders.push(formattedDate, formattedDate)
    subHeaders.push('Tổng hoá đơn', 'Giảm giá', 'Doanh thu (net)', 'Tổng hoá đơn', 'Giảm giá', 'Doanh thu (net)')
  })

  const totalColumns = 1 + 3 + sortedDates.length * 6

  const columns = []
  for (let i = 0; i < totalColumns; i++) {
    columns.push({
      header: '',
      key: `col_${i}`,
      width: i === 0 ? 25 : 15
    })
  }
  worksheet.columns = columns

  const topHeaderRow = worksheet.getRow(1)
  topHeaderRow.height = 30
  topHeaderRow.font = { bold: true, color: { argb: 'FFFFFF' } }
  topHeaderRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '4472C4' } }
  topHeaderRow.alignment = { horizontal: 'center', vertical: 'middle' }
  topHeaderRow.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  }

  topHeaders.forEach((header, index) => {
    const cell = topHeaderRow.getCell(index + 1)
    cell.value = header

    if (index === 0) {
      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFF' } }
      cell.font = { bold: true, color: { argb: '000000' } }
    } else if (index === 1) {
      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '4472C4' } }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
    } else {
      const isPrimaryColumn = (index - 2) % 2 === 0
      if (isPrimaryColumn) {
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '4472C4' } }
        cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      } else {
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF8C00' } }
        cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      }
    }
  })

  if (totalColumns >= 4) {
    worksheet.mergeCells(1, 2, 1, 4)
  }

  let currentCol = 5
  sortedDates.forEach(() => {
    const startCol = currentCol
    const endCol = currentCol + 5

    if (endCol <= totalColumns) {
      worksheet.mergeCells(1, startCol, 1, endCol)
    }

    currentCol = endCol + 1
  })

  const subHeaderRow = worksheet.getRow(2)
  subHeaderRow.height = 30
  subHeaderRow.font = { bold: true, color: { argb: '000000' } }
  subHeaderRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFF' } }
  subHeaderRow.alignment = { horizontal: 'center', vertical: 'middle' }
  subHeaderRow.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  }

  subHeaders.forEach((header, index) => {
    const cell = subHeaderRow.getCell(index + 1)
    cell.value = header

    if (index === 0) {
      cell.font = { bold: true, color: { argb: '000000' } }
    }
  })

  data.forEach((promotion, rowIndex) => {
    const rowData: Record<string, string | number> = { col_0: promotion.promotion_name }

    let totalData: { total_bill: number; discount_amount: number; revenue_net: number }

    if (
      promotion.total_bill !== undefined &&
      promotion.discount_amount !== undefined &&
      promotion.revenue_net !== undefined
    ) {
      totalData = {
        total_bill: promotion.total_bill || 0,
        discount_amount: promotion.discount_amount || 0,
        revenue_net: promotion.revenue_net || 0
      }
    } else {
      totalData = (promotion.list_data || []).reduce(
        (acc: { total_bill: number; discount_amount: number; revenue_net: number }, item) => ({
          total_bill: (acc.total_bill || 0) + (item.total_bill || 0),
          discount_amount: (acc.discount_amount || 0) + (item.discount_amount || 0),
          revenue_net: (acc.revenue_net || 0) + (item.revenue_net || 0)
        }),
        { total_bill: 0, discount_amount: 0, revenue_net: 0 }
      )
    }

    rowData.col_1 = totalData.total_bill || 0
    rowData.col_2 = totalData.discount_amount || 0
    rowData.col_3 = totalData.revenue_net || 0

    let colIndex = 4
    sortedDates.forEach((date, _) => {
      const dateData = (promotion.list_data || []).find(item => item.date === date)

      if (colIndex + 5 < totalColumns) {
        rowData[`col_${colIndex}`] = dateData?.total_bill || 0
        colIndex++
        rowData[`col_${colIndex}`] = dateData?.discount_amount || 0
        colIndex++
        rowData[`col_${colIndex}`] = dateData?.revenue_net || 0
        colIndex++

        rowData[`col_${colIndex}`] = 0
        colIndex++
        rowData[`col_${colIndex}`] = 0
        colIndex++
        rowData[`col_${colIndex}`] = 0
        colIndex++
      }
    })

    const row = worksheet.addRow(rowData)

    row.alignment = { horizontal: 'center', vertical: 'middle' }

    if (rowIndex % 2 === 0) {
      row.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F8F9FA' } }
    } else {
      row.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFF' } }
    }

    row.eachCell((cell, _colNumber) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }

      if (typeof cell.value === 'number') {
        if (cell.value > 0) {
          cell.numFmt = '#,##0'
          cell.font = { bold: true, color: { argb: '000000' } }
        } else {
          cell.numFmt = '#,##0'
          cell.font = { color: { argb: '666666' } }
        }
      }
    })
  })

  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })

  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.click()

  window.URL.revokeObjectURL(url)
}
