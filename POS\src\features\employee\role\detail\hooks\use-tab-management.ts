import { useState } from 'react'

interface UseTabManagementProps {
  permissions: string[]
  onPermissionChange: (permission: string, checked: boolean) => void
}

export const useTabManagement = ({ permissions, onPermissionChange }: UseTabManagementProps) => {
  const [activeTab, setActiveTab] = useState('cms')

  const getEnabledTabs = () => {
    const enabledTabs: string[] = []
    if (permissions.includes('POS_CMS')) enabledTabs.push('cms')
    if (permissions.includes('POS_CLIENT')) enabledTabs.push('pos-pda')
    if (permissions.includes('POS_MANAGER')) enabledTabs.push('manager')
    return enabledTabs
  }

  const enabledTabs = getEnabledTabs()
  const hasAnyEnabledTabs = enabledTabs.length > 0

  const handlePermissionChangeWithTabSwitch = (permission: string, checked: boolean) => {
    onPermissionChange(permission, checked)

    if (['POS_CMS', 'POS_CLIENT', 'POS_MANAGER'].includes(permission)) {
      setTimeout(() => {
        const newEnabledTabs = []
        const currentPermissions = checked
          ? [...permissions, permission]
          : permissions.filter(p => p !== permission)

        if (currentPermissions.includes('POS_CMS')) newEnabledTabs.push('cms')
        if (currentPermissions.includes('POS_CLIENT')) newEnabledTabs.push('pos-pda')
        if (currentPermissions.includes('POS_MANAGER')) newEnabledTabs.push('manager')

        if (!newEnabledTabs.includes(activeTab) && newEnabledTabs.length > 0) {
          setActiveTab(newEnabledTabs[0])
        }
      }, 0)
    }
  }

  return {
    activeTab,
    setActiveTab,
    enabledTabs,
    hasAnyEnabledTabs,
    handlePermissionChangeWithTabSwitch
  }
}
