import type { ItemScheduleData } from '../types/menu-schedule-api'
import { convertDateWeekToArray, convertHourDayToObject } from './time-conversion'

export interface FormData {
  item_name?: string
  item_id?: string
  item_id_barcode?: string
  ots_price?: number
  item_class_uid?: string
  item_type_uid?: string
  unit_uid?: string
  unit_secondary_uid?: string
  customization?: string
  vat_rate?: number
  cooking_time?: number
  is_service?: boolean
  display_order?: number
  description?: string
  is_eat_with?: boolean
  no_update_quantity?: boolean
  store_uid?: string
  sku?: string
  time_sale_date_week?: number
  time_sale_hour_day?: number
  selected_days?: boolean[]
  selected_hours?: Record<string, boolean>
  // Advanced fields
  allow_edit_price?: boolean
  require_quantity_input?: boolean
  allow_remove_without_permission?: boolean
  is_featured?: boolean
  is_buffet?: boolean
  inqr_formula?: string
}

export const mapItemDataToForm = (item: Partial<ItemScheduleData>): Partial<FormData> => ({
  item_name: item.item_name || '',
  item_id: item.item_id || '',
  ots_price: item.ots_price || 0,
  item_class_uid: item.item_class_uid || '',
  item_type_uid: item.item_type_uid || '',
  unit_uid: item.unit_uid || '',
  unit_secondary_uid: item.unit_secondary_uid || '',
  customization: item.customization_uid || '',
  vat_rate: item.ots_tax || 0,
  cooking_time: item.time_cooking || 0,
  is_service: item.is_service === 1,
  display_order: item.sort || 1000,
  description: item.description || '',
  is_eat_with: item.is_eat_with === 1,
  store_uid: item.store_uid || '',
  time_sale_date_week: item.time_sale_date_week || 0,
  time_sale_hour_day: item.time_sale_hour_day || 0,
  selected_days: item.time_sale_date_week
    ? convertDateWeekToArray(item.time_sale_date_week)
    : [false, false, false, false, false, false, false],
  selected_hours: item.time_sale_hour_day ? convertHourDayToObject(item.time_sale_hour_day) : {}
})
