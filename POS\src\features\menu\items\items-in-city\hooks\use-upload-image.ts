import { useState } from 'react'

import { imagesApi } from '@/lib/api/pos/images-api'

interface UploadImageResponse {
  image_path: string
  image_path_thumb: string
}

export const useUploadImage = () => {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const uploadImage = async (file: File): Promise<UploadImageResponse | null> => {
    setIsUploading(true)
    setError(null)

    try {
      const response = await imagesApi.uploadImage(file)

      const imageUrl = response.data.image_url
      return {
        image_path: imageUrl,
        image_path_thumb: `${imageUrl}?width185`
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed'
      setError(errorMessage)
      return null
    } finally {
      setIsUploading(false)
    }
  }

  return {
    uploadImage,
    isUploading,
    error
  }
}
