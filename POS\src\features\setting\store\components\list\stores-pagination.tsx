import { ChevronLeft, ChevronRight } from 'lucide-react'

import { Button } from '@/components/ui/button'

interface StoresPaginationProps {
  currentPage: number
  onPageChange: (page: number) => void
  hasNextPage: boolean
  total?: number
  pageSize?: number
}

export function StoresPagination({ 
  currentPage, 
  onPageChange, 
  hasNextPage, 
  total,
  pageSize = 50 
}: StoresPaginationProps) {
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1)
    }
  }

  const handleNextPage = () => {
    if (hasNextPage) {
      onPageChange(currentPage + 1)
    }
  }

  const startItem = (currentPage - 1) * pageSize + 1
  const endItem = Math.min(currentPage * pageSize, total || 0)
  const showingText = total ? `Hiển thị ${startItem}-${endItem} trong tổng số ${total} cửa hàng` : `Trang ${currentPage}`

  return (
    <div className='flex items-center justify-between py-4'>
      <div className='text-sm text-muted-foreground'>
        {showingText}
      </div>
      
      <div className='flex items-center gap-4'>
        <Button
          variant='outline'
          size='sm'
          onClick={handlePreviousPage}
          disabled={currentPage === 1}
          className='flex items-center gap-2'
        >
          <ChevronLeft className='h-4 w-4' />
          Trước
        </Button>

        <span className='text-sm font-medium'>Trang {currentPage}</span>

        <Button
          variant='outline'
          size='sm'
          onClick={handleNextPage}
          disabled={!hasNextPage}
          className='flex items-center gap-2'
        >
          Sau
          <ChevronRight className='h-4 w-4' />
        </Button>
      </div>
    </div>
  )
}
