// Import Store type from auth-api
import type { Brand, City, Store } from '@/types/auth'

// Employee data types from API
export interface EmployeeData {
  user: {
    id: string
    email: string
    phone: string
    full_name: string
    profile_image_path: string | null
    role_uid: string
    company_uid: string
    country_id: string
    active: number
    is_verified: number
    partner_company_uid: string | null
    fixed_account: boolean
    created_by: string
    updated_by: string | null
    deleted_by: string | null
    email_verified_at: string | null
    phone_verified_at: string | null
    password_updated_at: string | null
    created_at: string
    updated_at: string
    deleted_at: string | null
    last_login_at: string | null
    is_fabi: number
  }
  user_role: {
    id: string
    role_id: string
    role_name: string
    description: string
    scope: string
    scope_value: string | null
    allow_access: string[]
    reject_permissions: string[]
    created_by: string | null
    updated_by: string
    deleted_by: string | null
    created_at: string
    updated_at: string
    deleted_at: string | null
  }
  user_permissions: {
    id: string
    user_uid: string
    company_uid: string
    stores: Record<string, Record<string, unknown[]>>
    tables: Record<string, unknown>
    created_by: string
    updated_by: string | null
    deleted_by: string | null
    created_at: string
    updated_at: string
    deleted_at: string | null
  }
  company: {
    id: string
    company_id: string
    company_name: string
    description: string | null
    extra_data: Record<string, unknown>
    active: number
    revision: string | null
  }
  brands: Array<{
    id: string
    brand_id: string
    brand_name: string
    extra_data: Record<string, unknown>
    active: number
    is_fabi: number
    sort: number
    created_at: string
    currency: string
  }>
  cities: Array<{
    id: string
    city_id: string
    city_name: string
    active: number
  }>
  stores: Array<{
    id: string
    brand_uid: string
    city_uid: string
    company_uid: string
    store_id: string
    store_name: string
    logo: string
    background: string
    facebook: string
    website: string
    fb_store_id: number
    phone: string
    address: string
    latitude: number
    active: number
    open_at: number
    expiry_date: number
    sort: number
    enable_change_item_in_store: number
    enable_change_item_type_in_store: number
    enable_change_printer_position_in_store: number
    enable_turn_order_report: number
    sale_change_vat_enable: number
    bill_template: number
    is_franchise: number
    tracking_sale: number
    dateend: string
    istrial: string
    change_log_detail: number
  }>
}

// User data type from useUserData hook
export interface UserData {
  id: string
  email: string
  full_name: string
  phone: string
  role_uid: string
  active: number
  phone_verified_at: string | null
  role_name: string
  role_id: string
  role_description: string
  stores: Record<string, unknown>
  brand_access: string[]
  user_permissions?: {
    id: string
    user_uid: string
    company_uid: string
    stores: Record<string, unknown>
    tables: Record<string, unknown>
  }
}

// Brand with cities hierarchy
export interface BrandWithCities extends Brand {
  cities: Array<
    City & {
      stores: Store[]
    }
  >
}

// Summary item type
export interface SummaryItem {
  brandName: string
  accessLevel: string
  type: 'brand' | 'city' | 'store'
}
