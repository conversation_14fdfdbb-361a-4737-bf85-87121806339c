import React from 'react'

import { useNavigate } from '@tanstack/react-router'

import { Plus, Search, MoreHorizontal } from 'lucide-react'

import { useStoresData } from '@/hooks/api'

import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui'

import { OrderSourcesSortModal } from './order-sources-sort-modal'

interface ActionBarProps {
  searchInput: string
  storeFilter: string
  onSearchInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  onSearchKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void
  onStoreFilterChange: (value: string) => void
}

export function ActionBar({
  searchInput,
  storeFilter,
  onSearchInputChange,
  onSearchKeyPress,
  onStoreFilterChange
}: ActionBarProps) {
  const navigate = useNavigate()
  const { data: stores = [] } = useStoresData()
  const [sortModalOpen, setSortModalOpen] = React.useState(false)

  const handleCreateNew = () => {
    navigate({ to: '/setting/source/detail' })
  }

  const handleSortSources = () => {
    setSortModalOpen(true)
  }

  return (
    <div className='flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
      <div className='flex flex-1 items-center gap-4'>
        <div className='flex flex-col'>
          <h1 className='text-2xl font-bold tracking-tight'>Danh sách nguồn</h1>
        </div>

        <div className='relative max-w-sm flex-1'>
          <Search className='text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2' />
          <Input
            placeholder='Tìm kiếm nhà hàng (nhấn Enter để tìm)'
            value={searchInput}
            onChange={onSearchInputChange}
            onKeyDown={onSearchKeyPress}
            className='pl-9'
          />
        </div>

        <Select value={storeFilter} onValueChange={onStoreFilterChange}>
          <SelectTrigger className='w-[200px]'>
            <SelectValue placeholder='Tất cả các điểm' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả các điểm</SelectItem>
            {stores.map(store => (
              <SelectItem key={store.id} value={store.id}>
                {store.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className='flex items-center gap-2'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='outline'>
              <MoreHorizontal className='mr-2 h-4 w-4' />
              Tiện ích
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={handleSortSources}>Sắp xếp nguồn đơn</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button onClick={handleCreateNew}>
          <Plus className='mr-2 h-4 w-4' />
          Thêm nguồn hàng
        </Button>
      </div>

      <OrderSourcesSortModal open={sortModalOpen} onOpenChange={setSortModalOpen} />
    </div>
  )
}
