import { useState } from 'react'

import { toast } from 'sonner'

interface ApiItem {
  id: string
  item_id: string
  item_name: string
  ots_price: number
}

interface MenuItem {
  id: string
  name: string
  price: number
  code?: string
  size?: string
}

interface UseMenuItemSelectionOptions {
  onConfirm?: (newItems: MenuItem[]) => void
}

export function useMenuItemSelection(options: UseMenuItemSelectionOptions = {}) {
  const [selectedMenuItems, setSelectedMenuItems] = useState<Set<string>>(new Set())
  const [menuItemSearchTerm, setMenuItemSearchTerm] = useState('')
  const [selectedMenuSectionOpen, setSelectedMenuSectionOpen] = useState(true)
  const [remainingMenuSectionOpen, setRemainingMenuSectionOpen] = useState(true)

  const handleMenuItemToggle = (itemId: string) => {
    const newSelected = new Set(selectedMenuItems)
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId)
    } else {
      newSelected.add(itemId)
    }
    setSelectedMenuItems(newSelected)
  }

  const handleConfirmMenuItems = (items: ApiItem[]) => {
    // Get all selected items from modal
    const selectedItems = items.filter(item => selectedMenuItems.has(item.id))

    // Convert to MenuItem format
    const updatedMenuItems = selectedItems.map(item => ({
      id: item.id,
      name: item.item_name,
      price: item.ots_price,
      code: item.item_id
    }))

    options.onConfirm?.(updatedMenuItems)

    resetSelection()

    toast.success('Cập nhật món thành công')

    return updatedMenuItems
  }

  const resetSelection = () => {
    setSelectedMenuItems(new Set())
    setMenuItemSearchTerm('')
    setSelectedMenuSectionOpen(true)
    setRemainingMenuSectionOpen(true)
  }

  const setSelectedMenuItemsFromGroup = (groupMenuItems: MenuItem[]) => {
    const itemIds = new Set(groupMenuItems.map(item => item.id))
    setSelectedMenuItems(itemIds)
  }

  const getFilteredMenuItems = (items: ApiItem[]) => {
    return items.filter(item =>
      item.item_name.toLowerCase().includes(menuItemSearchTerm.toLowerCase())
    )
  }

  const getSelectedMenuItemsList = (items: ApiItem[]) => {
    const filtered = getFilteredMenuItems(items)
    const selected = filtered.filter(item => selectedMenuItems.has(item.id))

    return selected
  }

  const getRemainingMenuItemsList = (items: ApiItem[]) => {
    const filtered = getFilteredMenuItems(items)
    const remaining = filtered.filter(item => !selectedMenuItems.has(item.id))

    return remaining
  }

  return {
    // State
    selectedMenuItems,
    menuItemSearchTerm,
    selectedMenuSectionOpen,
    remainingMenuSectionOpen,

    // Actions
    setMenuItemSearchTerm,
    setSelectedMenuSectionOpen,
    setRemainingMenuSectionOpen,
    handleMenuItemToggle,
    handleConfirmMenuItems,
    resetSelection,
    setSelectedMenuItemsFromGroup,

    // Computed
    getFilteredMenuItems,
    getSelectedMenuItemsList,
    getRemainingMenuItemsList,
    hasSelectedItems: selectedMenuItems.size > 0
  }
}
