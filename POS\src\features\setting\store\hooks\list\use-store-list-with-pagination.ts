import { useState, useMemo } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { Store } from '@/types/store'

import { useCitiesData } from '@/hooks/api/use-removed-items'

import { STORE_CONSTANTS } from '../../data'
import { useCitiesTransform, type TransformedCity } from './use-cities-transform'
import { useStoreFiltering } from './use-store-filtering'
import { useStoreListState } from './use-store-list-state'
import { useStoresWithPagination } from './use-stores-with-pagination'

export interface UseStoreListWithPaginationReturn {
  // Data
  stores: Store[]
  filteredStores: Store[]
  cities: TransformedCity[]
  
  // Loading states
  isLoading: boolean
  citiesLoading: boolean
  error: any
  
  // Pagination
  currentPage: number
  setCurrentPage: (page: number) => void
  hasNextPage: boolean
  total?: number
  
  // Filters
  searchTerm: string
  setSearchTerm: (term: string) => void
  selectedCity: string
  setSelectedCity: (city: string) => void
  
  handleCreateStore: () => void
  handleSyncSecondaryScreen: () => void
  
  syncModalOpen: boolean
  setSyncModalOpen: (open: boolean) => void
}

export function useStoreListWithPagination(): UseStoreListWithPaginationReturn {
  const navigate = useNavigate()

  const [syncModalOpen, setSyncModalOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)

  const { searchTerm, setSearchTerm, selectedCity, setSelectedCity } = useStoreListState()

  const resetPage = () => setCurrentPage(1)

  const filterParams = useMemo(() => ({
    page: currentPage,
    limit: 50
  }), [currentPage])

  const { 
    data: stores = [], 
    isLoading, 
    error, 
    hasNextPage, 
    total 
  } = useStoresWithPagination({
    params: filterParams
  })

  const { data: citiesData = [], isLoading: citiesLoading } = useCitiesData()

  const { cities } = useCitiesTransform(citiesData)

  const { filteredStores } = useStoreFiltering({
    stores,
    searchTerm,
    selectedCity
  })

  const handleCreateStore = () => {
    navigate({ to: STORE_CONSTANTS.ROUTE_STORE_DETAIL })
  }

  const handleSyncSecondaryScreen = () => {
    setSyncModalOpen(true)
  }

  const setSearchTermWithReset = (term: string) => {
    setSearchTerm(term)
    resetPage()
  }

  const setSelectedCityWithReset = (city: string) => {
    setSelectedCity(city)
    resetPage()
  }

  return {
    stores,
    filteredStores,
    cities,
    isLoading,
    citiesLoading,
    error,
    currentPage,
    setCurrentPage,
    hasNextPage,
    total,
    searchTerm,
    setSearchTerm: setSearchTermWithReset,
    selectedCity,
    setSelectedCity: setSelectedCityWithReset,
    handleCreateStore,
    handleSyncSecondaryScreen,
    syncModalOpen,
    setSyncModalOpen
  }
}
