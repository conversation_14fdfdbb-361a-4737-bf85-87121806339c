import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'
import { useCurrentBrand } from '@/stores/posStore'

import { apiClient } from '@/lib/api/pos/pos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface UseMenuItemsProps {
  storeId?: string
  enabled?: boolean
}

export const useMenuItems = ({ storeId, enabled = true }: UseMenuItemsProps) => {
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const menuItemsQuery = useQuery({
    queryKey: [QUERY_KEYS.MENU_ITEMS, storeId, company?.id, selectedBrand?.id],
    queryFn: async (): Promise<any[]> => {
      if (!company?.id || !selectedBrand?.id || !storeId) {
        return []
      }

      const queryParams = new URLSearchParams({
        skip_limit: 'true',
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        store_uid: storeId,
        item_changed: '1',
        apply_with_store: '-1'
      })

      const response = await apiClient.get(`/mdata/v1/items?${queryParams.toString()}`)

      if (!response.data?.data) {
        return []
      }

      const apiItems = response.data.data as any[]

      return apiItems.map(item => {
        let status = 'Không có thay đổi'

        if (item.item_on_city?.ots_price && item.item_on_city?.ots_price !== (item.ots_price || 0)) {
          status = `Sửa từ món gốc - Giá gốc: ${(item.item_on_city.ots_price || 0).toLocaleString('vi-VN')} ₫`
        } else if (item.apply_with_store === 2) {
          status = 'Món mới'
        }

        return { ...item, status }
      })
    },
    enabled: enabled && !!storeId && !!company?.id && !!selectedBrand?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })

  const { data: menuItems, isLoading, isFetching, isError, error, refetch } = menuItemsQuery

  return {
    menuItems: menuItems || [],
    isLoading,
    isFetching,
    isError,
    error,
    refetch
  }
}
