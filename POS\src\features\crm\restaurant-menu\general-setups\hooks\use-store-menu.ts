import { useState, useMemo } from 'react'
import type { Store, StoreFilters } from '../types'

const mockStores: Store[] = [
  {
    id: '121956',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    phone: '0772593986',
    address: '146 <PERSON><PERSON><PERSON>, Phườ<PERSON>, T<PERSON>',
    lastSalesDataUpdate: '2024-01-15 10:30:00',
    partnerDriverPhone: '0772593987',
    active: true,
    onlineSales: true,
    deliverySales: false,
    onlineReservation: true,
    emailList: ['<EMAIL>', '<EMAIL>'],
    banner: 'https://via.placeholder.com/800x200/0066cc/ffffff?text=Tuti<PERSON>+Banner'
  },
  {
    id: '122423',
    name: '<PERSON>ều sữa 2',
    phone: '333',
    address: 'Win<PERSON><PERSON>, Đường <PERSON>, Ph<PERSON><PERSON>ng Ngh<PERSON>, Cau Giay District, Hà <PERSON>, 11150, Vietnam',
    lastSalesDataUpdate: '2024-01-14 15:45:00',
    partnerDriverPhone: '0334567890',
    active: false,
    onlineSales: false,
    deliverySales: true,
    onlineReservation: false,
    emailList: ['<EMAIL>'],
    banner: ''
  },
  {
    id: '123672',
    name: 'SEKAI-Đông Bắc 2',
    phone: '0961826917',
    address: 'Co.opmart Bình Tân 2, 819 Hương Lộ 2, Phường Bình Trị Đông A, Quận Bình Tân, Hồ Chí Minh, Việt Nam',
    lastSalesDataUpdate: '2024-01-13 09:20:00'
  },
  {
    id: '123674',
    name: 'dsdasd',
    phone: '0392321312',
    address: 'Phan Đăng Lưu, Yên Viên, Gia Lâm, Hà Nội, Việt Nam',
    lastSalesDataUpdate: '2024-01-12 14:15:00'
  },
  {
    id: '123676',
    name: '2323',
    phone: '09323123',
    address: 'Bình Lợi, Bình Chánh, Hồ Chí Minh, Việt Nam',
    lastSalesDataUpdate: '2024-01-11 11:30:00'
  },
  {
    id: '123678',
    name: 'czxczxc',
    phone: '083232324',
    address: 'Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam',
    lastSalesDataUpdate: '2024-01-10 16:45:00'
  },
  {
    id: '123678',
    name: 'czxczxc',
    phone: '083232324',
    address: 'Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam',
    lastSalesDataUpdate: '2024-01-10 16:45:00'
  },
  {
    id: '123678',
    name: 'czxczxc',
    phone: '083232324',
    address: 'Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam',
    lastSalesDataUpdate: '2024-01-10 16:45:00'
  },
  {
    id: '123678',
    name: 'czxczxc',
    phone: '083232324',
    address: 'Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam',
    lastSalesDataUpdate: '2024-01-10 16:45:00'
  },
  {
    id: '123678',
    name: 'czxczxc',
    phone: '083232324',
    address: 'Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam',
    lastSalesDataUpdate: '2024-01-10 16:45:00'
  },
  {
    id: '123678',
    name: 'czxczxc',
    phone: '083232324',
    address: 'Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam',
    lastSalesDataUpdate: '2024-01-10 16:45:00'
  },
  {
    id: '123678',
    name: 'czxczxc',
    phone: '083232324',
    address: 'Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam',
    lastSalesDataUpdate: '2024-01-10 16:45:00'
  },
  {
    id: '123678',
    name: 'czxczxc',
    phone: '083232324',
    address: 'Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam',
    lastSalesDataUpdate: '2024-01-10 16:45:00'
  },
]

export const useStoreMenu = () => {
  const [filters, setFilters] = useState<StoreFilters>({
    search: ''
  })

  const filteredStores = useMemo(() => {
    return mockStores.filter(store => {
      const searchLower = filters.search.toLowerCase()
      return (
        store.id.toLowerCase().includes(searchLower) ||
        store.name.toLowerCase().includes(searchLower) ||
        store.phone.includes(searchLower) ||
        store.address.toLowerCase().includes(searchLower)
      )
    })
  }, [filters.search])

  const updateFilters = (newFilters: Partial<StoreFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  return {
    stores: filteredStores,
    filters,
    updateFilters,
    isLoading: false,
    error: null
  }
}
