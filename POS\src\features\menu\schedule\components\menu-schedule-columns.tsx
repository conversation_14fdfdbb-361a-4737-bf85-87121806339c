import type { ColumnDef } from '@tanstack/react-table'

import { DataTableColumnHeader } from '@/components/data-table'

import { MenuSchedule } from '../data'
import { MenuScheduleRowActions } from './menu-schedule-row-actions'

export const columns: ColumnDef<
  MenuSchedule & {
    id?: number
    city_name?: string
    store_name?: string
    time_formatted?: string
    end_time_formatted?: string
  }
>[] = [
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => <div className='w-[50px]'>{row.index + 1}</div>,
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    accessorKey: 'time_formatted',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Từ ngày' />,
    cell: ({ row }) => <div className='text-sm font-medium'>{row.getValue('time_formatted')}</div>,
    enableSorting: true,
    enableHiding: false
  },
  {
    accessorKey: 'end_time_formatted',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Đến ngày' />,
    cell: ({ row }) => (
      <div className='text-sm font-medium'>{row.getValue('end_time_formatted')}</div>
    ),
    enableSorting: true,
    enableHiding: false
  },
  {
    accessorKey: 'city_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Thành phố' />,
    cell: ({ row }) => <div className='text-sm'>{row.getValue('city_name')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'store_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Cửa hàng' />,
    cell: ({ row }) => <div className='text-sm'>{row.getValue('store_name')}</div>,
    enableSorting: false,
    enableHiding: true
  },

  {
    id: 'actions',
    cell: ({ row }) => <MenuScheduleRowActions row={row} />,
    enableSorting: false,
    enableHiding: false
  }
]
