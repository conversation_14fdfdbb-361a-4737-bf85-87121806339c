import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

import { Card, CardContent } from '@/components/ui/card'

import type { ItemsInStore } from '../../data'

interface SortableMenuItemProps {
  item: ItemsInStore
}

export function SortableMenuItem({ item }: SortableMenuItemProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: item.id as string
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 1
  }

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <Card className='cursor-move transition-shadow hover:shadow-md'>
        <CardContent className='p-3'>
          <div className='mb-2 flex aspect-square items-center justify-center rounded-md bg-gray-200'>
            {item.image_path ? (
              <img src={item.image_path} alt={item.item_name} className='h-full w-full rounded-md object-cover' />
            ) : (
              <span className='text-2xl text-gray-400'>🍽️</span>
            )}
          </div>
          <h4
            className='mb-1 overflow-hidden text-sm font-medium text-ellipsis'
            style={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical'
            }}
          >
            {item.item_name}
          </h4>
          <p className='text-sm text-gray-600'>{item.ots_price?.toLocaleString('vi-VN')} đ</p>
        </CardContent>
      </Card>
    </div>
  )
}
