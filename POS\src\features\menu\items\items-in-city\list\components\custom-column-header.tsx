import { ArrowDownIcon, ArrowUpIcon, CaretSortIcon } from '@radix-ui/react-icons'
import { Column } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

interface CustomColumnHeaderProps<TData, TValue>
  extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>
  title: string
  defaultSort?: 'asc' | 'desc' // Default sort direction
}

export function CustomColumnHeader<TData, TValue>({
  column,
  title,
  className,
  defaultSort = 'desc'
}: CustomColumnHeaderProps<TData, TValue>) {
  if (!column.getCanSort()) {
    return <div className={cn(className)}>{title}</div>
  }

  const handleSort = () => {
    const currentSort = column.getIsSorted()
    
    if (!currentSort) {
      // First click: set to default sort
      column.toggleSorting(defaultSort === 'desc')
    } else if (currentSort === 'desc') {
      // If currently desc, switch to asc
      column.toggleSorting(false)
    } else {
      // If currently asc, switch to desc
      column.toggleSorting(true)
    }
  }

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <Button
        variant='ghost'
        size='sm'
        className='-ml-3 h-8 hover:bg-accent'
        onClick={handleSort}
      >
        <span>{title}</span>
        {column.getIsSorted() === 'desc' ? (
          <ArrowDownIcon className='ml-2 h-4 w-4' />
        ) : column.getIsSorted() === 'asc' ? (
          <ArrowUpIcon className='ml-2 h-4 w-4' />
        ) : (
          <CaretSortIcon className='ml-2 h-4 w-4' />
        )}
      </Button>
    </div>
  )
}
