import { Button, Input } from '@/components/ui'

interface SearchBarProps {
  value: string
  onChange: (value: string) => void
  onFilter?: () => void
  placeholder?: string
}

export const SearchBar = ({ 
  value, 
  onChange, 
  onFilter, 
  placeholder = 'Tên / ID nhà hàng' 
}: SearchBarProps) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
  }

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onFilter?.()
    }
  }

  return (
    <div className='flex items-center gap-2 mb-6'>
      <div className='flex-1 max-w-md'>
        <Input
          type='text'
          placeholder={placeholder}
          value={value}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          className='w-full'
        />
      </div>
      <Button 
        onClick={onFilter}
        variant='default'
        className='bg-orange-500 hover:bg-orange-600 text-white px-6'
      >
        Lọc
      </Button>
    </div>
  )
}
