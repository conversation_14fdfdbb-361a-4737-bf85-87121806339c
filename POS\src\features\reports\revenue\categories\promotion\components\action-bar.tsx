import React from 'react'

import { Download } from 'lucide-react'

import { But<PERSON>, DateRangePicker } from '@/components/ui'

import { usePromotionContext } from '../context'
import { StoreSelectionDropdown } from './store-selection-dropdown'

export const ActionBar: React.FC = () => {
  const {
    dateRange,
    setDateRange,
    selectedCities,
    selectedStores,
    setSelectedCities,
    setSelectedStores,
    compareCities,
    compareStores,
    setCompareCities,
    setCompareStores,
    handleUpdateDateRange,
    handleExport
  } = usePromotionContext()

  const hasFirstSelection = selectedCities.length > 0 || selectedStores.length > 0

  return (
    <div className='flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between'>
      <div className='flex flex-col gap-4 md:flex-row md:gap-6'>
        <DateRangePicker
          initialDateFrom={dateRange.from}
          initialDateTo={dateRange.to}
          onUpdate={({ range }) => {
            if (range?.from && range?.to) {
              setDateRange({ from: range.from, to: range.to })
              handleUpdateDateRange()
            }
          }}
          align='start'
          locale='vi-VN'
        />

        <StoreSelectionDropdown
          selectedCities={selectedCities}
          selectedStores={selectedStores}
          onCitiesChange={setSelectedCities}
          onStoresChange={setSelectedStores}
          className='h-[40px] min-w-[200px]'
        />

        {hasFirstSelection && (
          <>
            <div className='text-muted-foreground flex items-center text-sm'>so với</div>
            <StoreSelectionDropdown
              selectedCities={compareCities}
              selectedStores={compareStores}
              onCitiesChange={setCompareCities}
              onStoresChange={setCompareStores}
              className='h-[40px] min-w-[200px]'
              excludeCities={selectedCities}
              excludeStores={selectedStores}
            />
          </>
        )}
      </div>

      <Button variant='outline' size='sm' onClick={() => handleExport('selected')}>
        <Download className='mr-2 h-4 w-4' />
        Xuất báo cáo
      </Button>
    </div>
  )
}
