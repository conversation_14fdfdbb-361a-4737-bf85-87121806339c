import type { UseFormReturn } from 'react-hook-form'

import type { Store as ApiStore } from '@/types/auth'
import type { ItemClass } from '@/types/item-class'

import type { ItemType } from '@/lib/item-types-api'
import type { Unit } from '@/lib/units-api'

import { ItemBasicInfo } from './item-basic-info'
import { ItemConfiguration } from './item-configuration'

interface Props {
  form: UseFormReturn<any>
  mode: 'create' | 'update'
  itemTypes: ItemType[]
  itemClasses: ItemClass[]
  units: Unit[]
  stores: ApiStore[]
  imageFile?: File | null
  onImageChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  imagePreview?: string | null
  onImageRemove?: () => void
}

export function ItemFormSections({
  form,
  mode,
  itemTypes,
  itemClasses,
  units,
  stores,
  onImageChange,
  imagePreview,
  onImageRemove
}: Props) {
  return (
    <div className='space-y-6'>
      <ItemBasicInfo
        form={form}
        mode={mode}
        itemTypes={itemTypes}
        itemClasses={itemClasses}
        units={units}
        stores={stores}
        onImageChange={onImageChange}
        imagePreview={imagePreview}
        onImageRemove={onImageRemove}
      />

      <ItemConfiguration form={form} />
    </div>
  )
}
