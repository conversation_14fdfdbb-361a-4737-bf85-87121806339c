import type { UseFormReturn } from 'react-hook-form'

import { CityData } from '@/types'
import type { ItemClass } from '@/types/item-class'

import type { ItemType } from '@/lib/item-types-api'
import type { Unit } from '@/lib/units-api'

import { ItemBasicInfo } from './item-basic-info'
import { ItemConfiguration } from './item-configuration'

interface Props {
  form: UseFormReturn<any>
  itemTypes: ItemType[]
  itemClasses: ItemClass[]
  units: Unit[]
  cities: CityData[]
  imageFile?: File | null
  onImageChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  imagePreview?: string | null
  onImageRemove?: () => void
}

export function ItemFormSections({
  form,
  itemTypes,
  itemClasses,
  units,
  cities,
  onImageChange,
  imagePreview,
  onImageRemove
}: Props) {
  return (
    <div className='space-y-6'>
      <ItemBasicInfo
        form={form}
        itemTypes={itemTypes}
        itemClasses={itemClasses}
        units={units}
        cities={cities}
        onImageChange={onImageChange}
        imagePreview={imagePreview}
        onImageRemove={onImageRemove}
      />

      <ItemConfiguration form={form} />
    </div>
  )
}
