import { UseFormReturn } from 'react-hook-form'

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RadioGroup,
  RadioGroupItem
} from '@/components/ui'

import { CrmSettingsFormValues } from '../../data'
import { useSettingsApi } from '../../hooks/use-settings-api'
import { EmailTagsInput } from '../email-tags-input'

interface OnlineOrderAlertSectionProps {
  form: UseFormReturn<CrmSettingsFormValues>
  isLoading?: boolean
}

export function OnlineOrderAlertSection({ form, isLoading = false }: OnlineOrderAlertSectionProps) {
  const onlineOrderEmailType = form.watch('onlineOrderEmailType')
  const { saveOnlineOrderAlert, isLoading: isSaving } = useSettingsApi()

  const handleSave = async () => {
    const values = form.getValues()
    await saveOnlineOrderAlert({
      onlineOrderEmailType: values.onlineOrderEmailType,
      onlineOrderCustomEmails: values.onlineOrderCustomEmails
    })
  }
  return (
    <Card className='shadow-sm'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          CẢNH BÁO ĐƠN HÀNG ONLINE GỬI XUỐNG POS LỖI
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-6 p-6'>
        {/* Email nhận thông báo */}
        <FormField
          control={form.control}
          name='onlineOrderEmailType'
          render={({ field }) => (
            <FormItem>
              <div className='flex w-full flex-col gap-4'>
                <FormLabel className='w-full text-sm font-medium'>Email nhận thông báo</FormLabel>
                <FormControl>
                  <RadioGroup
                    value={field.value}
                    onValueChange={field.onChange}
                    className='flex gap-6'
                    disabled={isLoading}
                  >
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='regular' id='online-regular' />
                      <label htmlFor='online-regular' className='text-sm'>
                        Email thường hiệu
                      </label>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='point' id='online-point' />
                      <label htmlFor='online-point' className='text-sm'>
                        Email điểm bán
                      </label>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='other' id='online-other' />
                      <label htmlFor='online-other' className='text-sm'>
                        Email khác
                      </label>
                    </div>
                  </RadioGroup>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Conditional Email Input for Custom Emails */}
        {onlineOrderEmailType === 'other' && (
          <FormField
            control={form.control}
            name='onlineOrderCustomEmails'
            render={({ field }) => (
              <FormItem>
                <div className='flex flex-col gap-4'>
                  <FormControl>
                    <EmailTagsInput
                      value={field.value || []}
                      onChange={field.onChange}
                      disabled={isLoading}
                      className='w-96'
                      placeholder='Nhập email và ấn Enter'
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <div className='flex justify-start'>
          <Button type='button' disabled={isLoading || isSaving} onClick={handleSave}>
            {isSaving ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
