// Types
export interface SaleNotSyncVatListProps {
  dateRange: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  sourceId?: number
  pageSize?: number
  className?: string
  showStoreInfo?: boolean
  showEmployeeInfo?: boolean
  showPaymentMethod?: boolean
  showPagination?: boolean
  paymentMethodId?: string // Add payment method filter
}

// Format currency in Vietnamese style
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('vi-VN').format(amount)
}

// Format date
export function formatDate(timestamp: number): string {
  return new Date(timestamp).toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Format percentage
export function formatPercentage(value: number): string {
  return `${value.toFixed(1)}%`
}

// Get payment method badge variant
export function getPaymentMethodBadgeVariant(
  paymentMethod: string
): 'default' | 'secondary' | 'destructive' | 'outline' {
  const method = paymentMethod.toLowerCase()

  if (method.includes('tiền mặt') || method.includes('cod')) return 'default'
  if (method.includes('chuyển khoản') || method.includes('bank'))
    return 'secondary'
  if (method.includes('thẻ') || method.includes('card')) return 'outline'

  return 'outline'
}

// Get table service badge variant
export function getTableServiceBadgeVariant(
  tableName: string
): 'default' | 'secondary' | 'destructive' | 'outline' {
  const name = tableName.toLowerCase()

  if (name.includes('tại chỗ')) return 'destructive'
  if (name.includes('mang về')) return 'secondary'
  if (name.includes('giao hàng')) return 'outline'

  return 'default'
}
