import { PosModal } from '@/components/pos'
import { Input, Label, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui'

interface CreateGroupModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel: () => void
  onConfirm: () => void
  onAddMenuItem: () => void
  isEditing: boolean
  customizationName: string
  groupName: string
  setGroupName: (name: string) => void
  minRequired: string
  setMinRequired: (value: string) => void
  maxAllowed: string
  setMaxAllowed: (value: string) => void
  menuItems: Array<{
    id: string
    name: string
    price: number
  }>
}

export function CreateGroupModal({
  open,
  onOpenChange,
  onCancel,
  onConfirm,
  onAddMenuItem,
  isEditing,
  customizationName,
  groupName,
  setGroupName,
  minRequired,
  setMinRequired,
  maxAllowed,
  setMaxAllowed,
  menuItems
}: CreateGroupModalProps) {
  return (
    <PosModal
      title={`${isEditing ? 'Sửa' : 'Tạo'} nhóm cho customization ${customizationName || ''}`}
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm}
      confirmText='Lưu'
      cancelText='Hủy'
      maxWidth='sm:max-w-2xl'
    >
      <div className='space-y-4'>
        <div>
          <Input placeholder='Tên nhóm' value={groupName} onChange={e => setGroupName(e.target.value)} />
        </div>

        <div className='space-y-4'>
          <div className='flex items-center gap-4'>
            <Label htmlFor='min-required' className='min-w-[80px] text-sm font-medium'>
              Yêu cầu
            </Label>
            <Input
              id='min-required'
              type='number'
              value={minRequired}
              onChange={e => {
                let nextMin = Number(e.target.value || 0)
                if (nextMin > 1000) nextMin = 1000
                if (nextMin < 0) nextMin = 0
                const prevMin = Number(minRequired || 0)
                const delta = nextMin - prevMin
                setMinRequired(String(nextMin))
                {
                  const prevMax = Number(maxAllowed || 0)
                  let nextMax = prevMax + delta
                  if (nextMax < 0) nextMax = 0
                  if (nextMax > 1000) nextMax = 1000
                  setMaxAllowed(String(nextMax))
                }
              }}
              min='0'
              max='1000'
              className='flex-1'
            />
          </div>
          <div className='flex items-center gap-4'>
            <Label htmlFor='max-allowed' className='min-w-[80px] text-sm font-medium'>
              Tối đa
            </Label>
            <Input
              id='max-allowed'
              type='number'
              value={maxAllowed}
              onChange={e => {
                let v = Number(e.target.value || 0)
                if (v < 0) v = 0
                if (v > 1000) v = 1000
                setMaxAllowed(String(v))
              }}
              min='0'
              max='1000'
              className='flex-1'
            />
          </div>
        </div>

        <div className='space-y-3'>
          <h4 className='text-sm font-medium'>Danh sách món</h4>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tên</TableHead>
                  <TableHead>Giá</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {menuItems.map(item => (
                  <TableRow key={item.id}>
                    <TableCell>{item.name}</TableCell>
                    <TableCell>{item.price.toLocaleString('vi-VN')} đ</TableCell>
                  </TableRow>
                ))}
                <TableRow className='cursor-pointer hover:bg-gray-50' onClick={onAddMenuItem}>
                  <TableCell colSpan={2} className='py-4 text-center'>
                    <span className='font-medium text-blue-600'>Thêm món</span>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </PosModal>
  )
}
