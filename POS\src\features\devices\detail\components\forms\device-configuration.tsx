import { Check, ChevronsUpDown } from 'lucide-react'

import { cn } from '@/lib/utils'

import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

import { Button, Input, Label, Checkbox, RadioGroup, RadioGroupItem } from '@/components/ui'

import { DEVICE_TYPE_LOCAL_OPTIONS, KDS_NOTIFICATION_OPTIONS } from '../../constants/device-form-constants'
import { DeviceFormData } from '../../types'

interface DeviceConfigurationProps {
  formData: DeviceFormData
  setFormData: (data: DeviceFormData | ((prev: DeviceFormData) => DeviceFormData)) => void
  allowEditIpServer: boolean
  setAllowEditIpServer: (allow: boolean) => void
  deviceData: any
  isEditMode: boolean
  openDeviceTypeLocal: boolean
  setOpenDeviceTypeLocal: (open: boolean) => void
  openKdsNotification: boolean
  setOpenKdsNotification: (open: boolean) => void
  selectedGroups: Set<string>
  selectedCombos: Set<string>
  onOpenGroupSelection: () => void
}

export function DeviceConfiguration({
  formData,
  setFormData,
  allowEditIpServer,
  setAllowEditIpServer,
  deviceData,
  isEditMode,
  openDeviceTypeLocal,
  setOpenDeviceTypeLocal,
  openKdsNotification,
  setOpenKdsNotification,
  selectedGroups,
  selectedCombos,
  onOpenGroupSelection
}: DeviceConfigurationProps) {
  const getTotalSelectedCount = () => {
    if (selectedGroups.size === 0 && selectedCombos.size === 0) {
      return deviceData?.extra_data?.item_type_ignore?.length || 0
    }

    return selectedGroups.size + selectedCombos.size
  }
  return (
    <div className='space-y-6'>
      <h3 className='text-lg font-semibold text-gray-900'>Cấu hình thiết bị</h3>
      <div className='space-y-4'>
        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-right font-medium'>Loại máy</Label>
          <div className='col-span-2'>
            <Popover open={openDeviceTypeLocal} onOpenChange={setOpenDeviceTypeLocal}>
              <PopoverTrigger asChild>
                <Button
                  variant='outline'
                  role='combobox'
                  aria-expanded={openDeviceTypeLocal}
                  className='w-full justify-between text-blue-500'
                >
                  {formData.deviceTypeLocal || 'Chọn loại máy'}
                  <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                </Button>
              </PopoverTrigger>
              <PopoverContent className='w-full p-0'>
                <Command>
                  <CommandInput placeholder='Tìm kiếm loại máy...' />
                  <CommandList>
                    <CommandEmpty>Không tìm thấy loại máy.</CommandEmpty>
                    <CommandGroup>
                      {DEVICE_TYPE_LOCAL_OPTIONS.map(option => (
                        <CommandItem
                          className='text-blue-500'
                          key={option.value}
                          value={option.value}
                          onSelect={() => {
                            setFormData(prev => ({ ...prev, deviceTypeLocal: option.value }))
                            setOpenDeviceTypeLocal(false)
                          }}
                        >
                          <Check
                            className={cn(
                              'mr-2 h-4 w-4',
                              formData.deviceTypeLocal === option.value ? 'opacity-100' : 'opacity-0'
                            )}
                          />
                          {option.label}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {formData.deviceTypeLocal !== 'Máy chủ' && formData.deviceTypeLocal !== 'None' && (
          <div className='grid grid-cols-3 items-center gap-4'>
            <Label className='text-right font-medium text-gray-700'>Địa chỉ IP máy chủ</Label>
            <div className='col-span-2 flex items-center gap-3'>
              {isEditMode ? (
                <Input
                  value={
                    allowEditIpServer
                      ? formData.newIpAddress
                      : (deviceData as any)?.ip_local_server || (deviceData as any)?.ipAddress || ''
                  }
                  disabled={!allowEditIpServer}
                  onChange={e => setFormData(prev => ({ ...prev, newIpAddress: e.target.value }))}
                  className={allowEditIpServer ? '' : 'cursor-not-allowed bg-gray-50 font-medium text-gray-600'}
                  placeholder={allowEditIpServer ? 'Nhập địa chỉ IP máy chủ mới' : ''}
                />
              ) : (
                <Input
                  value={formData.newIpAddress}
                  onChange={e => setFormData(prev => ({ ...prev, newIpAddress: e.target.value }))}
                  placeholder='Nhập địa chỉ IP máy chủ'
                />
              )}
              {isEditMode && (
                <Checkbox
                  checked={allowEditIpServer}
                  onCheckedChange={checked => {
                    setAllowEditIpServer(!!checked)
                    if (checked) {
                      setFormData(prev => ({ ...prev, newIpAddress: '' }))
                    } else {
                      setFormData(prev => ({
                        ...prev,
                        newIpAddress: (deviceData as any)?.ip_local_server || (deviceData as any)?.ipAddress || ''
                      }))
                    }
                  }}
                />
              )}
            </div>
          </div>
        )}

        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-right font-medium text-gray-700'>Hiển thị tab quản lý khu vực</Label>
          <div className='col-span-2'>
            <Checkbox
              checked={formData.enableTabManagement}
              onCheckedChange={checked => setFormData(prev => ({ ...prev, enableTabManagement: !!checked }))}
            />
          </div>
        </div>

        {formData.enableTabManagement && (
          <div className='grid grid-cols-3 items-center gap-4'>
            <Label className='text-right font-medium text-gray-700'>Cấu hình hiển thị khu vực</Label>
            <div className='col-span-2'>
              <RadioGroup
                value={formData.displayColumns}
                onValueChange={value => setFormData(prev => ({ ...prev, displayColumns: value }))}
                className='flex space-x-6'
              >
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='5' id='col5' />
                  <Label htmlFor='col5'>5 cột</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='6' id='col6' />
                  <Label htmlFor='col6'>6 cột</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='7' id='col7' />
                  <Label htmlFor='col7'>7 cột</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='8' id='col8' />
                  <Label htmlFor='col8'>8 cột</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
        )}

        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-right font-medium text-gray-700'>Kích hoạt màn hình 2</Label>
          <div className='col-span-2'>
            <Checkbox
              checked={formData.enableScreen2}
              onCheckedChange={checked => setFormData(prev => ({ ...prev, enableScreen2: !!checked }))}
            />
          </div>
        </div>

        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-right font-medium text-gray-700'>Dùng mẫu in tem của máy chủ</Label>
          <div className='col-span-2'>
            <Checkbox
              checked={formData.useItemInStore}
              onCheckedChange={checked => setFormData(prev => ({ ...prev, useItemInStore: !!checked }))}
            />
          </div>
        </div>

        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-left font-medium'>Cấu hình hiện thông báo KDS</Label>
          <div className='col-span-2'>
            <Popover open={openKdsNotification} onOpenChange={setOpenKdsNotification}>
              <PopoverTrigger asChild>
                <Button
                  variant='outline'
                  role='combobox'
                  aria-expanded={openKdsNotification}
                  className='w-full justify-between text-blue-500'
                >
                  {formData.kdsNotificationConfig || 'Chọn cấu hình thông báo KDS'}
                  <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                </Button>
              </PopoverTrigger>
              <PopoverContent className='w-full p-0'>
                <Command>
                  <CommandInput placeholder='Tìm kiếm cấu hình...' />
                  <CommandList>
                    <CommandEmpty>Không tìm thấy cấu hình.</CommandEmpty>
                    <CommandGroup>
                      {KDS_NOTIFICATION_OPTIONS.map(option => (
                        <CommandItem
                          className='text-blue-500'
                          key={option.value}
                          value={option.value}
                          onSelect={() => {
                            setFormData(prev => ({ ...prev, kdsNotificationConfig: option.value }))
                            setOpenKdsNotification(false)
                          }}
                        >
                          <Check
                            className={cn(
                              'mr-2 h-4 w-4',
                              formData.kdsNotificationConfig === option.value ? 'opacity-100' : 'opacity-0'
                            )}
                          />
                          {option.label}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-right font-medium text-gray-700'>Hiển thị nút mở két ở POS</Label>
          <div className='col-span-2'>
            <Checkbox
              checked={formData.enablePosNutMode}
              onCheckedChange={checked => setFormData(prev => ({ ...prev, enablePosNutMode: !!checked }))}
            />
          </div>
        </div>

        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-right font-medium text-gray-700'>Cấu hình ký tự đặc biệt</Label>
          <div className='col-span-2'>
            <Input
              value={formData.specialConfigType}
              onChange={e => setFormData(prev => ({ ...prev, specialConfigType: e.target.value }))}
              placeholder='Nhập cấu hình ký tự đặc biệt'
            />
          </div>
        </div>

        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-left font-medium'>Ẩn nhóm hoặc combo không hiển thị trên thiết bị</Label>
          <div className='col-span-2'>
            <Button
              type='button'
              variant='outline'
              onClick={onOpenGroupSelection}
              className='w-full justify-start text-left text-blue-500'
            >
              {getTotalSelectedCount() > 0 ? `${getTotalSelectedCount()} nhóm hoặc combo` : '0 nhóm hoặc combo'}
            </Button>
          </div>
        </div>

        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-right font-medium text-gray-700'>Giao diện mới ở tab nhà hàng</Label>
          <div className='col-span-2'>
            <Checkbox
              checked={formData.enableTabDisplay}
              onCheckedChange={checked => setFormData(prev => ({ ...prev, enableTabDisplay: !!checked }))}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
