import { Checkbox, FormLabel } from '@/components/ui'

interface Permission {
  id: string
  name: string
}

interface PermissionCategory {
  name: string
  permissions: Permission[]
}

interface PermissionSectionProps {
  categories: PermissionCategory[]
  isPermissionSelected: (permissionId: string) => boolean
  onPermissionChange: (permissionId: string, isChecked: boolean) => void
  hasError?: boolean
  errorMessage?: string
}

const PermissionItem = ({ 
  permission, 
  isSelected, 
  onChange 
}: { 
  permission: Permission
  isSelected: boolean
  onChange: (isChecked: boolean) => void
}) => (
  <div className='flex items-center space-x-2'>
    <Checkbox
      id={permission.id}
      checked={isSelected}
      onCheckedChange={onChange}
    />
    <label
      htmlFor={permission.id}
      className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
    >
      {permission.name}
    </label>
  </div>
)

const PermissionCategoryGroup = ({ 
  category, 
  isPermissionSelected, 
  onPermissionChange 
}: { 
  category: PermissionCategory
  isPermissionSelected: (permissionId: string) => boolean
  onPermissionChange: (permissionId: string, isChecked: boolean) => void
}) => (
  <div className='space-y-3'>
    <h4 className='text-sm font-medium'>{category.name}</h4>
    <div className='grid grid-cols-1 gap-2 md:grid-cols-2'>
      {category.permissions.map(permission => (
        <PermissionItem
          key={permission.id}
          permission={permission}
          isSelected={isPermissionSelected(permission.id)}
          onChange={(isChecked) => onPermissionChange(permission.id, isChecked as boolean)}
        />
      ))}
    </div>
  </div>
)

export const PermissionSection = ({
  categories,
  isPermissionSelected,
  onPermissionChange,
  hasError,
  errorMessage
}: PermissionSectionProps) => (
  <div>
    <FormLabel className='text-base font-medium'>
      Cài đặt quyền cho tài khoản <span className='text-red-500'>*</span>
    </FormLabel>
    <div className='mt-4 space-y-6'>
      {categories.map(category => (
        <PermissionCategoryGroup
          key={category.name}
          category={category}
          isPermissionSelected={isPermissionSelected}
          onPermissionChange={onPermissionChange}
        />
      ))}
    </div>
    {hasError && errorMessage && (
      <p className='mt-2 text-sm text-destructive'>{errorMessage}</p>
    )}
  </div>
)
