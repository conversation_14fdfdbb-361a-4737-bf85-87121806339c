import { ConfirmDialog } from '@/components/confirm-dialog'

import { useItemsInCity } from '../../../context'
import { useDeleteItemInCity } from '../../../hooks'
import { ExportDialog } from './export-dialog'
import { ImportDialog } from './import-dialog'

export function ItemsInCityDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useItemsInCity()
  const { deleteItemAsync } = useDeleteItemInCity()

  return (
    <>
      <ExportDialog open={open === 'export-dialog'} onOpenChange={() => setOpen(null)} />
      <ImportDialog />

      {currentRow && (
        <>
          <ConfirmDialog
            key='quantity-day-delete'
            destructive
            open={open === 'delete'}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            handleConfirm={async () => {
              setOpen(null)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
              await deleteItemAsync(currentRow.id || '')
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá ?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}
    </>
  )
}
