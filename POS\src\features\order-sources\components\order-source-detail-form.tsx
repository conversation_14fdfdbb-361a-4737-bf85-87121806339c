import { useState, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { generateSourceId } from '@/lib/string-utils'

import {
  useCreateOrderSource,
  useOrderSourceById,
  useUpdateOrderSourceById
} from '@/hooks/api/use-order-sources'

import { SourceCombobox } from '@/components/pos/source-combobox'
import { Button, Checkbox, Input, Label } from '@/components/ui'

import { StoreSelectionModal } from './store-selection-modal'

interface OrderSourceDetailFormProps {
  sourceId?: string
}

export function OrderSourceDetailForm({ sourceId }: OrderSourceDetailFormProps) {
  const navigate = useNavigate()
  const { createOrderSource, isCreating } = useCreateOrderSource()
  const { updateOrderSource, isUpdating } = useUpdateOrderSourceById()

  const isEditMode = !!sourceId

  const { data: existingData, isLoading: isLoadingData } = useOrderSourceById(sourceId)

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    autoGenerateCode: true,
    selectedStores: [] as string[],
    selectedSourceId: ''
  })

  const [showStoreModal, setShowStoreModal] = useState(false)

  useEffect(() => {
    if (existingData && isEditMode) {
      let storeIds: string[] = []
      if (Array.isArray(existingData.stores)) {
        storeIds = existingData.stores.map(store => store.id)
      }

      setFormData({
        name: existingData.source_name || '',
        code: existingData.source_id || '',
        autoGenerateCode: !existingData.source_id,
        selectedStores: storeIds,
        selectedSourceId: existingData.source_id || ''
      })
    }
  }, [existingData, isEditMode])

  const handleBack = () => {
    navigate({ to: '/setting/source' })
  }

  const isFormValid = formData.name.trim() !== '' && formData.selectedStores.length > 0
  const isLoading = isCreating || isUpdating

  const handleSave = async () => {
    if (!isFormValid) return

    // Determine source_id based on selection and checkbox state
    let sourceId: string | undefined
    if (formData.selectedSourceId) {
      // Priority 1: Use selected source_id from suggestion (regardless of checkbox)
      sourceId = formData.selectedSourceId
    } else if (formData.autoGenerateCode) {
      // Priority 2: Checkbox is CHECKED and no selection - generate SOURCE-XXXX format
      sourceId = generateSourceId()
    } else {
      // Priority 3: Checkbox is NOT checked - use user input or let backend generate
      if (formData.code.trim()) {
        sourceId = formData.code.trim() // Use user input
      } else {
        sourceId = undefined // Let backend generate
      }
    }

    const createData = {
      source_name: formData.name,
      source_id: sourceId,
      stores: formData.selectedStores,
      description: null,
      active: 1,
      sort: 1000,
      extra_data: {},
      source_type: [],
      is_fabi: 1,
      partner_config: 0
    }

    if (isEditMode) {
      updateOrderSource(createData, {
        onSuccess: () => {
          navigate({ to: '/setting/source' })
        }
      })
    } else {
      createOrderSource(createData, {
        onSuccess: () => {
          navigate({ to: '/setting/source' })
        }
      })
    }
  }

  const handleStoreSelection = () => {
    setShowStoreModal(true)
  }

  const handleStoreSelectionChange = (storeIds: string[]) => {
    setFormData({ ...formData, selectedStores: storeIds })
  }

  const handleSourceNameChange = (sourceName: string) => {
    setFormData({ ...formData, name: sourceName, selectedSourceId: '' })
  }

  const handleSourceSelect = (source: { source_id: string; source_name: string }) => {
    setFormData({
      ...formData,
      name: source.source_name,
      code: source.source_id, // Map source_id to the code input field
      selectedSourceId: source.source_id
    })
  }

  // Show loading state when fetching existing data
  if (isEditMode && isLoadingData) {
    return (
      <div className='flex min-h-screen items-center justify-center bg-gray-50'>
        <div className='text-center'>
          <div className='mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900'></div>
          <p className='mt-2 text-gray-600'>Đang tải dữ liệu...</p>
        </div>
      </div>
    )
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header */}
      <div className='border-b bg-white px-6 py-4'>
        <div className='mx-auto flex max-w-4xl items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='p-2'>
            <X className='h-4 w-4' />
          </Button>
          <div className='flex-1 text-center'>
            <h1 className='text-xl font-semibold text-gray-900'>
              {isEditMode ? 'Chỉnh sửa nguồn đơn hàng' : 'Tạo nguồn mới'}
            </h1>
          </div>
          <div>
            <Button onClick={handleSave} disabled={!isFormValid || isLoading}>
              {isLoading ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className='mx-auto max-w-4xl'>
        <div className='space-y-6'>
          {/* Section Title */}
          <div>
            <h2 className='text-lg font-medium text-gray-900'>Thông tin chi tiết</h2>
          </div>

          {/* Form Fields */}
          <div className='space-y-6'>
            {/* Tên nguồn - Using SourceCombobox */}
            <div className='flex items-center gap-4'>
              <Label htmlFor='source-name' className='min-w-[200px] text-sm font-medium'>
                Tên nguồn *
              </Label>
              {isEditMode ? (
                <Input
                  value={formData.name}
                  disabled={true}
                  className='flex-1'
                  placeholder='Tên nguồn đơn hàng'
                />
              ) : (
                <SourceCombobox
                  value={formData.name}
                  onValueChange={handleSourceNameChange}
                  onSelectItem={handleSourceSelect}
                  placeholder='Nhập tên nguồn đơn hàng'
                  className='flex-1'
                />
              )}
            </div>

            {/* Cửa hàng áp dụng */}
            <div className='flex items-center gap-4'>
              <Label className='min-w-[200px] text-sm font-medium'>Cửa hàng áp dụng *</Label>
              <Button
                type='button'
                variant='outline'
                onClick={handleStoreSelection}
                className='flex-1 justify-start'
              >
                {formData.selectedStores.length > 0
                  ? `${formData.selectedStores.length} điểm`
                  : '0 điểm'}
              </Button>
            </div>

            {/* Mã nguồn */}
            <div className='flex items-center gap-4'>
              <Label htmlFor='source-code' className='min-w-[200px] text-sm font-medium'>
                Mã nguồn
              </Label>
              <div className='flex flex-1 items-center gap-3'>
                <Input
                  id='source-code'
                  value={formData.code}
                  onChange={e => setFormData({ ...formData, code: e.target.value })}
                  placeholder='Nếu để trống, hệ thống sẽ tự động tạo một mã nguồn'
                  disabled={isEditMode || formData.autoGenerateCode}
                  className='flex-1'
                />
                {!isEditMode && (
                  <Checkbox
                    id='auto-generate-code'
                    checked={formData.autoGenerateCode}
                    onCheckedChange={checked =>
                      setFormData({
                        ...formData,
                        autoGenerateCode: checked as boolean,
                        code: checked ? '' : formData.code
                      })
                    }
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Store Selection Modal */}
      <StoreSelectionModal
        open={showStoreModal}
        onOpenChange={setShowStoreModal}
        selectedStoreIds={formData.selectedStores}
        onStoreSelectionChange={handleStoreSelectionChange}
      />
    </div>
  )
}
