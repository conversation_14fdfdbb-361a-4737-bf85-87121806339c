import { useState, useEffect } from 'react'

import { useCurrentBrand } from '@/stores'
import { Download, Upload } from 'lucide-react'
import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import { useAuthStore } from '@/stores/authStore'

import { api } from '@/lib/api/pos/pos-api'

import { useStoresData, useItemTypesData, useItemClassesData, useUnitsData, useCitiesData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { parseActiveStatus, parseBooleanStatus } from '../../../utils'
import { ImportPreviewDialog } from './excel-preview-export-dialog'
import type { ImportItem } from './excel-preview-export-dialog'

interface ExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ExportDialog({ open, onOpenChange }: ExportDialogProps) {
  const [selectedLocation, setSelectedLocation] = useState<string>('')
  const [selectedGroup, setSelectedGroup] = useState<string>('')
  const [isDownloading, setIsDownloading] = useState(false)
  const [isImportPreviewOpen, setIsImportPreviewOpen] = useState(false)
  const [importData, setImportData] = useState<ImportItem[]>([])

  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const { data: storesData = [] } = useStoresData()
  const { data: itemTypesData = [] } = useItemTypesData({
    ...(selectedLocation && selectedLocation !== 'all' ? { store_uid: selectedLocation } : {})
  })
  const { data: itemClassesData = [] } = useItemClassesData({ skip_limit: true })
  const { data: unitsData = [] } = useUnitsData()
  const { data: citiesData = [] } = useCitiesData()

  const locationOptions = storesData.map(store => ({
    value: store.id,
    label: store.name || store.id
  }))

  const groupOptions = [
    { value: 'all', label: 'Tất cả nhóm món' },
    ...itemTypesData.map(itemType => ({
      value: itemType.id,
      label: itemType.item_type_name || itemType.id
    }))
  ]

  useEffect(() => {
    if (selectedLocation) {
      setSelectedGroup('all')
    }
  }, [selectedLocation])

  const handleDownloadTemplate = async () => {
    try {
      if (!company?.id || !selectedBrand?.id || !selectedLocation) {
        toast.error('Vui lòng chọn cửa hàng trước khi tải file')
        return
      }

      setIsDownloading(true)

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const params: any = {
        skip_limit: 'true',
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        store_uid: selectedLocation
      }

      if (selectedGroup && selectedGroup !== 'all') {
        params.item_type_uid = selectedGroup
      }

      const response = await api.get('/mdata/v1/items', { params })
      const itemsData = Array.isArray(response.data?.data) ? response.data.data : []

      if (itemsData.length === 0) {
        toast.error('Không có dữ liệu để xuất')
        return
      }

      const getStore = (storeId: string) => {
        const store = storesData.find(s => s.id === storeId)
        return { name: store?.name || storeId, id: store?.id || storeId }
      }

      const getCity = (cityId: string) => {
        const city = citiesData.find(c => c.id === cityId)
        return { name: city?.city_name || cityId, id: city?.id || cityId }
      }

      const getItemType = (itemTypeId: string) => {
        const itemType = itemTypesData.find(it => it.id === itemTypeId)
        return {
          name: itemType?.item_type_name || itemTypeId,
          id: itemType?.item_type_id || itemTypeId
        }
      }

      const getItemClass = (itemClassId: string) => {
        const itemClass = itemClassesData.find(ic => ic.id === itemClassId)
        return {
          name: itemClass?.item_class_name || itemClassId,
          id: itemClass?.item_class_id || itemClassId
        }
      }

      const getUnit = (unitId: string) => {
        const unit = unitsData.find(u => u.id === unitId)
        return { name: unit?.unit_name || unitId, id: unit?.unit_id || unitId }
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const excelData = itemsData.map((item: any) => ({
        ID: item.id,
        'Mã món': item.item_id,
        'Thành phố': getCity(item.city_uid).name,
        'Cửa hàng': getStore(item.store_uid).name,
        Tên: item.item_name,
        Giá: item.ots_price,
        'Trạng thái': item.active,
        'Mã barcode': item.item_id_barcode,
        'Món ăn kèm': item.is_eat_with,
        'Không cập nhật số lượng món ăn kèm': item.extra_data?.no_update_quantity_toping,
        'Đơn vị': getUnit(item.unit_uid).id,
        Nhóm: getItemType(item.item_type_uid).id,
        'Tên nhóm': getItemType(item.item_type_uid).name,
        'Loại món': getItemClass(item.item_class_uid).id,
        'Tên loại': getItemClass(item.item_class_uid).name,
        'Mô tả': item.description,
        SKU: '',
        'VAT (%)': item.ots_tax * 100,
        'Thời gian chế biến (phút)': Math.round(item.time_cooking / 60000),
        'Cho phép sửa giá khi bán': item.price_change,
        'Cấu hình món ảo': item.extra_data?.is_virtual_item,
        'Cấu hình món dịch vụ': item.extra_data?.is_item_service,
        'Cấu hình món ăn là vé buffet': item.extra_data?.is_buffet_item,
        Giờ: item.time_sale_hour_day,
        Ngày: item.time_sale_date_week,
        'Thứ tự': item.sort,
        'Hình ảnh': item.image_path,
        'Công thức inQR cho máy pha trà': ''
      }))

      const worksheet = XLSX.utils.json_to_sheet(excelData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Items')

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
      const filename = `items_export_${timestamp}.xlsx`

      XLSX.writeFile(workbook, filename)

      toast.success(`Đã tải xuống file ${filename}`)
    } catch (_error) {
      toast.error('Có lỗi xảy ra khi tải file')
    } finally {
      setIsDownloading(false)
    }
  }

  const handleImportFile = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.xlsx,.xls'
    input.onchange = async e => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        const arrayBuffer = await file.arrayBuffer()
        const workbook = XLSX.read(arrayBuffer, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const transformedData = jsonData.map((row: any) => ({
          id: row.ID || '',
          item_id: row['Mã món'] || '',
          city_name: row['Thành phố'] || '',
          store_name: row['Cửa hàng'] || '',
          item_name: row['Tên'] || '',
          ots_price: row['Giá'] || 0,
          active: parseActiveStatus(row['Trạng thái']),
          item_id_barcode: row['Mã barcode'] || '',
          is_eat_with: parseBooleanStatus(row['Món ăn kèm']),
          no_update_quantity_toping: parseBooleanStatus(row['Không cập nhật số lượng món ăn kèm']),
          unit_id: row['Đơn vị'] || '',
          item_type_id: row['Nhóm'] || '',
          item_type_name: row['Tên nhóm'] || '',
          item_class_id: row['Loại món'] || '',
          item_class_name: row['Tên loại'] || '',
          description: row['Mô tả'] || '',
          sku: row['SKU'] || '',
          ots_tax: (row['VAT (%)'] || 0) / 100,
          time_cooking: (row['Thời gian chế biến (phút)'] || 0) * 60000,
          price_change: parseBooleanStatus(row['Cho phép sửa giá khi bán']),
          is_virtual_item: parseBooleanStatus(row['Cấu hình món ảo']),
          is_item_service: parseBooleanStatus(row['Cấu hình món dịch vụ']),
          is_buffet_item: parseBooleanStatus(row['Cấu hình món ăn là vé buffet']),
          time_sale_hour_day: row['Giờ'] || 0,
          time_sale_date_week: row['Ngày'] || 0,
          list_order: row['Thứ tự'] || 0,
          image_path: row['Hình ảnh'] || '',
          inqr_formula: row['Công thức inQR cho máy pha trà'] || '',
          // Store all original data for reference
          originalData: row
        }))

        setImportData(transformedData)
        setIsImportPreviewOpen(true)
        onOpenChange(false)
      } catch (_error) {
        toast.error('Có lỗi xảy ra khi đọc file Excel')
      }
    }
    input.click()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <DialogTitle className='text-xl font-semibold'>Xuất, sửa thực đơn</DialogTitle>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Bước 1 */}
          <div>
            <h3 className='mb-4 text-lg font-medium'>Bước 1. Chỉnh bộ lọc để xuất file</h3>
            <div className='grid grid-cols-2 gap-4'>
              <div>
                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Chọn cửa hàng' />
                  </SelectTrigger>
                  <SelectContent>
                    {locationOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Select value={selectedGroup} onValueChange={setSelectedGroup}>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Tất cả nhóm món' />
                  </SelectTrigger>
                  <SelectContent>
                    {groupOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Bước 2 */}
          <div>
            <h3 className='mb-4 text-lg font-medium'>Bước 2. Tải file dữ liệu</h3>
            <div className='flex items-center justify-between rounded-lg border p-4'>
              <span className='text-muted-foreground text-sm'>Tải xuống</span>
              <Button
                variant='outline'
                size='icon'
                onClick={handleDownloadTemplate}
                disabled={isDownloading || !selectedLocation}
                className='h-10 w-10'
              >
                <Download className='h-4 w-4' />
              </Button>
            </div>
          </div>

          {/* Bước 3 */}
          <div>
            <h3 className='mb-4 text-lg font-medium'>Bước 3. Thêm cấu hình vào file</h3>
            <div className='space-y-2'>
              <p className='text-muted-foreground text-sm'>Không sửa các cột:</p>
              <p className='font-mono text-sm text-blue-600'>
                ID, Mã món, Tên (Với món sửa từ món gốc), Mô tả, Thành phố, SKU, Cửa hàng, Đơn vị, Tên nhóm, Tên loại.
              </p>
            </div>
          </div>

          {/* Bước 4 */}
          <div>
            <h3 className='mb-4 text-lg font-medium'>Bước 4. Tải file lên</h3>
            <div className='flex items-center justify-between'>
              <p className='text-muted-foreground text-sm'>Sau khi đã điền đầy đủ bạn có thể tải file lên</p>
              <Button onClick={handleImportFile} className='flex items-center gap-2'>
                <Upload className='h-4 w-4' />
                Tải file lên
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>

      <ImportPreviewDialog
        open={isImportPreviewOpen}
        onOpenChange={setIsImportPreviewOpen}
        data={importData}
        storeUid={selectedLocation}
      />
    </Dialog>
  )
}
