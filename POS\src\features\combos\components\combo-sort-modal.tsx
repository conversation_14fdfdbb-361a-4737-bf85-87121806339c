import { useEffect, useMemo, useState } from 'react'

import { useCurrentBrand } from '@/stores'
import type { Package } from '@/types/package-types'

import { useAuthStore } from '@/stores/authStore'

import { usePackagesForSort, useUpdatePackagesSort } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'

const SKELETON_ITEMS_COUNT = 8
const DRAG_DATA_TYPE = 'text/plain'

const MODAL_CONFIG = {
  title: 'Sắp xếp combo',
  confirmText: 'Lưu',
  description: 'Thứ tự hiển thị các combo sẽ được áp dụng tại thiết bị bán hàng'
}

const MESSAGES = {
  loadError: '<PERSON><PERSON> lỗi xảy ra khi tải danh sách combo',
  noSources: 'Không có combo nào để sắp xếp'
}

const STYLES = {
  description: 'text-muted-foreground text-sm',
  errorText: 'text-sm text-red-600',
  emptyState: 'py-8 text-center',
  grid: 'grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4',
  draggableItem:
    'flex aspect-square cursor-move items-center justify-center bg-slate-300 p-2 transition-colors select-none hover:bg-slate-400',
  itemText: 'text-center text-sm font-medium',
  skeletonContainer: 'aspect-square bg-slate-300 p-2'
}

interface ComboSortModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  storeUids: string[]
}

function LoadingSkeleton() {
  return (
    <div className={STYLES.grid}>
      {Array.from({ length: SKELETON_ITEMS_COUNT }).map((_, index) => (
        <div key={index} className={STYLES.skeletonContainer}>
          <Skeleton className='h-4 w-full' />
        </div>
      ))}
    </div>
  )
}

function ErrorState() {
  return (
    <div className={STYLES.emptyState}>
      <p className={STYLES.errorText}>{MESSAGES.loadError}</p>
    </div>
  )
}

function EmptyState() {
  return (
    <div className={STYLES.emptyState}>
      <p className={STYLES.description}>{MESSAGES.noSources}</p>
    </div>
  )
}

interface DraggablePackageItemProps {
  pkg: Package
  index: number
  onDragStart: (e: React.DragEvent, index: number) => void
  onDragOver: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, index: number) => void
}

function DraggablePackageItem({ pkg, index, onDragStart, onDragOver, onDrop }: DraggablePackageItemProps) {
  return (
    <div
      key={pkg.id}
      draggable
      onDragStart={e => onDragStart(e, index)}
      onDragOver={onDragOver}
      onDrop={e => onDrop(e, index)}
      className={STYLES.draggableItem}
    >
      <div className={STYLES.itemText}>{pkg.package_name}</div>
    </div>
  )
}

function useFilteredPackages(packagesResponse: any) {
  return useMemo(() => {
    if (!packagesResponse?.data) return []
    return packagesResponse.data.sort((a: Package, b: Package) => a.sort - b.sort)
  }, [packagesResponse?.data])
}

function useDragAndDrop(initialPackages: Package[]) {
  const [sortedPackages, setSortedPackages] = useState<Package[]>([])

  useEffect(() => {
    setSortedPackages(initialPackages)
  }, [initialPackages])

  const handleDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.setData(DRAG_DATA_TYPE, index.toString())
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    const dragIndex = parseInt(e.dataTransfer.getData(DRAG_DATA_TYPE))

    if (dragIndex === dropIndex) return

    const newPackages = [...sortedPackages]
    const draggedItem = newPackages[dragIndex]

    newPackages.splice(dragIndex, 1)
    newPackages.splice(dropIndex, 0, draggedItem)

    setSortedPackages(newPackages)
  }

  return {
    sortedPackages,
    handleDragStart,
    handleDragOver,
    handleDrop
  }
}

export function ComboSortModal({ open, onOpenChange, storeUids }: ComboSortModalProps) {
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const {
    data: packagesResponse,
    isLoading,
    error
  } = usePackagesForSort({
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    list_store_uid: storeUids
  })

  const { updateSort, isUpdating } = useUpdatePackagesSort()

  const filteredPackages = useFilteredPackages(packagesResponse)
  const { sortedPackages, handleDragStart, handleDragOver, handleDrop } = useDragAndDrop(filteredPackages)

  const isProcessing = isLoading || isUpdating
  const hasNoPackages = sortedPackages.length === 0

  const handleSave = () => {
    const sortData = {
      company_uid: company?.id || '',
      brand_uid: selectedBrand?.id || '',
      list_data: sortedPackages.map((pkg, index) => ({
        list_package_uid: pkg.list_package_uid,
        sort: index
      }))
    }

    updateSort(sortData, {
      onSuccess: () => onOpenChange(false)
    })
  }

  const renderContent = () => {
    if (isLoading) return <LoadingSkeleton />
    if (error) return <ErrorState />
    if (hasNoPackages) return <EmptyState />

    return (
      <div className={STYLES.grid}>
        {sortedPackages.map((pkg, index) => (
          <DraggablePackageItem
            key={pkg.id}
            pkg={pkg}
            index={index}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          />
        ))}
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='flex h-[85vh] max-w-6xl flex-col sm:max-w-5xl'>
        <DialogHeader className='flex-shrink-0'>
          <DialogTitle>{MODAL_CONFIG.title}</DialogTitle>
        </DialogHeader>

        <div className='mb-4 flex-shrink-0'>
          <p className={STYLES.description}>{MODAL_CONFIG.description}</p>
        </div>

        <div className='min-h-0 flex-1'>
          <ScrollArea className='h-full w-full'>
            <div className='p-4'>
              {renderContent()}
            </div>
            <ScrollBar orientation='vertical' />
          </ScrollArea>
        </div>

        <DialogFooter className='flex-shrink-0'>
          <Button onClick={handleSave} disabled={isProcessing}>
            {isUpdating ? 'Đang lưu...' : MODAL_CONFIG.confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
