import { useState, useEffect } from 'react'

import { ChevronDown, ChevronRight } from 'lucide-react'

import { PosModal } from '@/components/pos'
import {
  Input,
  Checkbox,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui'

import { useDeviceItemTypes } from '../../hooks/use-device-item-types'
import { useDevicePackages } from '../../hooks/use-device-packages'

interface GroupSelectionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedGroups: Set<string>
  setSelectedGroups: (groups: Set<string> | ((prev: Set<string>) => Set<string>)) => void
  selectedCombos: Set<string>
  setSelectedCombos: (combos: Set<string> | ((prev: Set<string>) => Set<string>)) => void
  onConfirm: () => void
  storeUid?: string
  deviceData?: any
}

export function GroupSelectionModal({
  open,
  onOpenChange,
  selectedGroups,
  setSelectedGroups,
  selectedCombos,
  setSelectedCombos,
  onConfirm,
  storeUid,
  deviceData
}: GroupSelectionModalProps) {
  const [activeTab, setActiveTab] = useState('groups')
  const [groupSearchTerm, setGroupSearchTerm] = useState('')
  const [selectedSectionOpen, setSelectedSectionOpen] = useState(true)
  const [remainingSectionOpen, setRemainingSectionOpen] = useState(true)

  const { data: itemTypesData = [] } = useDeviceItemTypes({ storeUid })
  const { data: combosData = [] } = useDevicePackages({ storeUid })

  useEffect(() => {
    if (
      open &&
      deviceData?.extra_data?.item_type_ignore &&
      Array.isArray(itemTypesData) &&
      Array.isArray(combosData) &&
      itemTypesData.length > 0 &&
      combosData.length > 0 &&
      selectedGroups.size === 0 &&
      selectedCombos.size === 0
    ) {
      const itemTypeIgnore = deviceData.extra_data.item_type_ignore || []

      const newSelectedGroups = new Set<string>()
      const newSelectedCombos = new Set<string>()

      itemTypeIgnore.forEach((ignoreId: string) => {
        const foundGroup = (itemTypesData as any[]).find((item: any) => item.item_type_id === ignoreId)
        if (foundGroup) {
          newSelectedGroups.add(ignoreId) // Lưu item_type_id thay vì foundGroup.id
        }

        const foundCombo = (combosData as any[]).find((combo: any) => combo.package_id === ignoreId)
        if (foundCombo) {
          newSelectedCombos.add(ignoreId) // Lưu package_id thay vì foundCombo.id
        }
      })

      setSelectedGroups(newSelectedGroups)
      setSelectedCombos(newSelectedCombos)
    }
  }, [
    open,
    deviceData,
    itemTypesData,
    combosData,
    selectedGroups.size,
    selectedCombos.size,
    setSelectedGroups,
    setSelectedCombos
  ])

  const itemTypes = (itemTypesData as any[]) || []
  const filteredItemTypes = itemTypes.filter((item: any) =>
    item.item_type_name.toLowerCase().includes(groupSearchTerm.toLowerCase())
  )

  const combos = (combosData as any[]) || []
  const filteredCombos = combos.filter((combo: any) =>
    (combo.package_name || combo.name || '').toLowerCase().includes(groupSearchTerm.toLowerCase())
  )

  const getCurrentTabData = () => {
    if (activeTab === 'groups') {
      return {
        allItems: filteredItemTypes.map((item: any) => ({
          id: item.id,
          item_type_id: item.item_type_id,
          name: item.item_type_name,
          type: 'group' as const
        })),
        selectedItems: selectedGroups,
        setSelectedItems: setSelectedGroups
      }
    } else {
      return {
        allItems: filteredCombos.map((combo: any) => ({
          id: combo.id,
          package_id: combo.package_id,
          name: combo.package_name,
          type: 'combo' as const
        })),
        selectedItems: selectedCombos,
        setSelectedItems: setSelectedCombos
      }
    }
  }

  const { allItems, selectedItems, setSelectedItems } = getCurrentTabData()

  const getItemIdToCheck = (item: any) => {
    if (activeTab === 'groups') {
      const foundGroup = itemTypes.find((group: any) => group.id === item.id)
      return foundGroup?.item_type_id || item.id
    } else {
      const foundCombo = combos.find((combo: any) => combo.id === item.id)
      return foundCombo?.package_id || item.id
    }
  }

  const selectedTabItems = allItems.filter(item => selectedItems.has(getItemIdToCheck(item)))
  const remainingTabItems = allItems.filter(item => !selectedItems.has(getItemIdToCheck(item)))

  const handleItemToggle = (itemId: string) => {
    let idToStore = itemId

    if (activeTab === 'groups') {
      const foundGroup = itemTypes.find((item: any) => item.id === itemId)
      if (foundGroup && foundGroup.item_type_id) {
        idToStore = foundGroup.item_type_id
      }
    } else {
      const foundCombo = combos.find((combo: any) => combo.id === itemId)
      if (foundCombo && foundCombo.package_id) {
        idToStore = foundCombo.package_id
      }
    }

    setSelectedItems((prev: Set<string>) => {
      const newSet = new Set(prev)
      if (newSet.has(idToStore)) {
        newSet.delete(idToStore)
      } else {
        newSet.add(idToStore)
      }
      return newSet
    })
  }

  return (
    <PosModal
      title='Chọn nhóm hoặc combo không hiển thị trên thiết bị'
      centerTitle
      open={open}
      onOpenChange={onOpenChange}
      onCancel={() => onOpenChange(false)}
      onConfirm={onConfirm}
      confirmText='Xác nhận'
      cancelText='Hủy'
      maxWidth='sm:max-w-2xl'
    >
      <div className='space-y-4'>
        <Input
          placeholder='Tìm kiếm nhóm hoặc combo'
          value={groupSearchTerm}
          onChange={e => setGroupSearchTerm(e.target.value)}
          className='w-full'
        />

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className='grid w-full grid-cols-2'>
            <TabsTrigger value='groups'>Nhóm món</TabsTrigger>
            <TabsTrigger value='combos'>Combo</TabsTrigger>
          </TabsList>

          <TabsContent value='groups' className='space-y-4'>
            <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
              <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                <span className='font-medium'>Đã chọn ({selectedTabItems.length})</span>
                {selectedSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
              </CollapsibleTrigger>
              <CollapsibleContent className='mt-2'>
                <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                  {selectedTabItems.length === 0 ? (
                    <p className='text-sm text-gray-500'>Chưa có nhóm món nào được chọn</p>
                  ) : (
                    selectedTabItems.map(item => (
                      <label
                        key={item.id}
                        className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                      >
                        <Checkbox
                          checked={selectedItems.has(getItemIdToCheck(item))}
                          onCheckedChange={() => handleItemToggle(item.id)}
                        />
                        <div className='flex-1'>
                          <p className='text-sm font-medium'>{item.name}</p>
                        </div>
                      </label>
                    ))
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>

            <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
              <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                <span className='font-medium'>Còn lại ({remainingTabItems.length})</span>
                {remainingSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
              </CollapsibleTrigger>
              <CollapsibleContent className='mt-2'>
                <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                  {remainingTabItems.length === 0 ? (
                    <p className='text-sm text-gray-500'>Không có nhóm món nào</p>
                  ) : (
                    remainingTabItems.map(item => (
                      <label
                        key={item.id}
                        className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                      >
                        <Checkbox
                          checked={selectedItems.has(getItemIdToCheck(item))}
                          onCheckedChange={() => handleItemToggle(item.id)}
                        />
                        <div className='flex-1'>
                          <p className='text-sm font-medium'>{item.name}</p>
                        </div>
                      </label>
                    ))
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>
          </TabsContent>

          <TabsContent value='combos' className='space-y-4'>
            <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
              <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                <span className='font-medium'>Đã chọn ({selectedTabItems.length})</span>
                {selectedSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
              </CollapsibleTrigger>
              <CollapsibleContent className='mt-2'>
                <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                  {selectedTabItems.length === 0 ? (
                    <p className='text-sm text-gray-500'>Chưa có combo nào được chọn</p>
                  ) : (
                    selectedTabItems.map(item => (
                      <label
                        key={item.id}
                        className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                      >
                        <Checkbox
                          checked={selectedItems.has(getItemIdToCheck(item))}
                          onCheckedChange={() => handleItemToggle(item.id)}
                        />
                        <div className='flex-1'>
                          <p className='text-sm font-medium'>{item.name}</p>
                        </div>
                      </label>
                    ))
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>

            <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
              <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                <span className='font-medium'>Còn lại ({remainingTabItems.length})</span>
                {remainingSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
              </CollapsibleTrigger>
              <CollapsibleContent className='mt-2'>
                <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                  {remainingTabItems.length === 0 ? (
                    <p className='text-sm text-gray-500'>Không có combo nào</p>
                  ) : (
                    remainingTabItems.map(item => (
                      <label
                        key={item.id}
                        className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                      >
                        <Checkbox
                          checked={selectedItems.has(getItemIdToCheck(item))}
                          onCheckedChange={() => handleItemToggle(item.id)}
                        />
                        <div className='flex-1'>
                          <p className='text-sm font-medium'>{item.name}</p>
                        </div>
                      </label>
                    ))
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>
          </TabsContent>
        </Tabs>
      </div>
    </PosModal>
  )
}
