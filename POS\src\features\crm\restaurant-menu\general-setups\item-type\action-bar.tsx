import { Button } from '@/components/ui/button'
import { RotateCcw, Eye, Plus } from 'lucide-react'

interface ItemTypeActionBarProps {
  onSyncItems: () => void
  onViewPreview: () => void
  onCreateItem: () => void
  className?: string
}

export function ItemTypeActionBar({ 
  onSyncItems, 
  onViewPreview, 
  onCreateItem,
  className = '' 
}: ItemTypeActionBarProps) {
  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      <Button 
        variant='outline' 
        onClick={onSyncItems} 
        className='text-orange-600 border-orange-600 hover:bg-orange-50'
      >
        <RotateCcw className='h-4 w-4 mr-2' />
        Đồng bộ nhóm món
      </Button>
      <Button variant='outline' onClick={onViewPreview}>
        <Eye className='h-4 w-4 mr-2' />
        Xem trước
      </Button>
      <Button onClick={onCreateItem}>
        <Plus className='h-4 w-4 mr-2' />
        Tạo nhóm
      </Button>
    </div>
  )
}
