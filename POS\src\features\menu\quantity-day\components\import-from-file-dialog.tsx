'use client'

import { useState, useRef } from 'react'

import { Download, Upload } from 'lucide-react'
import { Trash2 } from 'lucide-react'
import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import { useAuthStore } from '@/stores/authStore'

import { useCreateQuantityDay } from '@/hooks/api/use-quantity-days'
import { useStoresData } from '@/hooks/api/use-stores'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { itemsInStoreApiService } from '@/features/menu/items/items-in-store/hooks/items-in-store-api'

import { createQuantityDayTemplate } from '../utils/excel-template'

interface ImportFromFileDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface PreviewData {
  id: string
  appliedItems: string
  quantity: number
  startDate: string
  endDate: string
  startDateRaw: string
  endDateRaw: string
  selectedDays: string
  selectedDaysRaw: string
  isValid: boolean
  errorMessage?: string
}

export function ImportFromFileDialog({ open, onOpenChange }: ImportFromFileDialogProps) {
  const { data: stores = [] } = useStoresData()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]
  const { createQuantityDay } = useCreateQuantityDay()

  const [selectedStoreUid, setSelectedStoreUid] = useState<string>('')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewData, setPreviewData] = useState<PreviewData[]>([])
  const [requireUpdatePos, setRequireUpdatePos] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleRemoveRow = (id: string) => {
    setPreviewData(prev => prev.filter(row => row.id !== id))
  }

  const handlePreviewClose = (isOpen: boolean) => {
    setShowPreview(isOpen)
    if (!isOpen && previewData.length > 0) {
      onOpenChange(true)
    }
  }

  const handleDownloadTemplate = () => {
    try {
      createQuantityDayTemplate()
      toast.success('Đã tải file mẫu thành công')
    } catch (error) {
      console.error('Error creating template:', error)
      toast.error('Lỗi khi tạo file mẫu')
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const validTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']

    if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
      toast.error('File phải có định dạng Excel (.xlsx hoặc .xls)')
      return
    }

    setSelectedFile(file)
    parseExcelFile(file)
  }

  const parseDate = (dateStr: string): Date | null => {
    if (!dateStr) return null

    const dateString = dateStr.toString().trim()

    if (/^\d+$/.test(dateString)) {
      try {
        return new Date((parseInt(dateString) - 25569) * 86400 * 1000)
      } catch {
        return null
      }
    }

    const parts = dateString.split('/')
    if (parts.length !== 3) return null

    const day = parseInt(parts[0])
    const month = parseInt(parts[1])
    const year = parseInt(parts[2])

    if (isNaN(day) || isNaN(month) || isNaN(year)) return null
    if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > 2100) return null

    const date = new Date(year, month - 1, day)
    return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day ? date : null
  }

  const formatDate = (dateStr: string): string => {
    if (!dateStr) return ''

    const dateString = dateStr.toString().trim()

    if (/^\d+$/.test(dateString)) {
      try {
        const date = new Date((parseInt(dateString) - 25569) * 86400 * 1000)
        return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`
      } catch {
        return dateString
      }
    }

    return dateString
  }

  const formatDaysFromBitFlag = (bitFlag: number): string => {
    if (bitFlag === 0) return 'Tất cả ngày trong tuần'

    const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7']
    const dayValues = [2, 4, 8, 16, 32, 64, 128] // Bit values for each day
    const selectedDays: string[] = []

    for (let i = 0; i < 7; i++) {
      if (bitFlag & dayValues[i]) {
        selectedDays.push(dayNames[i])
      }
    }

    return selectedDays.join(', ')
  }

  const validateAndParseTimeValue = (timeStr: string): { isValid: boolean; bitFlag: number; errorMessage?: string } => {
    if (!timeStr || timeStr.trim() === '') {
      return { isValid: true, bitFlag: 0 }
    }

    const timeValue = parseInt(timeStr.trim())

    if (isNaN(timeValue)) {
      return { isValid: false, bitFlag: 0, errorMessage: 'Thời gian không hợp lệ' }
    }

    if (timeValue < 0 || timeValue > 254) {
      return { isValid: false, bitFlag: 0, errorMessage: 'Thời gian không hợp lệ' }
    }

    if (timeValue > 0) {
      const validDayBits = 2 + 4 + 8 + 16 + 32 + 64 + 128 // 254
      if ((timeValue & validDayBits) !== timeValue) {
        return { isValid: false, bitFlag: 0, errorMessage: 'Thời gian không hợp lệ' }
      }

      const dayValues = [2, 4, 8, 16, 32, 64, 128]
      let reconstructed = 0
      for (const dayValue of dayValues) {
        if (timeValue & dayValue) {
          reconstructed |= dayValue
        }
      }

      if (reconstructed !== timeValue) {
        return { isValid: false, bitFlag: 0, errorMessage: 'Thời gian không hợp lệ' }
      }
    }

    return { isValid: true, bitFlag: timeValue }
  }

  const parseExcelFile = async (file: File) => {
    const reader = new FileReader()
    reader.onload = async e => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]

        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          defval: '',
          blankrows: false
        }) as any[][]

        if (!jsonData || jsonData.length < 2) {
          toast.error('File không có dữ liệu hoặc chỉ có header')
          return
        }

        const expectedHeaders = ['Món áp dụng', 'Số lượng', 'Ngày bắt đầu', 'Ngày kết thúc', 'Chọn ngày']
        const actualHeaders = jsonData[0]

        for (let i = 0; i < expectedHeaders.length; i++) {
          if (!actualHeaders[i] || actualHeaders[i].toString().trim() !== expectedHeaders[i]) {
            toast.error(`Cột ${i + 1} phải có tiêu đề "${expectedHeaders[i]}"`)
            return
          }
        }

        const dataRows = jsonData.slice(1)
        const preview: PreviewData[] = []

        for (let i = 0; i < dataRows.length; i++) {
          const row = dataRows[i]
          const rowNumber = i + 2
          let isValid = true
          let errorMessage = ''

          const appliedItems = row[0]?.toString().trim() || ''
          const quantity = parseInt(row[1]?.toString() || '0')
          const startDateStr = row[2]?.toString().trim() || ''
          const endDateStr = row[3]?.toString().trim() || ''
          const selectedDaysStr = row[4]?.toString().trim() || ''

          if (!appliedItems) {
            isValid = false
            errorMessage = 'Món áp dụng không được để trống'
          } else if (isNaN(quantity) || quantity <= 0) {
            isValid = false
            errorMessage = 'Số lượng phải là số nguyên dương'
          } else if (!parseDate(startDateStr)) {
            isValid = false
            errorMessage = 'Ngày bắt đầu không hợp lệ (DD/MM/YYYY)'
          } else if (!parseDate(endDateStr)) {
            isValid = false
            errorMessage = 'Ngày kết thúc không hợp lệ (DD/MM/YYYY)'
          } else {
            const timeValidation = validateAndParseTimeValue(selectedDaysStr)
            if (!timeValidation.isValid) {
              isValid = false
              errorMessage = timeValidation.errorMessage || 'Thời gian không hợp lệ'
            }
          }

          if (isValid && selectedStoreUid && appliedItems) {
            try {
              const itemCodes = appliedItems.split(',').map((code: string) => code.trim())
              const storeItemsResp = await itemsInStoreApiService.getItemsInStore({
                store_uid: selectedStoreUid,
                skip_limit: true,
                company_uid: company?.id || '',
                brand_uid: selectedBrand?.id || ''
              })
              const validItemIds = new Set((storeItemsResp.data || []).map((item: any) => item.item_id))
              const invalidItems = itemCodes.filter((code: string) => !validItemIds.has(code))

              if (invalidItems.length > 0) {
                isValid = false
                errorMessage = `Món không hợp lệ: ${invalidItems.join(', ')}`
              }
            } catch (error) {
              console.error('Error validating items:', error)
            }
          }

          const timeValidation = validateAndParseTimeValue(selectedDaysStr)
          const bitFlag = timeValidation.bitFlag

          preview.push({
            id: `row-${rowNumber}`,
            appliedItems,
            quantity,
            startDate: formatDate(startDateStr),
            endDate: formatDate(endDateStr),
            startDateRaw: startDateStr,
            endDateRaw: endDateStr,
            selectedDays: formatDaysFromBitFlag(bitFlag),
            selectedDaysRaw: selectedDaysStr,
            isValid,
            errorMessage
          })
        }

        setPreviewData(preview)
        setShowPreview(true)
        onOpenChange(false)
        toast.success(`Đã đọc ${preview.length} dòng dữ liệu từ file`)
      } catch (error) {
        console.error('Error parsing Excel file:', error)
        toast.error(`Lỗi khi đọc file Excel: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
    reader.readAsArrayBuffer(file)
  }

  const handleImport = async () => {
    if (!selectedStoreUid) {
      toast.error('Vui lòng chọn cửa hàng')
      return
    }

    const validRows = previewData.filter(row => row.isValid)
    if (validRows.length === 0) {
      toast.error('Không có dữ liệu hợp lệ để import')
      return
    }

    setIsProcessing(true)

    try {
      let successCount = 0
      let errorCount = 0

      for (const row of validRows) {
        try {
          const itemCodes = row.appliedItems.split(',').map((code: string) => code.trim())
          const startDate = parseDate(row.startDateRaw)
          const endDate = parseDate(row.endDateRaw)

          if (process.env.NODE_ENV === 'development') {
            console.log('Processing row:', {
              startDateRaw: row.startDateRaw,
              endDateRaw: row.endDateRaw,
              startDate,
              endDate,
              startTimestamp: startDate ? Math.floor(startDate.getTime() / 1000) : null,
              endTimestamp: endDate ? Math.floor(endDate.getTime() / 1000) : null
            })
          }

          if (!startDate || !endDate) {
            console.error('Failed to parse dates:', { startDateRaw: row.startDateRaw, endDateRaw: row.endDateRaw })
            errorCount++
            continue
          }

          const timeValidation = validateAndParseTimeValue(row.selectedDaysRaw)
          const timeSaleDateWeek = timeValidation.bitFlag

          const createData = {
            require_update: requireUpdatePos ? 'true' : 'false',
            store_uid: selectedStoreUid,
            time_sale_date_week: timeSaleDateWeek,
            quantity_per_day: row.quantity,
            item_list: itemCodes,
            from_date: startDate.getTime(),
            to_date: endDate.getTime(),
            company_uid: company?.id || '',
            brand_uid: selectedBrand?.id || ''
          }

          createQuantityDay(createData)
          successCount++
        } catch (error) {
          console.error('Error creating quantity day:', error)
          errorCount++
        }
      }

      if (successCount > 0) {
        toast.success(`Đã thêm thành công ${successCount} cấu hình`)
      }

      if (errorCount > 0) {
        toast.error(`${errorCount} cấu hình bị lỗi`)
      }

      if (successCount > 0) {
        setSelectedStoreUid('')
        setSelectedFile(null)
        setPreviewData([])
        setShowPreview(false)
        setRequireUpdatePos(false)
      }
    } catch (error) {
      console.error('Import error:', error)
      toast.error('Lỗi khi import dữ liệu')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='w-[90vw] max-w-4xl'>
          <DialogHeader>
            <DialogTitle>Cấu hình số lượng món trong ngày</DialogTitle>
          </DialogHeader>

          <div className='space-y-6'>
            {/* Step 1: Select Store */}
            <div className='space-y-3'>
              <div className='bg-muted/50 rounded-lg p-4'>
                <h3 className='mb-3 font-medium'>Bước 1. Chọn cửa hàng</h3>
                <Select value={selectedStoreUid} onValueChange={setSelectedStoreUid}>
                  <SelectTrigger>
                    <SelectValue placeholder='Chọn cửa hàng' />
                  </SelectTrigger>
                  <SelectContent>
                    {stores.map(store => (
                      <SelectItem key={store.id} value={store.id}>
                        {store.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Step 2: Download Template */}
            <div className='space-y-3'>
              <div className='bg-muted/50 rounded-lg p-4'>
                <h3 className='mb-3 font-medium'>Bước 2. Tải file mẫu</h3>
                <Button variant='outline' onClick={handleDownloadTemplate} className='w-full'>
                  <Download className='mr-2 h-4 w-4' />
                  Tải xuống
                </Button>
              </div>
            </div>

            {/* Step 3: Upload File */}
            <div className='space-y-3'>
              <div className='bg-muted/50 rounded-lg p-4'>
                <h3 className='mb-3 font-medium'>Bước 3. Thêm cấu hình vào file</h3>
                <p className='text-muted-foreground mb-3 text-sm'>Thêm hoặc sửa thông tin món dựa vào sheet mẫu.</p>

                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='require-update'
                    checked={requireUpdatePos}
                    disabled={!selectedStoreUid}
                    onCheckedChange={checked => setRequireUpdatePos(checked as boolean)}
                  />
                  <Label
                    htmlFor='require-update'
                    className={`text-sm ${!selectedStoreUid ? 'text-muted-foreground' : ''}`}
                  >
                    Yêu cầu POS cập nhật lại số lượng trong khai báo ngày lập tức
                  </Label>
                </div>
              </div>
            </div>

            {/* Step 4: Upload File */}
            <div className='space-y-3'>
              <div className='bg-muted/50 rounded-lg p-4'>
                <h3 className='mb-3 font-medium'>Bước 4. Tải file lên</h3>
                <p className='text-muted-foreground mb-3 text-sm'>Sau khi đã điền đầy đủ bạn có thể tải file lên</p>

                <div className='space-y-3'>
                  <Input
                    ref={fileInputRef}
                    type='file'
                    accept='.xlsx,.xls'
                    onChange={handleFileSelect}
                    className='hidden'
                  />
                  <Button variant='outline' onClick={() => fileInputRef.current?.click()} className='w-full'>
                    <Upload className='mr-2 h-4 w-4' />
                    {selectedFile ? selectedFile.name : 'Chọn file'}
                  </Button>

                  {previewData.length > 0 && (
                    <div className='mt-4'>
                      <p className='text-muted-foreground mb-3 text-sm'>
                        Đã đọc {previewData.length} dòng dữ liệu ({previewData.filter(row => row.isValid).length} hợp
                        lệ, {previewData.filter(row => !row.isValid).length} lỗi)
                      </p>
                      <Button
                        onClick={handleImport}
                        disabled={!selectedStoreUid || isProcessing || !previewData.some(row => row.isValid)}
                        className='w-full'
                      >
                        {isProcessing ? 'Đang xử lý...' : 'Lưu'}
                        <Upload className='ml-2 h-4 w-4' />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={handlePreviewClose}>
        <DialogContent className='max-h-[95vh] w-[98vw] max-w-[98vw] overflow-hidden'>
          <DialogHeader>
            <DialogTitle>Preview dữ liệu</DialogTitle>
          </DialogHeader>

          <div className='max-h-[80vh] overflow-auto'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-12'></TableHead>
                  <TableHead className='min-w-[200px]'>Món áp dụng</TableHead>
                  <TableHead className='w-24 text-center'>Số lượng</TableHead>
                  <TableHead className='w-32 text-center'>Ngày bắt đầu</TableHead>
                  <TableHead className='w-32 text-center'>Ngày kết thúc</TableHead>
                  <TableHead className='min-w-[150px]'>Chọn ngày</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {previewData.map(row => (
                  <TableRow key={row.id} className={!row.isValid ? 'border-red-200 bg-red-50' : ''}>
                    <TableCell>
                      <Button variant='ghost' size='sm' onClick={() => handleRemoveRow(row.id)} className='h-6 w-6 p-0'>
                        <Trash2 className='h-3 w-3' />
                      </Button>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className='font-medium'>{row.appliedItems}</div>
                        {!row.isValid && row.errorMessage && (
                          <div className='mt-1 text-xs text-red-600'>{row.errorMessage}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className='text-center'>{row.quantity}</TableCell>
                    <TableCell className='text-center'>{row.startDate}</TableCell>
                    <TableCell className='text-center'>{row.endDate}</TableCell>
                    <TableCell>{row.selectedDays}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className='flex items-center justify-between border-t pt-4'>
            <div className='text-muted-foreground text-sm'>
              {previewData.filter(row => row.isValid).length} hợp lệ, {previewData.filter(row => !row.isValid).length}{' '}
              lỗi
            </div>
            <div className='flex gap-2'>
              <Button variant='outline' onClick={() => handlePreviewClose(false)}>
                Đóng
              </Button>
              <Button
                onClick={handleImport}
                disabled={!selectedStoreUid || isProcessing || !previewData.some(row => row.isValid)}
              >
                {isProcessing ? 'Đang xử lý...' : 'Lưu'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
