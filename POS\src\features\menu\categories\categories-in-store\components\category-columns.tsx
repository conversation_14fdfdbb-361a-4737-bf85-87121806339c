import { ColumnDef } from '@tanstack/react-table'

import { IconTrash } from '@tabler/icons-react'

import { ItemType } from '@/lib/item-types-api'

import { Button } from '@/components/ui/button'

import { StatusBadge } from '@/components/pos/status-badge'

export const categoryColumns: ColumnDef<ItemType>[] = [
  {
    accessorKey: 'id',
    header: '#',
    cell: ({ row }) => {
      const index = row.index + 1
      return <div className='w-[50px] font-medium'>{index}</div>
    },
    enableSorting: false
  },
  {
    accessorKey: 'item_type_id',
    header: 'Mã nhóm',
    cell: ({ row }) => {
      const category = row.original
      return <span className='font-mono text-sm'>{category.item_type_id}</span>
    }
  },
  {
    accessorKey: 'item_type_name',
    header: 'Tên nhóm',
    cell: ({ row }) => {
      const category = row.original
      return <span className='font-medium'>{category.item_type_name}</span>
    }
  },
  {
    accessorKey: 'active',
    header: 'Thao tác',
    cell: ({ row, table }) => {
      const category = row.original
      const meta = table.options.meta as {
        onToggleStatus?: (category: ItemType) => void
      }

      return (
        <div
          onClick={e => {
            e.stopPropagation()
            meta?.onToggleStatus?.(category)
          }}
          className='cursor-pointer'
        >
          <StatusBadge
            isActive={category.active === 1}
            activeText='Active'
            inactiveText='Deactive'
          />
        </div>
      )
    }
  },
  {
    id: 'delete',
    header: '',
    cell: ({ row, table }) => {
      const category = row.original
      const meta = table.options.meta as {
        onDeleteCategory?: (category: ItemType) => void
      }

      return (
        <Button
          variant='ghost'
          size='sm'
          onClick={e => {
            e.stopPropagation()
            meta?.onDeleteCategory?.(category)
          }}
          className='h-8 w-8 p-0 text-red-600 hover:text-red-700'
          title='Xóa nhóm'
        >
          <IconTrash className='h-4 w-4' />
          <span className='sr-only'>Xóa nhóm {category.item_type_name}</span>
        </Button>
      )
    }
  }
]
