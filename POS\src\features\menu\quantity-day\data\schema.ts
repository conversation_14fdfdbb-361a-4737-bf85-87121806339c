import { z } from 'zod'

// Schema for quantity day table
export const quantityDaySchema = z.object({
  id: z.string(),
  quantity: z.number(),
  fromDate: z.date(),
  toDate: z.date(),
  time: z.string(),
  appliedItems: z.string(), // Món áp dụng
  storeUid: z.string(),
  storeName: z.string(),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
  originalData: z.any().optional(), // Raw API data
})

export type QuantityDay = z.infer<typeof quantityDaySchema>
