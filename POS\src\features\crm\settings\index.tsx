import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Form } from '@/components/ui'

import {
  BrandConfigSection,
  DeliveryConfigSection,
  MembershipConfigSection,
  TransactionAlertSection,
  OnlineOrderAlertSection
} from './components/sections'
import { useCrmSettingsForm } from './hooks'

export default function CrmSettingsPage() {
  const { form, handleSubmit, isLoading } = useCrmSettingsForm()

  return (
    <>
      {/* Header */}
      <Header>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* Main Content */}
      <Main>
        {/* Content Section */}
        <div className='min-h-screen'>
          <div className='container mx-auto px-6 py-8'>
            <Form {...form}>
              <form id='crm-settings-form' onSubmit={form.handleSubmit(handleSubmit)} className='space-y-8'>
                {/* Cấu hình thương hiệu */}
                <BrandConfigSection form={form} isLoading={isLoading} />

                {/* Cấu hình đặt giao hàng */}
                <DeliveryConfigSection form={form} isLoading={isLoading} />

                {/* Chương trình thành viên */}
                <MembershipConfigSection form={form} isLoading={isLoading} />

                {/* Cảnh báo giao dịch bất thường */}
                <TransactionAlertSection form={form} isLoading={isLoading} />

                {/* Cảnh báo đơn hàng online */}
                <OnlineOrderAlertSection form={form} isLoading={isLoading} />
              </form>
            </Form>
          </div>
        </div>
      </Main>
    </>
  )
}
