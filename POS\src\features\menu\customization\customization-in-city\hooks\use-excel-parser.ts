import { ParsedCustomizationData } from '@/types/customizations'
import { toast } from 'sonner'

export function useExcelParser() {
  const parseExcelFile = async (
    file: File,
    isImport = false
  ): Promise<ParsedCustomizationData[]> => {
    try {
      // Dynamic import for xlsx
      const XLSX = await import('xlsx')

      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

      // Parse data according to format: Row 1: headers, Row 2+: data
      const parsedCustomizations: ParsedCustomizationData[] = []

      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i] as unknown[]
        
        if (isImport) {
          // Import format: name, cityName, appliedItemCodes, groupName, minRequired, maxAllowed, groupItemCodes
          if (row && row.length >= 7) {
            parsedCustomizations.push({
              id: '', // ID will be generated by the API
              name: String(row[0] || '').trim(),
              cityName: String(row[1] || '').trim(),
              appliedItemCodes: String(row[2] || '').trim(),
              groupName: String(row[3] || '').trim(),
              minRequired: Number(row[4]) || 0,
              maxAllowed: Number(row[5]) || 0,
              groupItemCodes: String(row[6] || '').trim(),
            })
          }
        } else {
          // Export format: id, name, cityName, appliedItemCodes, groupName, minRequired, maxAllowed, groupItemCodes
          if (row && row.length >= 8) {
            parsedCustomizations.push({
              id: String(row[0] || '').trim(),
              name: String(row[1] || '').trim(),
              cityName: String(row[2] || '').trim(),
              appliedItemCodes: String(row[3] || '').trim(),
              groupName: String(row[4] || '').trim(),
              minRequired: Number(row[5]) || 0,
              maxAllowed: Number(row[6]) || 0,
              groupItemCodes: String(row[7] || '').trim(),
            })
          }
        }
      }

      return parsedCustomizations
    } catch (error) {
      toast.error('Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file.')
      throw error
    }
  }

  const downloadTemplate = () => {
    // Create template Excel file
    const templateData = [
      [
        'Tên',
        'Thành phố',
        'Mã món áp dụng',
        'Tên nhóm',
        'Yêu cầu chọn',
        'Giới hạn chọn',
        'Mã món theo nhóm',
      ],
      [
        'Tên customization',
        'Hồ Chí Minh',
        '',
        'Tên nhóm',
        '1',
        '1',
        'ITEM-CODE1,ITEM-CODE2',
      ],
    ]

    // Dynamic import for xlsx
    import('xlsx').then((XLSX) => {
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.aoa_to_sheet(templateData)
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Template')
      XLSX.writeFile(workbook, 'create-customization-in-city.xlsx')
      toast.success('Đã tải file mẫu thành công!')
    })
  }

  return {
    parseExcelFile,
    downloadTemplate,
  }
}
