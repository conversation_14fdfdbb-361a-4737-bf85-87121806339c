import { useState } from 'react'
import { usePosData } from '@/hooks/use-pos-data'

export function useCustomizationSearch() {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCityId, setSelectedCityId] = useState<string>('all')

  const { cities } = usePosData()

  // Prepare city UIDs for API call
  const listCityUid =
    selectedCityId === 'all' ? cities.map((city) => city.id) : [selectedCityId]

  const handleSearchSubmit = () => {
    setSearchTerm(searchQuery)
  }

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSearchSubmit()
    }
  }

  return {
    searchTerm,
    searchQuery,
    setSearchQuery,
    selectedCityId,
    setSelectedCityId,
    listCityUid,
    cities,
    handleSearchSubmit,
    handleSearchKeyDown,
  }
}
