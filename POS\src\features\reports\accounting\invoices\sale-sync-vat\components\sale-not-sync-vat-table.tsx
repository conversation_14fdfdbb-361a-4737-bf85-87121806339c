import { SaleNotSyncVatData } from '@/lib/sales-api'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  formatCurrency,
  formatDate,
  formatPercentage,
  getPaymentMethodBadgeVariant,
  getTableServiceBadgeVariant,
} from './sale-not-sync-vat-utils'

// Table Header Component
export function SaleNotSyncVatTableHeader({
  showStoreInfo = true,
  showEmployeeInfo = true,
  showPaymentMethod = true,
}: {
  showStoreInfo?: boolean
  showEmployeeInfo?: boolean
  showPaymentMethod?: boolean
}) {
  return (
    <TableHeader>
      <TableRow>
        <TableHead>Mã giao dịch</TableHead>
        <TableHead>Thời gian</TableHead>
        {showStoreInfo && <TableHead>C<PERSON><PERSON> hàng</TableHead>}
        {showEmployeeInfo && <TableHead>Nhân viên</TableHead>}
        <TableHead>Loại bàn</TableHead>
        {showPaymentMethod && <TableHead>Thanh toán</TableHead>}
        <TableHead>Voucher</TableHead>
        <TableHead className='text-right'>Số tiền</TableHead>
      </TableRow>
    </TableHeader>
  )
}

// Table Row Component
function SaleNotSyncVatTableRow({
  sale,
  showStoreInfo = true,
  showEmployeeInfo = true,
  showPaymentMethod = true,
}: {
  sale: SaleNotSyncVatData & { storeName: string }
  showStoreInfo?: boolean
  showEmployeeInfo?: boolean
  showPaymentMethod?: boolean
}) {
  const discountPercentage =
    sale.amount_origin > 0
      ? ((sale.amount_origin - sale.total_amount) / sale.amount_origin) * 100
      : 0

  return (
    <TableRow>
      <TableCell>
        <div className='space-y-1'>
          <div className='font-mono text-sm font-medium'>{sale.tran_no}</div>
        </div>
      </TableCell>
      <TableCell>
        <span className='text-sm'>{formatDate(sale.tran_date)}</span>
      </TableCell>
      {showStoreInfo && (
        <TableCell>
          <span className='text-sm'>{sale.storeName}</span>
        </TableCell>
      )}
      {showEmployeeInfo && (
        <TableCell>
          <span className='text-sm'>{sale.employee_name}</span>
        </TableCell>
      )}
      <TableCell>
        <Badge
          variant={getTableServiceBadgeVariant(sale.table_name)}
          className='px-2 py-0.5 text-xs'
        >
          {sale.table_name}
        </Badge>
      </TableCell>
      {showPaymentMethod && (
        <TableCell>
          <Badge
            variant={getPaymentMethodBadgeVariant(sale.payment_method_name)}
            className='px-1.5 py-0.5 text-xs'
          >
            {sale.payment_method_name}
          </Badge>
        </TableCell>
      )}
      <TableCell>
        {sale.voucher_code ? (
          <Badge variant='outline' className='px-2 py-0.5 font-mono text-xs'>
            {sale.voucher_code}
          </Badge>
        ) : (
          <span className='text-muted-foreground text-xs'>-</span>
        )}
      </TableCell>
      <TableCell className='text-right'>
        <div className='space-y-1'>
          <div className='text-sm font-semibold'>
            {formatCurrency(sale.total_amount)} VNĐ
          </div>
          {sale.amount_origin !== sale.total_amount && (
            <div className='text-muted-foreground text-xs line-through'>
              {formatCurrency(sale.amount_origin)} VNĐ
            </div>
          )}
          {discountPercentage > 0 && (
            <div className='text-xs'>
              <span className='text-red-600'>
                -{formatPercentage(discountPercentage)}
              </span>
            </div>
          )}
        </div>
      </TableCell>
    </TableRow>
  )
}

// Table Skeleton Component
export function SaleNotSyncVatTableSkeleton({
  showStoreInfo = true,
  showEmployeeInfo = true,
  showPaymentMethod = true,
}: {
  showStoreInfo?: boolean
  showEmployeeInfo?: boolean
  showPaymentMethod?: boolean
}) {
  return (
    <div className='rounded-md border'>
      <Table>
        <SaleNotSyncVatTableHeader
          showStoreInfo={showStoreInfo}
          showEmployeeInfo={showEmployeeInfo}
          showPaymentMethod={showPaymentMethod}
        />
        <TableBody>
          {Array.from({ length: 5 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <div className='space-y-1'>
                  <Skeleton className='h-4 w-24' />
                  <Skeleton className='h-3 w-16' />
                </div>
              </TableCell>
              <TableCell>
                <Skeleton className='h-4 w-20' />
              </TableCell>
              {showStoreInfo && (
                <TableCell>
                  <Skeleton className='h-4 w-24' />
                </TableCell>
              )}
              {showEmployeeInfo && (
                <TableCell>
                  <Skeleton className='h-4 w-20' />
                </TableCell>
              )}
              <TableCell>
                <Skeleton className='h-6 w-16' />
              </TableCell>
              {showPaymentMethod && (
                <TableCell>
                  <Skeleton className='h-6 w-20' />
                </TableCell>
              )}
              <TableCell>
                <Skeleton className='h-6 w-16' />
              </TableCell>
              <TableCell className='text-right'>
                <div className='space-y-1'>
                  <Skeleton className='h-4 w-20' />
                  <Skeleton className='h-3 w-16' />
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

// Table Empty Component
export function SaleNotSyncVatTableEmpty({
  showStoreInfo = true,
  showEmployeeInfo = true,
  showPaymentMethod = true,
}: {
  showStoreInfo?: boolean
  showEmployeeInfo?: boolean
  showPaymentMethod?: boolean
}) {
  const colSpan =
    4 +
    (showStoreInfo ? 1 : 0) +
    (showEmployeeInfo ? 1 : 0) +
    (showPaymentMethod ? 1 : 0)

  return (
    <div className='rounded-md border'>
      <Table>
        <SaleNotSyncVatTableHeader
          showStoreInfo={showStoreInfo}
          showEmployeeInfo={showEmployeeInfo}
          showPaymentMethod={showPaymentMethod}
        />
        <TableBody>
          <TableRow>
            <TableCell colSpan={colSpan} className='py-8 text-center'>
              <div className='text-muted-foreground'>
                Không có dữ liệu giao dịch
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  )
}

// Reusable Table Component
export function SaleNotSyncVatTable({
  data,
  showStoreInfo,
  showEmployeeInfo,
  showPaymentMethod,
}: {
  data: (SaleNotSyncVatData & { storeName: string })[]
  showStoreInfo: boolean
  showEmployeeInfo: boolean
  showPaymentMethod: boolean
}) {
  return (
    <div className='rounded-md border'>
      <Table>
        <SaleNotSyncVatTableHeader
          showStoreInfo={showStoreInfo}
          showEmployeeInfo={showEmployeeInfo}
          showPaymentMethod={showPaymentMethod}
        />
        <TableBody>
          {data.map((sale) => (
            <SaleNotSyncVatTableRow
              key={sale.id}
              sale={sale}
              showStoreInfo={showStoreInfo}
              showEmployeeInfo={showEmployeeInfo}
              showPaymentMethod={showPaymentMethod}
            />
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
