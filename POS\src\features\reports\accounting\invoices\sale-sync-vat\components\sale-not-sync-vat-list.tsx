import { useMemo } from 'react'
import { SaleNotSyncVatData } from '@/lib/sales-api'
import { cn } from '@/lib/utils'
import { formatDataForExcel } from '@/hooks/use-excel-export'
import { useSaleNotSyncVatData } from '@/hooks/use-sale-not-sync-vat-data'
import { Badge } from '@/components/ui/badge'
import { QuickExportButton } from '@/components/ui/export-button'
import { TablePagination } from '@/components/table-pagination'
import {
  SaleNotSyncVatTable,
  SaleNotSyncVatTableSkeleton,
  SaleNotSyncVatTableEmpty,
} from './sale-not-sync-vat-table'
import {
  formatCurrency,
  formatDate,
  type SaleNotSyncVatListProps,
} from './sale-not-sync-vat-utils'

export function SaleNotSyncVatList({
  dateRange,
  selectedStores = ['all-stores'],
  sourceId = 10000172,
  pageSize = 20,
  className,
  showStoreInfo = true,
  showEmployeeInfo = true,
  showPaymentMethod = true,
  showPagination = true,
  paymentMethodId = 'COD', // Default to COD
}: SaleNotSyncVatListProps) {
  const {
    data: storesSummary,
    totalPrice: _totalPrice,
    totalAmount,
    totalTransactions: _totalTransactions,
    isLoading,
    error,
  } = useSaleNotSyncVatData({
    dateRange,
    selectedStores,
    sourceId,
    autoFetch: true,
    paymentMethodId,
  })

  // Flatten all sales data from all stores and sort by date
  // (Filtering is now done in the hook)
  const allSalesData = useMemo(() => {
    const flatData: (SaleNotSyncVatData & { storeName: string })[] = []

    storesSummary.forEach((storeSummary) => {
      storeSummary.salesData.forEach((sale) => {
        flatData.push({ ...sale, storeName: storeSummary.storeName })
      })
    })

    return flatData.sort((a, b) => b.tran_date - a.tran_date)
  }, [storesSummary])

  // Format data for Excel export with formatted dates
  const formattedExcelData = useMemo(() => {
    return formatDataForExcel(allSalesData, {
      tran_date: (timestamp: number) => formatDate(timestamp),
    })
  }, [allSalesData])

  // Common props for table components
  const tableProps = { showStoreInfo, showEmployeeInfo, showPaymentMethod }

  if (error) {
    return (
      <div className={cn('text-center text-red-500', className)}>
        Lỗi khi tải dữ liệu: {error}
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Summary Header */}
      <div className='space-y-2'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <h3 className='text-lg font-semibold'>
              Giao Dịch Chưa Đồng Bộ VAT
            </h3>
            <Badge variant='outline' className='text-xs'>
              Tại chỗ
            </Badge>
            <Badge variant='secondary' className='text-xs'>
              COD
            </Badge>
            <Badge variant='secondary' className='text-xs'>
              Lọc: Không giảm giá hoặc ≤ 50%
            </Badge>
          </div>

          {/* Export Button */}
          <QuickExportButton
            type='sale-not-sync-vat'
            data={formattedExcelData}
            dateRange={dateRange}
            className='gap-2'
            disabled={isLoading || allSalesData.length === 0}
          />
        </div>
        {!isLoading && (
          <div className='text-muted-foreground space-y-1 text-sm'>
            <div>
              Tổng gốc: {allSalesData.length} giao dịch • Thành tiền:{' '}
              {formatCurrency(totalAmount)} VNĐ
            </div>
          </div>
        )}
      </div>

      {/* Table */}
      {isLoading ? (
        <SaleNotSyncVatTableSkeleton {...tableProps} />
      ) : allSalesData.length === 0 ? (
        <SaleNotSyncVatTableEmpty {...tableProps} />
      ) : (
        showPagination && (
          <>
            <TablePagination
              data={allSalesData}
              pageSize={pageSize}
              showPageSizeSelector
              showItemCount
              pageSizeOptions={[10, 20, 50, 100]}
            >
              {(paginatedData) => {
                return (
                  <div className='space-y-4'>
                    <SaleNotSyncVatTable data={paginatedData} {...tableProps} />
                  </div>
                )
              }}
            </TablePagination>
          </>
        )
      )}
    </div>
  )
}

export default SaleNotSyncVatList
