import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'
import { useCurrentBrand } from '@/stores/posStore'

import { getStores, type StoreListParams, type StoreListResponse } from '@/lib/stores-api'
import { convertApiStoreToStore, type Store } from '@/types/store'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseStoresWithPaginationOptions {
  params?: Partial<StoreListParams>
  enabled?: boolean
}

export const useStoresWithPagination = (options: UseStoresWithPaginationOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const dynamicParams: StoreListParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
    limit: 50,
    ...params
  }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  const storesQuery = useQuery({
    queryKey: [QUERY_KEYS.STORES_LIST, 'paginated', JSON.stringify(dynamicParams)],
    queryFn: async (): Promise<StoreListResponse> => {
      const response = await getStores(dynamicParams)
      return response
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000, 
    refetchInterval: 10 * 60 * 1000 
  })

  const nextPageParams: StoreListParams = {
    ...dynamicParams,
    page: (dynamicParams.page || 1) + 1
  }

  const nextPageQuery = useQuery({
    queryKey: [QUERY_KEYS.STORES_LIST, 'paginated', 'next', JSON.stringify(nextPageParams)],
    queryFn: async (): Promise<StoreListResponse> => {
      const response = await getStores(nextPageParams)
      return response
    },
    enabled: enabled && hasRequiredAuth && (storesQuery.data?.data ? storesQuery.data.data.length > 0 : false),
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000
  })

  const pageSize = dynamicParams.limit || 50
  const hasNextPage =
    (nextPageQuery.data?.data ? nextPageQuery.data.data.length > 0 : false) ||
    (storesQuery.data?.data ? storesQuery.data.data.length === pageSize : false)

  const transformedData = storesQuery.data?.data?.map(apiStore => {
    const convertedStore = convertApiStoreToStore(apiStore)
    return {
      ...convertedStore,
      fb_store_id: apiStore.fb_store_id,
      city_name: apiStore.city_name,
      expiry_date: apiStore.expiry_date,
      active: apiStore.active,
      latitude: apiStore.latitude,
      longitude: apiStore.longitude,
      address: apiStore.address
    } as Store & any
  }) || []

  return {
    data: transformedData,
    isLoading: storesQuery.isLoading,
    error: storesQuery.error,
    refetch: storesQuery.refetch,
    isFetching: storesQuery.isFetching,
    nextPageData: nextPageQuery.data?.data || [],
    hasNextPage,
    total: storesQuery.data?.total,
    currentPage: dynamicParams.page || 1
  }
}
