import { useMutation, useQueryClient } from '@tanstack/react-query'

import { useCurrentBrand } from '@/stores'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { QUERY_KEYS } from '@/constants/query-keys'

import { ItemsInStore } from '../data/schema'
import { itemsInStoreApiService } from './items-in-store-api'
import type {
  CreateItemInStoreRequest,
  UpdateItemInStoreRequest,
  UpdateItemStatusRequest,
  DeleteItemInStoreParams,
  DeleteMultipleItemsInStoreParams,
  CloneMenuRequest,
  BulkCreateItemInStoreRequest
} from './items-in-store-types'

export const useCreateItemInStore = () => {
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn: (data: CreateItemInStoreRequest) => itemsInStoreApiService.createItemInStore(data),
    onSuccess: () => {
      itemsInStoreApiService.clearCache()

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
      })
      toast.success('Tạo món thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi tạo món')
    }
  })

  return { createItemAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useUpdateItemInStore = () => {
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn: (data: UpdateItemInStoreRequest) => itemsInStoreApiService.updateItemInStore(data),
    onSuccess: () => {
      itemsInStoreApiService.clearCache()

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_DETAIL]
      })
      toast.success('Cập nhật món thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật món')
    }
  })

  return { updateItemAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useDeleteItemInStore = () => {
  const queryClient = useQueryClient()
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const mutation = useMutation({
    mutationFn: (id: string) => {
      const params: DeleteItemInStoreParams = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        id
      }
      return itemsInStoreApiService.deleteItemInStore(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
      })
      toast.success('Xóa món thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi xóa món')
    }
  })

  return { deleteItemAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useDeleteMultipleItemsInStore = () => {
  const queryClient = useQueryClient()
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const mutation = useMutation({
    mutationFn: (itemUids: string[]) => {
      const params: DeleteMultipleItemsInStoreParams = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        list_item_uid: itemUids
      }
      return itemsInStoreApiService.deleteMultipleItemsInStore(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      toast.success('Xóa món ăn thành công')
    },
    onError: (error: Error) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi xóa món ăn')
    }
  })

  return { deleteMultipleItemsAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useUpdateItemInStoreStatus = () => {
  const queryClient = useQueryClient()
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const mutation = useMutation({
    mutationFn: (data: { id: string; active: number }) => {
      const params: UpdateItemStatusRequest = {
        id: data.id,
        active: data.active,
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || ''
      }
      return itemsInStoreApiService.updateItemStatus(params)
    },
    onSuccess: () => {
      itemsInStoreApiService.clearCache()

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_DETAIL]
      })
      toast.success('Cập nhật trạng thái thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật trạng thái')
    }
  })

  return { updateStatusAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useDownloadItemsTemplate = () => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const mutation = useMutation({
    mutationFn: (params: Record<string, unknown>) => {
      const downloadParams = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        ...params
      }
      return itemsInStoreApiService.downloadTemplate(downloadParams)
    },
    onSuccess: (blob: Blob) => {
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `items-template-${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      toast.success('Tải template thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi tải template')
    }
  })

  return { downloadTemplateAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useImportItems = () => {
  const queryClient = useQueryClient()
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const mutation = useMutation({
    mutationFn: (items: unknown[]) => {
      const params = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        items
      }
      return itemsInStoreApiService.importItems(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
      })
      toast.success('Import món thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi import món')
    }
  })

  return { importItemsAsync: mutation.mutateAsync, isPending: mutation.isPending }
}

export const useBulkUpdateItemsInStore = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (items: ItemsInStore[]) => itemsInStoreApiService.bulkUpdateItemsInStore(items),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_FOR_TABLE] })
      toast.success('Cập nhật thứ tự món ăn thành công')
    },
    onError: (error: Error) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật thứ tự món ăn')
    }
  })
}

export const useBulkCreateItemsInStore = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: BulkCreateItemInStoreRequest[]) => {
      return itemsInStoreApiService.bulkCreateItemsInStore(data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST],
        refetchType: 'none'
      })

      setTimeout(() => {
        queryClient.refetchQueries({
          queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
        })
      }, 100)

      toast.success('Tạo món ăn thành công!')
    },
    onError: (error: Error) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi tạo món ăn')
    }
  })

  return { bulkCreateItemsInStore: mutate, isBulkCreating: isPending }
}

export const useCloneMenu = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CloneMenuRequest) => itemsInStoreApiService.cloneMenu(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_FOR_TABLE] })
      toast.success('Sao chép thực đơn thành công')
    },
    onError: (error: Error) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi sao chép thực đơn')
    }
  })
}
