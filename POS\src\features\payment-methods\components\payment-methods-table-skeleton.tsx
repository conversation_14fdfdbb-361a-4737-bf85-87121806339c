import { Skeleton } from '@/components/ui/skeleton'
import { TableBody, TableCell, TableRow } from '@/components/ui/table'
import { PaymentMethodsTableHeader } from './payment-methods-table-header'

export function PaymentMethodsTableSkeleton() {
  return (
    <>
      <PaymentMethodsTableHeader />
      <TableBody>
        {Array.from({ length: 5 }).map((_, index) => (
          <TableRow key={index}>
            <TableCell>
              <Skeleton className='h-4 w-8' />
            </TableCell>
            <TableCell>
              <Skeleton className='h-4 w-20' />
            </TableCell>
            <TableCell>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-32' />
                <Skeleton className='h-3 w-24' />
              </div>
            </TableCell>
            <TableCell>
              <Skeleton className='h-4 w-12 mx-auto' />
            </TableCell>
            <TableCell>
              <Skeleton className='h-4 w-24' />
            </TableCell>
            <TableCell>
              <Skeleton className='h-8 w-8 ml-auto' />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </>
  )
}

export function PaymentMethodsTableEmpty({
  searchQuery,
  storeFilter,
}: {
  searchQuery?: string
  storeFilter?: string
}) {
  const hasFilters = searchQuery || (storeFilter && storeFilter !== 'all')

  return (
    <div className='flex flex-col items-center justify-center py-12 text-center'>
      <div className='text-muted-foreground mb-4'>
        <svg
          className='mx-auto h-12 w-12'
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={1}
            d='M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z'
          />
        </svg>
      </div>
      <h3 className='text-lg font-medium mb-2'>
        {hasFilters ? 'Không tìm thấy phương thức thanh toán' : 'Chưa có phương thức thanh toán'}
      </h3>
      <p className='text-muted-foreground text-sm max-w-sm'>
        {hasFilters
          ? 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm để xem kết quả khác.'
          : 'Bắt đầu bằng cách tạo phương thức thanh toán đầu tiên cho thương hiệu của bạn.'}
      </p>
    </div>
  )
}
