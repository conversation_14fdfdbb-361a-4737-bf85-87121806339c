import { useState } from 'react'
import { Customization } from '@/types/customizations'

export function useCustomizationModals() {
  const [selectedCustomization, setSelectedCustomization] = useState<Customization | null>(null)
  const [isCopyModalOpen, setIsCopyModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [isExportModalOpen, setIsExportModalOpen] = useState(false)
  const [isImportModalOpen, setIsImportModalOpen] = useState(false)

  const openCopyModal = (customization: Customization) => {
    setSelectedCustomization(customization)
    setIsCopyModalOpen(true)
  }

  const closeCopyModal = () => {
    setIsCopyModalOpen(false)
    setSelectedCustomization(null)
  }

  const openDeleteModal = (customization: Customization) => {
    setSelectedCustomization(customization)
    setIsDeleteModalOpen(true)
  }

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false)
    setSelectedCustomization(null)
  }

  const openExportModal = () => {
    setIsExportModalOpen(true)
  }

  const closeExportModal = () => {
    setIsExportModalOpen(false)
  }

  const openImportModal = () => {
    setIsImportModalOpen(true)
  }

  const closeImportModal = () => {
    setIsImportModalOpen(false)
  }

  return {
    selectedCustomization,
    isCopyModalOpen,
    isDeleteModalOpen,
    isExportModalOpen,
    isImportModalOpen,
    openCopyModal,
    closeCopyModal,
    openDeleteModal,
    closeDeleteModal,
    openExportModal,
    closeExportModal,
    openImportModal,
    closeImportModal,
  }
}
