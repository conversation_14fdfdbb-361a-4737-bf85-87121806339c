import { ChevronDown, ChevronRight } from 'lucide-react'

import { PosModal } from '@/components/pos'
import { Checkbox, Collapsible, CollapsibleContent, CollapsibleTrigger, Input } from '@/components/ui'

import { useFormContext } from '../form-context'

interface MenuItem {
  id: string
  item_name: string
  ots_price: number
  active?: number
}

interface AddMenuItemModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel: () => void
  onConfirm: () => void
  selectedMenuItemsList: MenuItem[]
  remainingMenuItemsList: MenuItem[]
}

export function AddMenuItemModal({
  open,
  onOpenChange,
  onCancel,
  onConfirm,
  selectedMenuItemsList,
  remainingMenuItemsList
}: AddMenuItemModalProps) {
  const { menuItemSelection } = useFormContext()

  const {
    menuItemSearchTerm,
    setMenuItemSearchTerm,
    selectedMenuSectionOpen,
    setSelectedMenuSectionOpen,
    remainingMenuSectionOpen,
    setRemainingMenuSectionOpen,
    selectedMenuItems,
    handleMenuItemToggle
  } = menuItemSelection
  return (
    <PosModal
      title='Chọn món để thêm vào nhóm'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm}
      confirmText='Xác nhận'
      cancelText='Hủy'
      maxWidth='sm:max-w-2xl'
    >
      <div className='space-y-4'>
        <Input
          placeholder='Tìm kiếm món'
          value={menuItemSearchTerm}
          onChange={e => setMenuItemSearchTerm(e.target.value)}
          className='w-full'
        />

        <Collapsible open={selectedMenuSectionOpen} onOpenChange={setSelectedMenuSectionOpen}>
          <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
            <span className='font-medium'>Đã chọn ({selectedMenuItemsList.length})</span>
            {selectedMenuSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
          </CollapsibleTrigger>
          <CollapsibleContent className='mt-2'>
            <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
              {selectedMenuItemsList.length === 0 && <p className='text-sm text-gray-500'>Chưa có món nào được chọn</p>}
              {selectedMenuItemsList.length > 0 &&
                selectedMenuItemsList.map(item => {
                  const isActive = item.active === 1
                  return (
                    <label
                      key={item.id}
                      className={`flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50 ${
                        !isActive ? 'bg-gray-100 opacity-50' : ''
                      }`}
                    >
                      <Checkbox
                        checked={selectedMenuItems.has(item.id)}
                        onCheckedChange={() => handleMenuItemToggle(item.id)}
                      />
                      <div className='flex-1'>
                        <p className={`text-sm font-medium ${isActive ? '' : 'text-gray-400'}`}>{item.item_name}</p>
                        <p className={`text-xs ${isActive ? 'text-gray-500' : 'text-gray-400'}`}>
                          {item.ots_price.toLocaleString('vi-VN')} đ
                        </p>
                      </div>
                    </label>
                  )
                })}
            </div>
          </CollapsibleContent>
        </Collapsible>

        <Collapsible open={remainingMenuSectionOpen} onOpenChange={setRemainingMenuSectionOpen}>
          <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
            <span className='font-medium'>
              Còn lại ({remainingMenuItemsList.filter(item => item.active !== 0).length})
            </span>
            {remainingMenuSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
          </CollapsibleTrigger>
          <CollapsibleContent className='mt-2'>
            <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
              {remainingMenuItemsList.filter(item => item.active !== 0).length === 0 && (
                <p className='text-sm text-gray-500'>Không có món nào</p>
              )}
              {remainingMenuItemsList.filter(item => item.active !== 0).length > 0 &&
                remainingMenuItemsList
                  .filter(item => item.active !== 0)
                  .map(item => (
                    <label
                      key={item.id}
                      className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                    >
                      <Checkbox
                        checked={selectedMenuItems.has(item.id)}
                        onCheckedChange={() => handleMenuItemToggle(item.id)}
                      />
                      <div className='flex-1'>
                        <p className='text-sm font-medium'>{item.item_name}</p>
                        <p className='text-xs text-gray-500'>{item.ots_price.toLocaleString('vi-VN')} đ</p>
                      </div>
                    </label>
                  ))}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </PosModal>
  )
}
